import { test, expect } from '@playwright/test';

test.describe('Profile Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock connected accounts API
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: [
            {
              id: 'yt-1',
              platform: 'youtube',
              handle: '@testchannel',
              avatar: 'https://example.com/avatar1.jpg',
              lastSync: '2024-01-01T12:00:00Z',
              metrics: {
                followers: 15420,
                engagement: 4.2,
                reach: 50000,
                growth: {
                  followers: 7.8,
                  engagement: 2.1,
                },
              },
            },
            {
              id: 'ig-1',
              platform: 'instagram',
              handle: '@testaccount',
              avatar: 'https://example.com/avatar2.jpg',
              lastSync: '2024-01-01T11:30:00Z',
              metrics: {
                followers: 8750,
                engagement: 3.1,
                reach: 25000,
                growth: {
                  followers: 5.2,
                  engagement: 1.5,
                },
              },
            }
          ]
        })
      });
    });

    // Mock platform metrics API
    await page.route('**/api/metrics**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: {
            totalFollowers: 24170,
            avgEngagement: 3.65,
            avgGrowth: 6.5
          }
        })
      });
    });

    await page.goto('/profile');
  });

  test('should display analytics dashboard with connected accounts', async ({ page }) => {
    // Check dashboard header
    await expect(page.getByRole('heading', { name: /analytics dashboard/i })).toBeVisible();
    await expect(page.getByText(/overview of your social media performance/i)).toBeVisible();

    // Check for platform performance section
    await expect(page.getByRole('heading', { name: /platform performance/i })).toBeVisible();
  });

  test('should show correct total followers calculation', async ({ page }) => {
    // Should calculate total from YouTube (15,420) + Instagram (8,750) = 24,170
    await expect(page.getByText('Total Followers')).toBeVisible();
    await expect(page.getByText('24,170')).toBeVisible();
  });

  test('should display average engagement correctly', async ({ page }) => {
    // Should show average engagement
    await expect(page.getByText('Avg. Engagement')).toBeVisible();
    await expect(page.getByText(/3\.\d%/)).toBeVisible(); // Around 3.65%
  });

  test('should show connected platforms count', async ({ page }) => {
    await expect(page.getByText('Connected Platforms')).toBeVisible();
    await expect(page.getByText('2')).toBeVisible();
  });

  test('should display platform tiles for each connected account', async ({ page }) => {
    // Check for YouTube tile
    await expect(page.getByText('@testchannel')).toBeVisible();
    await expect(page.getByText('youtube')).toBeVisible();
    await expect(page.getByText('15.4K')).toBeVisible(); // 15,420 formatted

    // Check for Instagram tile
    await expect(page.getByText('@testaccount')).toBeVisible();
    await expect(page.getByText('instagram')).toBeVisible();
    await expect(page.getByText('8.8K')).toBeVisible(); // 8,750 formatted
  });

  test('should allow timeframe selection', async ({ page }) => {
    const timeframeSelect = page.locator('select');
    
    // Default should be 30d
    await expect(timeframeSelect).toHaveValue('30d');
    
    // Change to 7d
    await timeframeSelect.selectOption('7d');
    await expect(timeframeSelect).toHaveValue('7d');
    
    // Change to 90d
    await timeframeSelect.selectOption('90d');
    await expect(timeframeSelect).toHaveValue('90d');
  });

  test('should handle refresh functionality', async ({ page }) => {
    let apiCallCount = 0;
    
    // Track API calls
    await page.route('**/api/accounts', async (route) => {
      apiCallCount++;
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: []
        })
      });
    });

    // Click refresh button
    const refreshButton = page.getByRole('button', { name: /refresh/i });
    await refreshButton.click();
    
    // API should be called again
    expect(apiCallCount).toBeGreaterThan(0);
  });

  test('should show growth indicators with correct styling', async ({ page }) => {
    // Positive growth should show green indicators
    const growthTexts = page.locator('text=/\\+.*%/');
    await expect(growthTexts.first()).toBeVisible();
    
    // Check for trending up icons (specific implementation may vary)
    await expect(page.locator('[data-testid="trending-up"]').or(page.locator('svg')).first()).toBeVisible();
  });

  test('should display engagement rate progress bars', async ({ page }) => {
    // Check for engagement rate sections
    await expect(page.getByText('Engagement Rate')).toBeVisible();
    
    // Should show percentage values
    await expect(page.getByText('4.2%')).toBeVisible(); // YouTube
    await expect(page.getByText('3.1%')).toBeVisible(); // Instagram
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Dashboard should still be accessible
    await expect(page.getByRole('heading', { name: /analytics dashboard/i })).toBeVisible();
    
    // Platform tiles should stack vertically on mobile
    const tiles = page.locator('[data-testid^="platform-tile"]');
    if (await tiles.count() > 0) {
      await expect(tiles.first()).toBeVisible();
    }
  });

  test('should navigate to connections page from empty state', async ({ page }) => {
    // Mock empty accounts response
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: []
        })
      });
    });

    await page.reload();

    // Should show empty state
    await expect(page.getByText('No Connected Accounts')).toBeVisible();
    await expect(page.getByText(/Connect your social media accounts/)).toBeVisible();

    // Click connect button
    const connectButton = page.getByRole('button', { name: /connect your first account/i });
    await connectButton.click();

    // Should navigate to connections page
    await page.waitForURL('**/connections');
  });

  test('should handle loading state', async ({ page }) => {
    // Mock slow API response
    await page.route('**/api/accounts', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ status: 'success', data: [] })
      });
    });

    await page.reload();

    // Should show loading skeletons
    const skeletons = page.locator('.animate-pulse');
    await expect(skeletons.first()).toBeVisible();
  });

  test('should handle API error gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' })
      });
    });

    await page.reload();

    // Should handle error gracefully (implementation-dependent)
    // At minimum, page should not crash
    await expect(page.locator('body')).toBeVisible();
  });
});

test.describe('Connection Modal E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock APIs
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ status: 'success', data: [] })
      });
    });

    await page.goto('/connections');
  });

  test('should open connection modal', async ({ page }) => {
    // Look for connect button or modal trigger
    const connectButton = page.getByRole('button', { name: /connect/i }).first();
    if (await connectButton.isVisible()) {
      await connectButton.click();
      
      // Modal should open
      await expect(page.getByText('Connect Your Accounts')).toBeVisible();
    }
  });

  test('should display available platforms', async ({ page }) => {
    // Open modal if not already open
    const connectButton = page.getByRole('button', { name: /connect/i }).first();
    if (await connectButton.isVisible()) {
      await connectButton.click();
    }

    // Check for platform options
    await expect(page.getByText('YouTube')).toBeVisible();
    await expect(page.getByText('Instagram')).toBeVisible();
    
    // Twitter should show as coming soon
    await expect(page.getByText('X (Twitter)')).toBeVisible();
    await expect(page.getByText('Coming Soon')).toBeVisible();
  });

  test('should handle platform connection simulation', async ({ page }) => {
    // Mock OAuth endpoint
    await page.route('**/api/auth/connect/youtube', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          auth_url: 'https://accounts.google.com/oauth/authorize?...'
        })
      });
    });

    // Open modal
    const connectButton = page.getByRole('button', { name: /connect/i }).first();
    if (await connectButton.isVisible()) {
      await connectButton.click();
    }

    // Click YouTube connect
    const youtubeConnect = page.getByRole('button', { name: 'Connect' }).first();
    await youtubeConnect.click();

    // Should show connecting state
    await expect(page.getByText('Connecting...')).toBeVisible();
  });

  test('should close modal when close button clicked', async ({ page }) => {
    // Open modal
    const connectButton = page.getByRole('button', { name: /connect/i }).first();
    if (await connectButton.isVisible()) {
      await connectButton.click();
    }

    // Close modal
    const closeButton = page.getByRole('button', { name: /close/i }).or(page.locator('button').filter({ hasText: '✕' }));
    if (await closeButton.isVisible()) {
      await closeButton.click();
      
      // Modal should close
      await expect(page.getByText('Connect Your Accounts')).not.toBeVisible();
    }
  });

  test('should show security notice', async ({ page }) => {
    // Open modal
    const connectButton = page.getByRole('button', { name: /connect/i }).first();
    if (await connectButton.isVisible()) {
      await connectButton.click();
    }

    // Check for security notice
    await expect(page.getByText(/We use secure OAuth 2.0 authentication/)).toBeVisible();
    await expect(page.getByText(/Your account credentials are never stored/)).toBeVisible();
  });

  test('should handle keyboard navigation in modal', async ({ page }) => {
    // Open modal
    const connectButton = page.getByRole('button', { name: /connect/i }).first();
    if (await connectButton.isVisible()) {
      await connectButton.click();
    }

    // Should be able to tab through interactive elements
    await page.keyboard.press('Tab');
    
    // Should focus on interactive elements
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();
  });
});