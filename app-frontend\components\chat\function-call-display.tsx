"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  ChevronDown, 
  ChevronRight, 
  Zap, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  ExternalLink,
  Copy,
  Eye
} from 'lucide-react';
import { FunctionCall, FunctionResponse, ToolExecution } from '@/types/adk';
import { ToolExecutionDetail } from './tool-execution-detail';
import { cn } from '@/lib/utils';

export interface FunctionCallDisplayProps {
  functionCalls: Record<string, any>[];
  functionResponses?: Record<string, any>[];
  toolExecutions?: ToolExecution[];
  expanded?: boolean;
  onToggleExpanded?: () => void;
  showDetailModal?: boolean;
  onCopy?: (content: string, type: string) => void;
  className?: string;
}

export function FunctionCallDisplay({
  functionCalls,
  functionResponses = [],
  toolExecutions = [],
  expanded = false,
  onToggleExpanded,
  showDetailModal = true,
  onCopy,
  className
}: FunctionCallDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(expanded);
  const [selectedExecution, setSelectedExecution] = useState<ToolExecution | null>(null);
  const [showDetailDialog, setShowDetailDialog] = useState(false);

  const handleToggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    onToggleExpanded?.();
  };

  const handleCopy = async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content);
      onCopy?.(content, type);
    } catch (error) {
      console.error('Failed to copy content:', error);
    }
  };

  const getExecutionStatus = (callName: string, callId?: string) => {
    const execution = toolExecutions.find(
      exec => exec.name === callName || exec.id === callId
    );
    return execution?.status || 'unknown';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'failed':
        return <XCircle className="w-3 h-3 text-red-500" />;
      case 'running':
        return <Clock className="w-3 h-3 text-blue-500 animate-pulse" />;
      case 'pending':
        return <Clock className="w-3 h-3 text-yellow-500" />;
      default:
        return <AlertTriangle className="w-3 h-3 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      case 'running':
        return 'text-blue-600 dark:text-blue-400';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const findFunctionResponse = (callName: string, callId?: string) => {
    return functionResponses.find(
      response => response.name === callName || response.id === callId
    );
  };

  const findToolExecution = (callName: string, callId?: string) => {
    return toolExecutions.find(
      exec => exec.name === callName || exec.id === callId
    );
  };

  const handleShowDetails = (callName: string, callId?: string) => {
    const execution = findToolExecution(callName, callId);
    if (execution && showDetailModal) {
      setSelectedExecution(execution);
      setShowDetailDialog(true);
    }
  };

  if (!functionCalls || functionCalls.length === 0) {
    return null;
  }

  return (
    <>
      <div className={cn("mt-3 pt-3 border-t border-border/50", className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggleExpanded}
          className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
        >
          {isExpanded ? (
            <ChevronDown className="w-3 h-3 mr-1" />
          ) : (
            <ChevronRight className="w-3 h-3 mr-1" />
          )}
          <Zap className="w-3 h-3 mr-1" />
          {functionCalls.length} tool{functionCalls.length !== 1 ? 's' : ''} used
        </Button>

        {isExpanded && (
          <div className="mt-2 space-y-2">
            {functionCalls.map((call, index) => {
              const callName = call.name || `Function ${index + 1}`;
              const callId = call.id;
              const status = getExecutionStatus(callName, callId);
              const response = findFunctionResponse(callName, callId);
              const execution = findToolExecution(callName, callId);

              return (
                <Card key={index} className="bg-muted/30">
                  <CardContent className="p-3">
                    <div className="space-y-2">
                      {/* Function Call Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(status)}
                          <span className="font-medium text-sm">{callName}</span>
                          <Badge 
                            variant="outline" 
                            className={cn("h-4 px-1 text-xs", getStatusColor(status))}
                          >
                            {status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopy(JSON.stringify(call, null, 2), 'function_call')}
                            className="h-5 w-5 p-0 opacity-60 hover:opacity-100"
                            title="Copy function call"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          {execution && showDetailModal && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleShowDetails(callName, callId)}
                              className="h-5 w-5 p-0 opacity-60 hover:opacity-100"
                              title="View details"
                            >
                              <Eye className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Function Arguments */}
                      {call.arguments && Object.keys(call.arguments).length > 0 && (
                        <div className="text-xs">
                          <div className="text-muted-foreground mb-1">Arguments:</div>
                          <div className="bg-background/50 rounded p-2 font-mono overflow-x-auto">
                            {JSON.stringify(call.arguments, null, 2)}
                          </div>
                        </div>
                      )}

                      {/* Function Response */}
                      {response && (
                        <div className="text-xs">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-muted-foreground">Response:</span>
                            <Badge 
                              variant={response.success ? "default" : "destructive"}
                              className="h-4 px-1 text-xs"
                            >
                              {response.success ? 'Success' : 'Failed'}
                            </Badge>
                          </div>
                          <div className="bg-background/50 rounded p-2 max-h-20 overflow-y-auto">
                            {response.error ? (
                              <span className="text-red-600 dark:text-red-400">
                                Error: {response.error}
                              </span>
                            ) : (
                              <span>{response.content || 'No content'}</span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Execution Duration */}
                      {execution?.duration_ms && (
                        <div className="text-xs text-muted-foreground">
                          Duration: {execution.duration_ms < 1000 
                            ? `${execution.duration_ms}ms` 
                            : `${(execution.duration_ms / 1000).toFixed(1)}s`
                          }
                        </div>
                      )}

                      {/* Error Display */}
                      {execution?.error && (
                        <div className="text-xs p-2 bg-red-50 dark:bg-red-950 rounded border border-red-200 dark:border-red-800">
                          <div className="flex items-center gap-1 text-red-800 dark:text-red-200 mb-1">
                            <AlertTriangle className="w-3 h-3" />
                            <span className="font-medium">Error</span>
                          </div>
                          <div className="text-red-700 dark:text-red-300">
                            {execution.error}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Tool Execution Detail Modal */}
      {selectedExecution && showDetailDialog && (
        <ToolExecutionDetail
          execution={selectedExecution}
          functionCall={functionCalls.find(call => 
            call.name === selectedExecution.name || call.id === selectedExecution.id
          )}
          functionResponse={functionResponses.find(response => 
            response.name === selectedExecution.name || response.id === selectedExecution.id
          )}
          isOpen={showDetailDialog}
          onClose={() => {
            setShowDetailDialog(false);
            setSelectedExecution(null);
          }}
          onCopy={handleCopy}
          onExport={(execution) => {
            // Export functionality - could download as JSON
            const dataStr = JSON.stringify(execution, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `tool-execution-${execution.id}.json`;
            link.click();
            URL.revokeObjectURL(url);
          }}
        />
      )}
    </>
  );
}