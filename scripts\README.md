# Sample Data Scripts

This directory contains scripts for generating and seeding sample data for the Social Media Manager application. These scripts are essential for development, testing, and demo purposes.

## Overview

The sample data generation process consists of two main scripts:

1. **`generate_sample_data.py`** - Generates realistic sample data in JSON format
2. **`seed_database.py`** - Loads the sample data into Firestore database

## Scripts Description

### 1. Generate Sample Data (`generate_sample_data.py`)

**Purpose**: Creates comprehensive, realistic sample data for all aspects of the social media manager application.

**Features**:
- ✅ **3 Sample Users** with complete profiles and preferences
- ✅ **Connected Social Accounts** for YouTube, Instagram, and Twitter
- ✅ **Realistic Metrics** with platform-specific data
- ✅ **Content Plans** with scheduled posts and AI-generated suggestions
- ✅ **Chat History** with user-AI conversations
- ✅ **Analytics Data** with 30 days of historical metrics
- ✅ **Cross-platform Integration** showing multi-platform strategies

**Generated Data Structure**:
```json
{
  "users": [...],
  "connected_accounts": [...],
  "content_plans": [...], 
  "chat_messages": [...],
  "analytics_data": {...},
  "metadata": {...}
}
```

**Usage**:
```bash
cd scripts
python generate_sample_data.py
```

**Output**: Creates `sample_data.json` with all generated data (~50+ KB of realistic sample data)

### 2. Database Seeder (`seed_database.py`)

**Purpose**: Loads the generated sample data into Firestore for backend development and testing.

**Features**:
- ✅ **Batch Processing** for efficient Firestore writes
- ✅ **Data Validation** and type conversion
- ✅ **Index Recommendations** for optimal query performance  
- ✅ **Clear Existing Data** option for fresh starts
- ✅ **Progress Logging** with detailed feedback
- ✅ **Error Handling** with rollback capabilities

**Usage**:
```bash
# Basic seeding (requires sample_data.json)
python seed_database.py

# Seed with specific data file
python seed_database.py --data-file /path/to/custom_data.json

# Clear existing data and seed fresh
python seed_database.py --clear

# Specify Google Cloud project
python seed_database.py --project-id your-project-id

# Environment-specific seeding
python seed_database.py --env dev
```

## Sample Data Details

### Users (3 Profiles)

**Alex Chen** - Tech Entrepreneur
- Industry: Technology
- Platforms: YouTube (@TechWithAlex), Instagram, Twitter
- Content Focus: Startup insights, innovation, tech tutorials
- Subscription: Pro Plan

**Maria Rodriguez** - Digital Marketing Strategist  
- Industry: Marketing
- Platforms: Instagram (@digitalmarketingpro), Twitter, YouTube
- Content Focus: Marketing strategies, business growth
- Subscription: Enterprise Plan

**David Johnson** - Fitness Coach
- Industry: Health & Fitness
- Platforms: Instagram (@fitnessjourney), YouTube, Twitter
- Content Focus: Wellness, healthy lifestyle, fitness tips
- Subscription: Free Plan

### Connected Accounts (6-9 total)

Each user has 2-3 connected platforms with realistic metrics:

**YouTube Accounts**:
- Subscribers: 5,000 - 50,000
- Engagement Rate: 3.5% - 8.2%
- Content: Educational videos, tutorials, vlogs
- Metrics: Views, watch time, subscriber growth

**Instagram Accounts**:
- Followers: 2,000 - 25,000  
- Engagement Rate: 2.8% - 6.5%
- Content: Posts, stories, reels, carousels
- Metrics: Likes, comments, saves, reach

**Twitter Accounts**:
- Followers: 1,000 - 15,000
- Engagement Rate: 1.5% - 4.2%
- Content: Tweets, threads, retweets
- Metrics: Impressions, retweets, link clicks

### Content Plans (6-12 plans)

**Plan Types**:
- 7-Day Growth Strategy
- 14-Day Content Calendar  
- 30-Day Brand Awareness Campaign
- Product Launch Plans
- Engagement Boost Strategies

**Each Plan Includes**:
- Multi-platform posting schedule
- Content type recommendations
- Optimal posting times
- Performance predictions
- AI-generated suggestions
- Goal tracking metrics

### Sample Posts (200+ planned posts)

**Content Categories**:
- Educational tutorials and how-to guides
- Behind-the-scenes content
- Product showcases and reviews
- Industry insights and trends
- Personal stories and inspiration
- Live sessions and Q&A content

**Platform-Specific Content**:
- **YouTube**: Tutorials, reviews, vlogs (weekly frequency)
- **Instagram**: Posts, stories, reels (daily frequency)  
- **Twitter**: Tweets, threads (multiple daily)

### Chat History (30+ conversations)

**Sample User Queries**:
- "How is my YouTube performance this month?"
- "Can you analyze my Instagram engagement rate?"
- "Create a content plan for the next 14 days"
- "What are the best times to post on Instagram?"
- "Help me plan content for product launch"

**AI Assistant Responses**:
- Detailed analytics explanations
- Actionable content recommendations
- Performance insights and trends
- Strategic planning suggestions
- Cross-platform optimization tips

### Analytics Data (30 days historical)

**Daily Metrics Per Account**:
- Follower growth/decline
- Engagement rates
- Post performance
- Reach and impressions
- Platform-specific metrics

**Aggregated Insights**:
- Growth trends over time
- Best performing content
- Optimal posting times
- Engagement patterns
- Cross-platform comparisons

## Prerequisites

### Environment Setup

1. **Python Dependencies**:
```bash
pip install google-cloud-firestore
```

2. **Google Cloud Setup**:
```bash
# Set up authentication
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"

# Or use gcloud auth
gcloud auth application-default login
```

3. **Firestore Database**:
- Create a Firestore database in your Google Cloud project
- Set up security rules for development access

### Required Files

1. **Service Account Key** (for production) or **Application Default Credentials** (for development)
2. **Generated Sample Data** (`sample_data.json`)

## Usage Workflows

### 1. Development Setup

```bash
# Step 1: Generate sample data
cd scripts
python generate_sample_data.py

# Step 2: Seed development database  
python seed_database.py --env dev --clear

# Step 3: Start backend server
cd ../app-agents
uvicorn app.main:app --reload

# Step 4: Start frontend
cd ../app-frontend  
npm run dev
```

### 2. Testing Environment

```bash
# Fresh test data
python generate_sample_data.py
python seed_database.py --env staging --clear

# Run tests against seeded data
cd ../app-agents
pytest tests/

cd ../app-frontend
npm test
```

### 3. Demo Preparation

```bash
# Generate rich demo data
python generate_sample_data.py
python seed_database.py --clear

# Verify data in Firestore Console
# Test key user journeys
# Prepare demo scenarios
```

## Database Collections

The seeded database creates the following Firestore collections:

### Core Collections

1. **`users`** - User profiles and preferences
2. **`connected_accounts`** - Social media account connections  
3. **`content_plans`** - Content planning and scheduling
4. **`chat_messages`** - AI chat conversation history
5. **`analytics_data`** - Historical performance metrics

### Subcollections

1. **`content_plans/{planId}/posts`** - Individual planned posts
2. **`users/{userId}/sessions`** - User session data (if implemented)

### Recommended Indexes

The seeder provides recommendations for Firestore indexes:

```
users: email (ascending)
connected_accounts: user_id (ascending), platform (ascending)  
content_plans: user_id (ascending), status (ascending)
chat_messages: user_id (ascending), timestamp (descending)
analytics_data: account_id (ascending)
```

## Data Validation

### Quality Checks

The generated data includes:

- ✅ **Realistic Metrics** based on industry averages
- ✅ **Temporal Consistency** with proper date relationships
- ✅ **Cross-Reference Integrity** between related records
- ✅ **Platform-Specific Logic** for each social network
- ✅ **Diverse Content Types** across different industries
- ✅ **Authentic User Scenarios** for comprehensive testing

### Data Relationships

```
Users (1) → Connected Accounts (N)
Users (1) → Content Plans (N) 
Content Plans (1) → Posts (N)
Users (1) → Chat Messages (N)
Connected Accounts (1) → Analytics Data (1)
```

## Troubleshooting

### Common Issues

1. **Permission Denied**:
   - Check Google Cloud authentication
   - Verify Firestore IAM permissions
   - Ensure service account has Firestore Admin role

2. **Import Errors**:
   - Install required dependencies: `pip install google-cloud-firestore`
   - Check Python path and module imports

3. **Data Validation Errors**:
   - Verify JSON format in sample_data.json
   - Check date format consistency (ISO 8601)
   - Ensure required fields are present

4. **Firestore Quota Exceeded**:
   - Reduce batch size in seeder
   - Implement delays between batch writes
   - Use smaller sample dataset

### Debug Mode

```bash
# Enable verbose logging
python seed_database.py --clear --env dev --verbose

# Test with smaller dataset
python generate_sample_data.py --users 1 --accounts 2
```

## Performance Considerations

### Optimization Tips

1. **Batch Operations**: Seeder uses Firestore batch writes (max 500 operations)
2. **Parallel Processing**: Consider parallel collection seeding for large datasets
3. **Index Creation**: Create recommended indexes before large data loads
4. **Memory Usage**: Monitor memory for very large datasets

### Scaling for Production

```bash
# For production-scale data generation
python generate_sample_data.py --users 100 --scale-factor 10
python seed_database.py --batch-size 500 --parallel-workers 4
```

## Security Notes

### Development vs Production

- ✅ **Development**: Use generated OAuth tokens and test credentials
- ❌ **Production**: Never seed production databases with sample data
- ✅ **Staging**: Safe for testing with proper data isolation
- ⚠️ **Security**: Sample data contains no real user information

### Data Privacy

- All generated user data is fictional
- No real social media accounts or tokens
- Safe for development and demo purposes
- Compliant with data privacy requirements

## Contributing

### Adding New Sample Data

1. **Extend Generator Classes**: Add new data types in `generate_sample_data.py`
2. **Update Seeder**: Add corresponding seeding logic in `seed_database.py`  
3. **Test Thoroughly**: Verify data integrity and relationships
4. **Document Changes**: Update this README with new data types

### Improving Data Quality

1. **Industry-Specific Content**: Add domain expertise for different verticals
2. **Temporal Patterns**: Improve realistic posting schedules and seasonality
3. **Engagement Models**: Enhance engagement rate calculations
4. **Platform Updates**: Keep pace with social media platform changes

---

This comprehensive sample data infrastructure ensures robust development and testing capabilities for the Social Media Manager application, providing realistic scenarios for all user workflows and system components.