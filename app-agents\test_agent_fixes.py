#!/usr/bin/env python3
"""
Test script to verify all agent fixes are working correctly.
Tests parameter type compatibility with Google ADK.
"""

import sys
import traceback
from pathlib import Path

# Add the agents directory to Python path
agents_dir = Path(__file__).parent / "agents"
sys.path.insert(0, str(agents_dir))

def test_agent_loading():
    """Test loading all agents to verify ADK compatibility."""
    
    test_results = {}
    
    # Test Instagram Analyzer Agent
    try:
        from instagram_analyzer.agent import root_agent as instagram_agent
        test_results['instagram_analyzer'] = {
            'status': 'success',
            'agent_name': instagram_agent.name,
            'model': instagram_agent.model,
            'tool_count': len(instagram_agent.tools)
        }
        print(f"✅ Instagram Analyzer Agent loaded successfully")
        print(f"   - Name: {instagram_agent.name}")
        print(f"   - Model: {instagram_agent.model}")
        print(f"   - Tools: {len(instagram_agent.tools)}")
        
    except Exception as e:
        test_results['instagram_analyzer'] = {
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        print(f"❌ Instagram Analyzer Agent failed to load: {e}")
    
    # Test YouTube Analyzer Agent
    try:
        from youtube_analyzer.agent import root_agent as youtube_agent
        test_results['youtube_analyzer'] = {
            'status': 'success',
            'agent_name': youtube_agent.name,
            'model': youtube_agent.model,
            'tool_count': len(youtube_agent.tools)
        }
        print(f"✅ YouTube Analyzer Agent loaded successfully")
        print(f"   - Name: {youtube_agent.name}")
        print(f"   - Model: {youtube_agent.model}")
        print(f"   - Tools: {len(youtube_agent.tools)}")
        
    except Exception as e:
        test_results['youtube_analyzer'] = {
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        print(f"❌ YouTube Analyzer Agent failed to load: {e}")
    
    # Test Research Agent
    try:
        from research_agent.agent import root_agent as research_agent
        test_results['research_agent'] = {
            'status': 'success',
            'agent_name': research_agent.name,
            'model': research_agent.model,
            'tool_count': len(research_agent.tools)
        }
        print(f"✅ Research Agent loaded successfully")
        print(f"   - Name: {research_agent.name}")
        print(f"   - Model: {research_agent.model}")
        print(f"   - Tools: {len(research_agent.tools)}")
        
    except Exception as e:
        test_results['research_agent'] = {
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        print(f"❌ Research Agent failed to load: {e}")
    
    # Test Content Planner Agent
    try:
        from content_planner.agent import root_agent as content_planner_agent
        test_results['content_planner'] = {
            'status': 'success',
            'agent_name': content_planner_agent.name,
            'model': content_planner_agent.model,
            'tool_count': len(content_planner_agent.tools)
        }
        print(f"✅ Content Planner Agent loaded successfully")
        print(f"   - Name: {content_planner_agent.name}")
        print(f"   - Model: {content_planner_agent.model}")
        print(f"   - Tools: {len(content_planner_agent.tools)}")
        
    except Exception as e:
        test_results['content_planner'] = {
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        print(f"❌ Content Planner Agent failed to load: {e}")
    
    return test_results

def test_function_parameters():
    """Test function parameter compatibility specifically."""
    
    print("\n" + "="*60)
    print("Testing Function Parameter Compatibility")
    print("="*60)
    
    # Test analyze_instagram_performance function
    try:
        from instagram_analyzer.agent import analyze_instagram_performance
        result = analyze_instagram_performance("test_user", "30d", None)
        print(f"✅ analyze_instagram_performance: Parameter types compatible")
        print(f"   - Result status: {result.get('status', 'unknown')}")
    except Exception as e:
        print(f"❌ analyze_instagram_performance: {e}")
    
    # Test analyze_youtube_performance function  
    try:
        from youtube_analyzer.agent import analyze_youtube_performance
        result = analyze_youtube_performance("test_user", "30d", None)
        print(f"✅ analyze_youtube_performance: Parameter types compatible")
        print(f"   - Result status: {result.get('status', 'unknown')}")
    except Exception as e:
        print(f"❌ analyze_youtube_performance: {e}")
    
    # Test optimize_content_mix function
    try:
        from content_planner.agent import optimize_content_mix
        result = optimize_content_mix({"content_types": {}}, None)
        print(f"✅ optimize_content_mix: Parameter types compatible")
        print(f"   - Result status: {result.get('status', 'unknown')}")
    except Exception as e:
        print(f"❌ optimize_content_mix: {e}")

if __name__ == "__main__":
    print("="*60)
    print("ADK Agent Parameter Fix Verification")
    print("="*60)
    
    # Test agent loading
    results = test_agent_loading()
    
    # Test function parameters
    test_function_parameters()
    
    # Summary
    print("\n" + "="*60)
    print("Test Summary")
    print("="*60)
    
    success_count = sum(1 for result in results.values() if result['status'] == 'success')
    total_count = len(results)
    
    print(f"Agents successfully loaded: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All agents are now compatible with Google ADK!")
        print("You can now run 'adk web' without parameter type errors.")
    else:
        print("⚠️  Some agents still have issues. Check the error details above.")
        
    # Show detailed results
    print("\nDetailed Results:")
    for agent_name, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"{status_icon} {agent_name}: {result['status']}")
        if result['status'] == 'error':
            print(f"   Error: {result['error']}")