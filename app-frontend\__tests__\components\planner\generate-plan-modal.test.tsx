import React from 'react'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { GeneratePlanModal } from '@/components/planner/generate-plan-modal'
import { renderWithProviders } from '../../utils/test-utils'

describe('GeneratePlanModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  describe('Modal Behavior', () => {
    it('renders when isOpen is true', () => {
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      expect(screen.getByText('Generate Content Plan')).toBeInTheDocument()
      expect(screen.getByText(/Create a personalized content strategy/)).toBeInTheDocument()
    })

    it('does not render when isOpen is false', () => {
      renderWithProviders(<GeneratePlanModal {...defaultProps} isOpen={false} />)
      
      expect(screen.queryByText('Generate Content Plan')).not.toBeInTheDocument()
    })

    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      const closeButton = screen.getByRole('button', { name: /close/i })
      await user.click(closeButton)
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })

    it('calls onClose when cancel is clicked', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      const cancelButton = screen.getByRole('button', { name: /cancel/i })
      await user.click(cancelButton)
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })
  })

  describe('Step 1: Timeframe & Platforms', () => {
    beforeEach(() => {
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
    })

    it('displays timeframe selection', () => {
      expect(screen.getByText('Select Timeframe & Platforms')).toBeInTheDocument()
      expect(screen.getByText('Planning Timeframe')).toBeInTheDocument()
      
      expect(screen.getByText('7 Days')).toBeInTheDocument()
      expect(screen.getByText('14 Days')).toBeInTheDocument()
      expect(screen.getByText('30 Days')).toBeInTheDocument()
    })

    it('selects timeframe correctly', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const sevenDaysButton = screen.getByText('7 Days').closest('button')!
      await user.click(sevenDaysButton)
      
      expect(sevenDaysButton).toHaveClass('border-primary', 'bg-primary/5')
    })

    it('displays platform selection options', () => {
      expect(screen.getByText('Select Platforms')).toBeInTheDocument()
      expect(screen.getByText('YouTube')).toBeInTheDocument()
      expect(screen.getByText('Instagram')).toBeInTheDocument()
      expect(screen.getByText('Twitter')).toBeInTheDocument()
    })

    it('allows multiple platform selection', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      const instagramButton = screen.getByText('Instagram').closest('button')!
      
      await user.click(youtubeButton)
      await user.click(instagramButton)
      
      expect(youtubeButton).toHaveClass('border-primary', 'bg-primary/5')
      expect(instagramButton).toHaveClass('border-primary', 'bg-primary/5')
    })

    it('toggles platform selection on double click', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      
      // Select
      await user.click(youtubeButton)
      expect(youtubeButton).toHaveClass('border-primary', 'bg-primary/5')
      
      // Deselect
      await user.click(youtubeButton)
      expect(youtubeButton).not.toHaveClass('border-primary', 'bg-primary/5')
    })

    it('disables next button when no platforms selected', () => {
      const nextButton = screen.getByRole('button', { name: /next: goals/i })
      expect(nextButton).toBeDisabled()
    })

    it('enables next button when platforms are selected', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      const nextButton = screen.getByRole('button', { name: /next: goals/i })
      expect(nextButton).not.toBeDisabled()
    })

    it('advances to step 2 when next is clicked', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      const nextButton = screen.getByRole('button', { name: /next: goals/i })
      await user.click(nextButton)
      
      expect(screen.getByText('What are your goals?')).toBeInTheDocument()
    })
  })

  describe('Step 2: Goals Selection', () => {
    beforeEach(async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      // Navigate to step 2
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      const nextButton = screen.getByRole('button', { name: /next: goals/i })
      await user.click(nextButton)
    })

    it('displays goals selection interface', () => {
      expect(screen.getByText('What are your goals?')).toBeInTheDocument()
      expect(screen.getByText('Increase followers')).toBeInTheDocument()
      expect(screen.getByText('Boost engagement')).toBeInTheDocument()
      expect(screen.getByText('Drive website traffic')).toBeInTheDocument()
    })

    it('allows multiple goal selection', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const followersButton = screen.getByText('Increase followers').closest('button')!
      const engagementButton = screen.getByText('Boost engagement').closest('button')!
      
      await user.click(followersButton)
      await user.click(engagementButton)
      
      expect(followersButton).toHaveClass('border-primary', 'bg-primary/5')
      expect(engagementButton).toHaveClass('border-primary', 'bg-primary/5')
    })

    it('has back button that returns to step 1', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const backButton = screen.getByRole('button', { name: /back/i })
      await user.click(backButton)
      
      expect(screen.getByText('Select Timeframe & Platforms')).toBeInTheDocument()
    })

    it('disables next button when no goals selected', () => {
      const nextButton = screen.getByRole('button', { name: /next/i })
      expect(nextButton).toBeDisabled()
    })

    it('enables next button when goals are selected', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const followersButton = screen.getByText('Increase followers').closest('button')!
      await user.click(followersButton)
      
      const nextButton = screen.getByRole('button', { name: /next/i })
      expect(nextButton).not.toBeDisabled()
    })
  })

  describe('Step 3: Content Types', () => {
    beforeEach(async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      // Navigate to step 3
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      let nextButton = screen.getByRole('button', { name: /next: goals/i })
      await user.click(nextButton)
      
      const followersButton = screen.getByText('Increase followers').closest('button')!
      await user.click(followersButton)
      
      nextButton = screen.getByRole('button', { name: /next/i })
      await user.click(nextButton)
    })

    it('displays content types selection', () => {
      expect(screen.getByText('Choose Content Types')).toBeInTheDocument()
      expect(screen.getByText('Educational tutorials')).toBeInTheDocument()
      expect(screen.getByText('Behind-the-scenes')).toBeInTheDocument()
      expect(screen.getByText('Product showcases')).toBeInTheDocument()
    })

    it('allows multiple content type selection', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const tutorialsButton = screen.getByText('Educational tutorials').closest('button')!
      const behindScenesButton = screen.getByText('Behind-the-scenes').closest('button')!
      
      await user.click(tutorialsButton)
      await user.click(behindScenesButton)
      
      expect(tutorialsButton).toHaveClass('border-primary', 'bg-primary/5')
      expect(behindScenesButton).toHaveClass('border-primary', 'bg-primary/5')
    })

    it('displays posting frequency options', () => {
      expect(screen.getByText('Posting Frequency')).toBeInTheDocument()
      expect(screen.getByText('Light')).toBeInTheDocument()
      expect(screen.getByText('Regular')).toBeInTheDocument()
      expect(screen.getByText('Intensive')).toBeInTheDocument()
    })

    it('selects posting frequency', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const intensiveButton = screen.getByText('Intensive').closest('button')!
      await user.click(intensiveButton)
      
      expect(intensiveButton).toHaveClass('border-primary', 'bg-primary/5')
    })
  })

  describe('Plan Generation', () => {
    beforeEach(async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      // Complete all steps
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      let nextButton = screen.getByRole('button', { name: /next: goals/i })
      await user.click(nextButton)
      
      const followersButton = screen.getByText('Increase followers').closest('button')!
      await user.click(followersButton)
      
      nextButton = screen.getByRole('button', { name: /next/i })
      await user.click(nextButton)
      
      const tutorialsButton = screen.getByText('Educational tutorials').closest('button')!
      await user.click(tutorialsButton)
    })

    it('shows generate button after completing all steps', () => {
      const generateButton = screen.getByRole('button', { name: /generate plan/i })
      expect(generateButton).toBeInTheDocument()
      expect(generateButton).not.toBeDisabled()
    })

    it('shows loading state during generation', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const generateButton = screen.getByRole('button', { name: /generate plan/i })
      await user.click(generateButton)
      
      expect(screen.getByText(/generating your plan/i)).toBeInTheDocument()
      expect(generateButton).toBeDisabled()
    })

    it('completes generation after timeout', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const generateButton = screen.getByRole('button', { name: /generate plan/i })
      await user.click(generateButton)
      
      // Fast-forward time
      jest.advanceTimersByTime(3000)
      
      await waitFor(() => {
        expect(screen.getByText(/plan generated successfully/i)).toBeInTheDocument()
      })
    })
  })

  describe('Form State Management', () => {
    it('maintains form data across steps', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      // Select platform in step 1
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      // Go to step 2
      const nextButton = screen.getByRole('button', { name: /next: goals/i })
      await user.click(nextButton)
      
      // Go back to step 1
      const backButton = screen.getByRole('button', { name: /back/i })
      await user.click(backButton)
      
      // Verify YouTube is still selected
      expect(youtubeButton).toHaveClass('border-primary', 'bg-primary/5')
    })

    it('resets form data when modal is closed and reopened', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      const { rerender } = renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      // Select platform
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      // Close modal
      const closeButton = screen.getByRole('button', { name: /close/i })
      await user.click(closeButton)
      
      // Reopen modal
      rerender(<GeneratePlanModal {...defaultProps} isOpen={true} />)
      
      // Verify selection is reset (this depends on implementation)
      const newYoutubeButton = screen.getByText('YouTube').closest('button')!
      expect(newYoutubeButton).not.toHaveClass('border-primary', 'bg-primary/5')
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
    })

    it('has proper heading hierarchy', () => {
      expect(screen.getByRole('heading', { level: 2, name: /generate content plan/i })).toBeInTheDocument()
      expect(screen.getByRole('heading', { level: 3, name: /select timeframe & platforms/i })).toBeInTheDocument()
    })

    it('has proper button labels', () => {
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /next: goals/i })).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      // Tab navigation should work
      await user.tab()
      expect(document.activeElement).toBeDefined()
    })

    it('maintains focus management during step transitions', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      const youtubeButton = screen.getByText('YouTube').closest('button')!
      await user.click(youtubeButton)
      
      const nextButton = screen.getByRole('button', { name: /next: goals/i })
      await user.click(nextButton)
      
      // Focus should be maintained appropriately
      expect(document.activeElement).toBeDefined()
    })
  })

  describe('Responsive Design', () => {
    it('applies responsive grid classes', () => {
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      const timeframeGrid = screen.getByText('7 Days').closest('.grid')
      expect(timeframeGrid).toHaveClass('grid-cols-3')
      
      const platformGrid = screen.getByText('YouTube').closest('.grid')
      expect(platformGrid).toHaveClass('grid-cols-3')
    })

    it('handles overflow with scrolling', () => {
      renderWithProviders(<GeneratePlanModal {...defaultProps} />)
      
      const modal = screen.getByText('Generate Content Plan').closest('.max-h-\\[90vh\\]')
      expect(modal).toHaveClass('overflow-y-auto')
    })
  })
})