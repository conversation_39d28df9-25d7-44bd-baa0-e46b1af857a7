from setuptools import setup, find_packages

setup(
    name="social_media_agents",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "google-adk",  # Google Agent Development Kit
        "pytest",     # Testing framework
        "anyio",      # Async IO for tests
        "python-dotenv"  # For environment variables
    ],
    extras_require={
        "dev": [
            "pytest",
            "anyio",
            "black",
            "isort",
            "mypy"
        ]
    },
    python_requires=">=3.10",
    entry_points={
        "console_scripts": [
            "social-media-agent=app.main:main"
        ]
    }
)