#!/usr/bin/env python3
"""
Simple validation test for the error handling system.
"""

import sys
import traceback
from datetime import datetime

def test_error_hierarchy():
    """Test the error hierarchy works correctly"""
    print("Testing error hierarchy...")
    
    try:
        from app.core.error_handling import (
            ADKIntegrationError,
            ADKServerUnavailableError,
            ADKAgentNotFoundError,
            ADKSessionError,
            ADKStreamingError,
            ADKTimeoutError,
            ADKAuthenticationError,
            ADKValidationError,
            ErrorSeverity,
            ErrorHandler
        )
        print("✓ All error classes imported successfully")
        
        # Test base error
        base_error = ADKIntegrationError(
            message="Test error",
            error_code="TEST_ERROR",
            severity=ErrorSeverity.HIGH
        )
        assert base_error.message == "Test error"
        assert base_error.error_code == "TEST_ERROR"
        assert base_error.severity == ErrorSeverity.HIGH
        print("✓ Base ADKIntegrationError works correctly")
        
        # Test server unavailable error
        server_error = ADKServerUnavailableError()
        assert server_error.error_code == "ADK_SERVER_UNAVAILABLE"
        assert server_error.severity == ErrorSeverity.HIGH
        print("✓ ADKServerUnavailableError works correctly")
        
        # Test agent not found error
        agent_error = ADKAgentNotFoundError(
            agent_name="missing_agent",
            available_agents=["agent1", "agent2"]
        )
        assert agent_error.error_code == "ADK_AGENT_NOT_FOUND"
        assert agent_error.details["agent_name"] == "missing_agent"
        assert agent_error.details["available_agents"] == ["agent1", "agent2"]
        print("✓ ADKAgentNotFoundError works correctly")
        
        # Test error serialization
        error_dict = base_error.to_dict()
        assert error_dict["error_type"] == "ADKIntegrationError"
        assert error_dict["error_message"] == "Test error"  # Updated field name
        assert error_dict["error_code"] == "TEST_ERROR"
        assert error_dict["severity"] == "high"
        print("✓ Error serialization works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Error hierarchy test failed: {e}")
        traceback.print_exc()
        return False


def test_error_handler():
    """Test the error handler functionality"""
    print("\nTesting error handler...")
    
    try:
        from app.core.error_handling import (
            ErrorHandler,
            ADKServerUnavailableError,
            ADKAgentNotFoundError
        )
        from app.models.chat_models import StreamingChunk
        
        error_handler = ErrorHandler()
        
        # Test user-friendly messages
        server_error = ADKServerUnavailableError()
        message = error_handler.get_user_friendly_message(server_error)
        assert "temporarily unavailable" in message.lower()
        print("✓ User-friendly messages work correctly")
        
        # Test error response creation
        response = error_handler.create_error_response(server_error)
        assert response["error"] is True
        assert response["error_code"] == "ADK_SERVER_UNAVAILABLE"
        assert response["severity"] == "high"
        print("✓ Error response creation works correctly")
        
        # Test streaming chunk creation
        chunk = error_handler.create_error_stream_chunk(server_error, "test_id")
        assert isinstance(chunk, StreamingChunk)
        assert chunk.done is True
        assert chunk.message_id == "test_id"
        assert chunk.metadata["error"] is True
        print("✓ Error streaming chunk creation works correctly")
        
        # Test HTTP exception creation
        http_exc = error_handler.create_http_exception(server_error)
        assert http_exc.status_code == 503
        assert http_exc.detail["error"] is True
        print("✓ HTTP exception creation works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Error handler test failed: {e}")
        traceback.print_exc()
        return False


def test_middleware_imports():
    """Test middleware imports work correctly"""
    print("\nTesting middleware imports...")
    
    try:
        from app.core.middleware import (
            ADKErrorHandlingMiddleware,
            ADKHealthCheckMiddleware,
            create_adk_error_middleware,
            create_adk_health_middleware
        )
        print("✓ All middleware classes imported successfully")
        
        # Test middleware factory functions
        error_middleware_factory = create_adk_error_middleware(enable_fallback=True)
        assert callable(error_middleware_factory)
        print("✓ Error middleware factory works correctly")
        
        health_middleware_factory = create_adk_health_middleware(failure_threshold=3)
        assert callable(health_middleware_factory)
        print("✓ Health middleware factory works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Middleware import test failed: {e}")
        traceback.print_exc()
        return False


def test_convenience_functions():
    """Test convenience functions"""
    print("\nTesting convenience functions...")
    
    try:
        from app.core.error_handling import (
            handle_adk_error,
            create_error_stream,
            ADKStreamingError
        )
        
        # Test handle_adk_error function
        error = ADKStreamingError(message="Test stream error")
        response = handle_adk_error(error)
        assert response["error"] is True
        assert response["error_code"] == "ADK_STREAMING_ERROR"
        print("✓ handle_adk_error function works correctly")
        
        # Test create_error_stream function
        stream_data = create_error_stream(error, "test_id")
        assert stream_data.startswith("data: ")
        assert stream_data.endswith("\n\n")
        print("✓ create_error_stream function works correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Convenience functions test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all validation tests"""
    print("=" * 60)
    print("ADK Error Handling System Validation")
    print("=" * 60)
    
    tests = [
        test_error_hierarchy,
        test_error_handler,
        test_middleware_imports,
        test_convenience_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All error handling components are working correctly!")
        return 0
    else:
        print("✗ Some error handling components failed validation")
        return 1


if __name__ == "__main__":
    sys.exit(main())