#!/usr/bin/env python3
"""
Test Agent Connectivity - Verify all agents and tools are properly connected
"""

import sys
import os

# Add the agents directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'agents'))

def test_agent_connectivity():
    """Test that all agents and tools are properly connected."""
    print("🧪 Testing Agent Connectivity...")
    print("=" * 50)
    
    try:
        # Import the main coordinator agent
        from agents.agent import root_agent
        
        print(f"✅ Main Coordinator Agent: {root_agent.name}")
        print(f"   Model: {root_agent.model}")
        print(f"   Description: {root_agent.description[:100]}...")
        print()
        
        # Test sub-agents
        print("📋 Sub-Agents:")
        for i, sub_agent in enumerate(root_agent.sub_agents, 1):
            print(f"   {i}. {sub_agent.name}")
            print(f"      Model: {sub_agent.model}")
            print(f"      Tools: {len(sub_agent.tools) if hasattr(sub_agent, 'tools') else 0}")
        print()
        
        # Test tools
        print("🔧 Available Tools:")
        for i, tool in enumerate(root_agent.tools, 1):
            tool_name = getattr(tool, '__name__', getattr(tool, 'name', str(tool)))
            tool_type = type(tool).__name__
            print(f"   {i}. {tool_name} ({tool_type})")
        print()
        
        # Test platform-specific tools
        instagram_tools = [t for t in root_agent.tools if 'instagram' in str(t).lower()]
        youtube_tools = [t for t in root_agent.tools if 'youtube' in str(t).lower()]
        
        print("📱 Platform-Specific Tools:")
        print(f"   Instagram tools: {len(instagram_tools)}")
        for tool in instagram_tools:
            tool_name = getattr(tool, '__name__', getattr(tool, 'name', str(tool)))
            print(f"     • {tool_name}")
        
        print(f"   YouTube tools: {len(youtube_tools)}")
        for tool in youtube_tools:
            tool_name = getattr(tool, '__name__', getattr(tool, 'name', str(tool)))
            print(f"     • {tool_name}")
        print()
        
        # Test individual agent imports
        print("🤖 Individual Agent Tests:")
        
        # Test Instagram agent
        try:
            from agents.instagram_analyzer.agent import root_agent as instagram_agent
            print(f"   ✅ Instagram Agent: {instagram_agent.name} ({len(instagram_agent.tools)} tools)")
        except Exception as e:
            print(f"   ❌ Instagram Agent: {e}")
        
        # Test YouTube agent
        try:
            from agents.youtube_analyzer.agent import root_agent as youtube_agent
            print(f"   ✅ YouTube Agent: {youtube_agent.name} ({len(youtube_agent.tools)} tools)")
        except Exception as e:
            print(f"   ❌ YouTube Agent: {e}")
        
        # Test News Content agent
        try:
            from agents.news_content_agent.agent import root_agent as news_agent
            print(f"   ✅ News Content Agent: {news_agent.name} ({len(news_agent.tools)} tools)")
        except Exception as e:
            print(f"   ❌ News Content Agent: {e}")
        
        # Test Google Search agent
        try:
            from agents.google_search_root_agent.agent import root_agent as search_agent
            print(f"   ✅ Google Search Agent: {search_agent.name} ({len(search_agent.tools)} tools)")
        except Exception as e:
            print(f"   ❌ Google Search Agent: {e}")
        
        print()
        print("🎯 Connectivity Summary:")
        print(f"   Total sub-agents: {len(root_agent.sub_agents)}")
        print(f"   Total tools: {len(root_agent.tools)}")
        print(f"   Instagram connectivity: {'✅ Connected' if instagram_tools else '❌ Not connected'}")
        print(f"   YouTube connectivity: {'✅ Connected' if youtube_tools else '❌ Not connected'}")
        print(f"   News workflow: {'✅ Available' if any('news' in str(agent).lower() for agent in root_agent.sub_agents) else '❌ Missing'}")
        print(f"   Google Search: {'✅ Available' if any('search' in str(tool).lower() for tool in root_agent.tools) else '❌ Missing'}")
        
        print()
        print("🚀 Test Result: CONNECTIVITY VERIFIED!")
        return True
        
    except Exception as e:
        print(f"❌ Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_agent_connectivity()
    sys.exit(0 if success else 1)