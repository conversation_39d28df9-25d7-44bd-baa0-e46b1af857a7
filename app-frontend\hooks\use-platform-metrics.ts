"use client";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/lib/api-client";

export interface PlatformMetricsData {
  timeframe: string;
  platforms: {
    [platform: string]: {
      followers: number;
      engagement: number;
      reach: number;
      growth: {
        followers: number;
        engagement: number;
      };
      posts: number;
      avgPerformance: number;
    };
  };
  overall: {
    totalFollowers: number;
    avgEngagement: number;
    totalReach: number;
    healthScore: number;
  };
}

export function usePlatformMetrics(timeframe: string = "30d") {
  return useQuery({
    queryKey: ["metrics", timeframe],
    queryFn: async (): Promise<PlatformMetricsData> => {
      try {
        const response = await apiClient.get(`/accounts/metrics?timeframe=${timeframe}`);
        return response.data;
      } catch (error) {
        // Return mock data for development
        return {
          timeframe,
          platforms: {
            youtube: {
              followers: 15420,
              engagement: 4.2,
              reach: 125000,
              growth: { followers: 7.8, engagement: 0.5 },
              posts: 12,
              avgPerformance: 10400
            },
            instagram: {
              followers: 8750,
              engagement: 3.1,
              reach: 67000,
              growth: { followers: 5.2, engagement: -0.2 },
              posts: 18,
              avgPerformance: 425
            }
          },
          overall: {
            totalFollowers: 24170,
            avgEngagement: 3.65,
            totalReach: 192000,
            healthScore: 78
          }
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}