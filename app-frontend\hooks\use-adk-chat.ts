"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useADKStreaming } from './use-adk-streaming';
import { useSessionPersistence } from './use-session-persistence';
import { useChatHistory } from './use-chat-history';
import { 
  EnhancedChatMessage, 
  StreamingChunk, 
  ChatError, 
  MessageRole 
} from '@/types/adk';

export interface UseADKChatOptions {
  userId: string;
  sessionId?: string;
  agentName?: string;
  enableStreaming?: boolean;
  enableSessionPersistence?: boolean;
  enableHistoryRecovery?: boolean;
  onMessageReceived?: (message: EnhancedChatMessage) => void;
  onError?: (error: ChatError) => void;
  onSessionRecovered?: (sessionId: string) => void;
}

export interface UseADKChatReturn {
  messages: EnhancedChatMessage[];
  isLoading: boolean;
  isStreaming: boolean;
  isReconnecting: boolean;
  isRecoveringSession: boolean;
  error: ChatError | null;
  currentAgent: string;
  sessionId: string | null;
  streamingMessage: Partial<EnhancedChatMessage> | null;
  sendMessage: (message: string) => Promise<void>;
  interruptStreaming: () => void;
  clearError: () => void;
  retryLastMessage: () => Promise<void>;
  clearSession: () => void;
  refreshHistory: () => Promise<void>;
}

export function useADKChat(options: UseADKChatOptions): UseADKChatReturn {
  const {
    userId,
    sessionId: providedSessionId,
    agentName = 'content_planner',
    enableStreaming = true,
    enableSessionPersistence = true,
    enableHistoryRecovery = true,
    onMessageReceived,
    onError,
    onSessionRecovered
  } = options;

  const queryClient = useQueryClient();
  const [streamingMessage, setStreamingMessage] = useState<Partial<EnhancedChatMessage> | null>(null);
  const [lastMessage, setLastMessage] = useState<string>('');
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(providedSessionId || null);
  const currentMessageIdRef = useRef<string>('');

  // Session persistence hook
  const sessionPersistence = useSessionPersistence({
    userId,
    agentName,
    storageType: 'localStorage',
    autoSave: enableSessionPersistence,
    onSessionRecovered: (sessionData) => {
      setCurrentSessionId(sessionData.sessionId);
      onSessionRecovered?.(sessionData.sessionId);
    },
    onSessionCleared: () => {
      setCurrentSessionId(null);
    }
  });

  // Chat history hook
  const chatHistory = useChatHistory({
    sessionId: currentSessionId || undefined,
    userId,
    agentName,
    autoRefresh: false,
    includeMetadata: true
  });

  // Use session from persistence if no session provided
  const effectiveSessionId = currentSessionId || sessionPersistence.sessionId;

  // Handle streaming chunks
  const handleStreamingChunk = useCallback((chunk: StreamingChunk) => {
    const messageId = chunk.message_id;
    
    setStreamingMessage(prev => {
      const updated: Partial<EnhancedChatMessage> = {
        id: messageId,
        content: (prev?.content || '') + chunk.content,
        role: 'model' as MessageRole,
        agent_name: chunk.metadata?.author || agentName,
        function_calls: chunk.metadata?.function_calls,
        interrupted: chunk.metadata?.interrupted,
        metadata: chunk.metadata,
        timestamp: new Date().toISOString()
      };
      return updated;
    });

    // Update the query cache with streaming content
    queryClient.setQueryData(["chat", "history"], (old: EnhancedChatMessage[] = []) => {
      const newMessages = [...old];
      const lastIndex = newMessages.length - 1;
      
      if (lastIndex >= 0 && newMessages[lastIndex].id === messageId) {
        // Update existing streaming message
        newMessages[lastIndex] = {
          ...newMessages[lastIndex],
          content: (newMessages[lastIndex].content || '') + chunk.content,
          function_calls: chunk.metadata?.function_calls,
          interrupted: chunk.metadata?.interrupted,
          metadata: chunk.metadata
        };
      } else {
        // Add new streaming message
        newMessages.push({
          id: messageId,
          session_id: effectiveSessionId || '',
          role: 'model' as MessageRole,
          content: chunk.content,
          user_id: userId,
          timestamp: new Date().toISOString(),
          agent_name: chunk.metadata?.author || agentName,
          function_calls: chunk.metadata?.function_calls,
          interrupted: chunk.metadata?.interrupted,
          metadata: chunk.metadata
        });
      }
      
      return newMessages;
    });
  }, [queryClient, effectiveSessionId, userId, agentName]);

  // Handle streaming completion
  const handleStreamingComplete = useCallback((messageId: string) => {
    if (streamingMessage) {
      const finalMessage: EnhancedChatMessage = {
        id: messageId,
        session_id: effectiveSessionId || '',
        role: 'model' as MessageRole,
        content: streamingMessage.content || '',
        user_id: userId,
        timestamp: new Date().toISOString(),
        agent_name: streamingMessage.agent_name || agentName,
        function_calls: streamingMessage.function_calls,
        interrupted: streamingMessage.interrupted,
        metadata: streamingMessage.metadata
      };

      onMessageReceived?.(finalMessage);
      setStreamingMessage(null);

      // Update session activity
      if (enableSessionPersistence) {
        sessionPersistence.updateActivity();
      }

      // Update chat history cache
      if ((window as any).__updateChatHistoryCache) {
        (window as any).__updateChatHistoryCache(finalMessage);
      }
    }
  }, [streamingMessage, effectiveSessionId, userId, agentName, onMessageReceived, enableSessionPersistence, sessionPersistence]);

  // Handle streaming errors
  const handleStreamingError = useCallback((error: ChatError) => {
    console.error('Streaming error:', error);
    setStreamingMessage(null);
    onError?.(error);
  }, [onError]);

  // Initialize streaming hook
  const streaming = useADKStreaming({
    userId,
    sessionId: effectiveSessionId || undefined,
    agentName,
    onChunk: handleStreamingChunk,
    onComplete: handleStreamingComplete,
    onError: handleStreamingError,
    autoReconnect: true,
    maxReconnectAttempts: 3,
    reconnectDelay: 2000
  });

  // Create or get session before sending message
  const ensureSession = useCallback(async (): Promise<string> => {
    if (effectiveSessionId) {
      return effectiveSessionId;
    }

    // Create new session via backend
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
    const authToken = localStorage.getItem('access_token') || 'dev-token';

    const response = await fetch(`${baseUrl}/chat/session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        user_id: userId,
        agent_name: agentName
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to create session: ${response.status}`);
    }

    const result = await response.json();
    const newSessionId = result.session_id;

    // Save session to persistence
    if (enableSessionPersistence) {
      sessionPersistence.saveSession(newSessionId, {
        agent_name: agentName,
        created_at: new Date().toISOString()
      });
    }

    setCurrentSessionId(newSessionId);
    return newSessionId;
  }, [effectiveSessionId, userId, agentName, enableSessionPersistence, sessionPersistence]);

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async (message: string): Promise<void> => {
      setLastMessage(message);
      
      // Ensure we have a session
      const sessionId = await ensureSession();
      
      // Add user message immediately to the UI
      const userMessageId = `user_${Date.now()}`;
      const userMessage: EnhancedChatMessage = {
        id: userMessageId,
        session_id: sessionId,
        role: 'user' as MessageRole,
        content: message,
        user_id: userId,
        timestamp: new Date().toISOString(),
        agent_name: agentName
      };

      queryClient.setQueryData(["chat", "history"], (old: EnhancedChatMessage[] = []) => [
        ...old,
        userMessage,
      ]);

      // Update chat history cache
      if ((window as any).__updateChatHistoryCache) {
        (window as any).__updateChatHistoryCache(userMessage);
      }

      if (enableStreaming) {
        // Use EventSource streaming
        const messageId = `assistant_${Date.now()}`;
        currentMessageIdRef.current = messageId;
        await streaming.startStreaming(messageId, message);
      } else {
        // Fallback to regular HTTP request
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
        const authToken = localStorage.getItem('access_token') || 'dev-token';
        
        const response = await fetch(`${baseUrl}/chat/send-message`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
          },
          body: JSON.stringify({
            message,
            session_id: sessionId,
            user_id: userId,
            agent_name: agentName
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        const assistantMessage: EnhancedChatMessage = {
          id: `assistant_${Date.now()}`,
          session_id: sessionId,
          role: 'model' as MessageRole,
          content: result.content || result.message || '',
          user_id: userId,
          timestamp: new Date().toISOString(),
          agent_name: agentName,
          metadata: result.metadata
        };

        queryClient.setQueryData(["chat", "history"], (old: EnhancedChatMessage[] = []) => [
          ...old,
          assistantMessage,
        ]);

        onMessageReceived?.(assistantMessage);

        // Update session activity
        if (enableSessionPersistence) {
          sessionPersistence.updateActivity();
        }
      }
    },
    onError: (error) => {
      console.error("Failed to send message:", error);
      
      // Remove the user message if sending failed
      queryClient.setQueryData(["chat", "history"], (old: EnhancedChatMessage[] = []) => {
        return old.slice(0, -1); // Remove the last message (user message)
      });

      const chatError: ChatError = {
        error: error instanceof Error ? error.message : 'Failed to send message',
        error_code: 'SEND_MESSAGE_ERROR',
        session_id: effectiveSessionId || undefined
      };
      
      handleStreamingError(chatError);
    },
  });

  // Retry last message
  const retryLastMessage = useCallback(async () => {
    if (lastMessage) {
      await sendMessageMutation.mutateAsync(lastMessage);
    }
  }, [lastMessage, sendMessageMutation]);

  // Clear session function
  const clearSession = useCallback(() => {
    if (enableSessionPersistence) {
      sessionPersistence.clearSession();
    }
    setCurrentSessionId(null);
    queryClient.removeQueries({ queryKey: ["chat", "history"] });
    chatHistory.clearHistory();
  }, [enableSessionPersistence, sessionPersistence, queryClient, chatHistory]);

  // Refresh history function
  const refreshHistory = useCallback(async () => {
    await chatHistory.refreshHistory();
  }, [chatHistory]);

  // Auto-recover session on mount
  useEffect(() => {
    if (enableSessionPersistence && enableHistoryRecovery && !effectiveSessionId) {
      sessionPersistence.recoverSession();
    }
  }, [enableSessionPersistence, enableHistoryRecovery, effectiveSessionId, sessionPersistence]);

  // Get messages from chat history or query cache
  const messages = enableHistoryRecovery && effectiveSessionId 
    ? chatHistory.messages 
    : queryClient.getQueryData<EnhancedChatMessage[]>(["chat", "history"]) || [];

  return {
    messages,
    isLoading: sendMessageMutation.isPending,
    isStreaming: streaming.isStreaming,
    isReconnecting: streaming.isReconnecting,
    isRecoveringSession: sessionPersistence.isRecovering,
    error: streaming.error,
    currentAgent: agentName,
    sessionId: effectiveSessionId,
    streamingMessage,
    sendMessage: sendMessageMutation.mutateAsync,
    interruptStreaming: streaming.interruptStreaming,
    clearError: streaming.clearError,
    retryLastMessage,
    clearSession,
    refreshHistory
  };
}