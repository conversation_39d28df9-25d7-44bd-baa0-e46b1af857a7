apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: social-media-frontend
  labels:
    app: social-media-manager
    component: frontend
    env: production
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      labels:
        app: social-media-manager
        component: frontend
        env: production
      annotations:
        # Autoscaling
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "50"
        
        # Performance
        run.googleapis.com/cpu: "1"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/startup-cpu-boost: "true"
        
        # Timeout
        run.googleapis.com/timeout: "60s"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 60
      containers:
      - name: frontend
        image: gcr.io/PROJECT_ID/social-media-frontend:latest
        ports:
        - name: http1
          containerPort: 3000
        env:
        # Application Configuration
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: HOSTNAME
          value: "0.0.0.0"
        - name: NEXT_TELEMETRY_DISABLED
          value: "1"
        
        # API Configuration
        - name: NEXT_PUBLIC_API_URL
          value: "https://your-backend-domain.com/api"
        - name: NEXT_PUBLIC_FRONTEND_URL
          value: "https://your-frontend-domain.com"
        
        # OAuth Configuration (Public keys only)
        - name: NEXT_PUBLIC_GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: oauth-secrets
              key: google_client_id
        - name: NEXT_PUBLIC_INSTAGRAM_APP_ID
          valueFrom:
            secretKeyRef:
              name: oauth-secrets
              key: instagram_app_id
        
        # Feature Flags
        - name: NEXT_PUBLIC_ENABLE_ANALYTICS
          value: "true"
        - name: NEXT_PUBLIC_ENABLE_DEBUG
          value: "false"
        
        # Resource Configuration
        resources:
          limits:
            cpu: "1"
            memory: "2Gi"
          requests:
            cpu: "0.5"
            memory: "1Gi"
        
        # Health Checks
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        
        startupProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
  
  traffic:
  - percent: 100
    latestRevision: true