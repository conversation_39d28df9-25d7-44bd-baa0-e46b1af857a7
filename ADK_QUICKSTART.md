# ADK Quick Start Guide

## 🚀 Get Started with Google ADK in 5 Minutes

This quick guide gets you up and running with Google ADK (Agent Development Kit) integration in the Social Media Agents project.

## Prerequisites

- Project already set up and running
- Python 3.11+ environment
- Basic understanding of the project structure

## Step 1: Verify ADK Installation

ADK is already installed in the project. Verify it's working:

```bash
cd app-agents
python -c "import google.adk; print('ADK installed successfully!')"
```

## Step 2: Configure Authentication

### Quick Setup (Google AI Studio)

1. **Get API Key**: Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. **Set Environment**: Add to your `.env` file:

```bash
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
```

3. **Verify Setup**:

```bash
python -c "from app.services.adk_config_service import get_adk_config_service; print(get_adk_config_service().validate_configuration())"
```

## Step 3: Test ADK Research Agent

Create a simple test script:

```python
# test_adk.py
import asyncio
from app.agents.adk_research_agent import ADKResearchAgent

async def test_adk():
    # Create ADK research agent
    agent = ADKResearchAgent()
    
    # Run intelligent analysis
    result = await agent.analyze(
        user_id="test_user",
        query="social media trends 2024",
        platforms=["youtube", "instagram"]
    )
    
    print("Analysis Method:", result.get('analysis_method'))
    print("AI Response:", result.get('agent_response', 'No response'))
    print("Insights:", result.get('insights', []))

# Run the test
asyncio.run(test_adk())
```

Run the test:

```bash
cd app-agents
python test_adk.py
```

## Step 4: Use Enhanced Research Agent

The existing research agent now has ADK enhancement:

```python
# enhanced_research_test.py
import asyncio
from app.agents.research_agent import ResearchAgent

async def test_enhanced_research():
    # Create enhanced research agent (ADK enabled by default)
    agent = ResearchAgent(use_adk=True)
    
    # Perform intelligent research
    result = await agent.search_trends(
        query="YouTube content strategy",
        platforms=["youtube", "instagram"]
    )
    
    print("Analysis Method:", result.get('analysis_method'))
    
    # Check if ADK enhancement was used
    if result.get('analysis_method') == 'adk_enhanced':
        print("🤖 ADK Enhancement Active!")
        adk_analysis = result.get('adk_analysis', {})
        print("AI Agent Response:", adk_analysis.get('agent_response'))
    else:
        print("📊 Using traditional analysis")
    
    print("Insights:", result.get('insights', [])[:3])  # Top 3 insights

asyncio.run(test_enhanced_research())
```

## Step 5: Test the Full Pipeline

Test the complete coordinator integration:

```python
# coordinator_test.py
import asyncio
from app.agents.coordinator import CoordinatorAgent

async def test_coordinator():
    # Create coordinator with ADK enhancement
    coordinator = CoordinatorAgent(enable_adk=True)
    
    # Simulate chat message processing
    user_id = "test_user"
    message = "Analyze current social media trends and give me content ideas"
    
    print("Processing message with ADK-enhanced coordinator...")
    
    # Stream the response
    async for response_chunk in coordinator.process_chat_message(user_id, message):
        print(response_chunk, end='', flush=True)
    
    print("\n\nCoordinator test completed!")

asyncio.run(test_coordinator())
```

## Step 6: Monitor ADK Status

Create a status dashboard script:

```python
# adk_status.py
import asyncio
from app.services.adk_config_service import get_adk_config_service
from app.services.adk_session_service import get_adk_session_service

async def show_adk_status():
    # Configuration status
    config_service = get_adk_config_service()
    status = config_service.validate_configuration()
    
    print("🔧 ADK Configuration Status")
    print("="*40)
    print(f"ADK Available: {status['adk_available']}")
    print(f"Configuration Loaded: {status['config_loaded']}")
    print(f"ADK Ready: {status['adk_ready']}")
    print(f"Authentication: {status['authentication']}")
    print(f"Model Provider: {status['model_provider']}")
    
    if status['issues']:
        print("\n⚠️ Issues Found:")
        for issue in status['issues']:
            print(f"  - {issue}")
    else:
        print("\n✅ No issues found")
    
    # Available models
    models = config_service.get_available_models()
    print(f"\n🤖 Available Models: {', '.join(models)}")
    
    # Session statistics
    session_service = get_adk_session_service()
    session_stats = await session_service.get_session_stats()
    
    print("\n📊 Session Statistics")
    print("="*40)
    print(f"Total Sessions: {session_stats['total_sessions']}")
    print(f"Active Sessions: {session_stats['active_sessions']}")
    print(f"Expired Sessions: {session_stats['expired_sessions']}")
    print(f"Session Timeout: {session_stats['session_timeout_hours']} hours")

asyncio.run(show_adk_status())
```

Run the status check:

```bash
python adk_status.py
```

## Expected Output

If everything is set up correctly, you should see:

```
🔧 ADK Configuration Status
========================================
ADK Available: True
Configuration Loaded: True
ADK Ready: True
Authentication: google_ai_studio
Model Provider: Google AI Studio

✅ No issues found

🤖 Available Models: gemini-2.0-flash-exp, gemini-1.5-pro, gemini-1.5-flash, gemini-pro

📊 Session Statistics
========================================
Total Sessions: 0
Active Sessions: 0
Expired Sessions: 0
Session Timeout: 1.0 hours
```

## Troubleshooting Quick Fixes

### Issue: "ADK not available"
```bash
# Reinstall ADK
pip install google-adk==1.12.0

# Check installation
python -c "import google.adk; print('OK')"
```

### Issue: "Authentication missing"
```bash
# Check API key is set
echo $GOOGLE_API_KEY

# Or check in Python
python -c "import os; print('API Key:', 'SET' if os.getenv('GOOGLE_API_KEY') else 'NOT SET')"
```

### Issue: "Configuration failed"
```bash
# Check environment variables
python -c "
import os
print('GOOGLE_GENAI_USE_VERTEXAI:', os.getenv('GOOGLE_GENAI_USE_VERTEXAI'))
print('GOOGLE_API_KEY:', 'SET' if os.getenv('GOOGLE_API_KEY') else 'NOT SET')
"
```

## Next Steps

1. **Explore Tools**: Check out the tool functions in `adk_research_agent.py`
2. **Custom Agents**: Create your own ADK agents using the config service
3. **Production Setup**: Move to Vertex AI for production deployment
4. **Integration**: Use ADK agents in your existing workflows

## Development Tips

- **Start Simple**: Begin with basic queries to test connectivity
- **Use Mock Mode**: ADK gracefully falls back when unavailable
- **Monitor Sessions**: Keep an eye on session usage for performance
- **Check Logs**: Enable debug logging to trace ADK operations

## Production Checklist

- [ ] API key or GCP authentication configured
- [ ] Environment variables properly set
- [ ] ADK validation passing
- [ ] Session management working
- [ ] Error handling tested
- [ ] Monitoring in place

Congratulations! You now have Google ADK integrated and running. Your social media agents are powered by advanced AI capabilities! 🎉

For more detailed information, see the full [ADK Integration Guide](ADK_INTEGRATION_GUIDE.md).