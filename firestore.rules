rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions for authentication and authorization
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidEmail(email) {
      return email.matches('.*@.*\\..*');
    }
    
    function isValidUser() {
      let data = request.resource.data;
      return data.keys().hasAll(['id', 'name', 'email']) &&
             data.name is string && data.name.size() > 0 &&
             data.email is string && isValidEmail(data.email) &&
             data.id is string && data.id.size() > 0;
    }
    
    function isValidAccount() {
      let data = request.resource.data;
      return data.keys().hasAll(['id', 'user_id', 'platform', 'handle']) &&
             data.platform in ['youtube', 'instagram', 'twitter'] &&
             data.handle is string && data.handle.size() > 0 &&
             data.user_id is string && data.user_id.size() > 0;
    }
    
    function isValidPlan() {
      let data = request.resource.data;
      return data.keys().hasAll(['id', 'user_id', 'title']) &&
             data.title is string && data.title.size() > 0 &&
             data.user_id is string && data.user_id.size() > 0 &&
             data.status in ['draft', 'active', 'completed', 'cancelled'];
    }
    
    function isValidMessage() {
      let data = request.resource.data;
      return data.keys().hasAll(['id', 'user_id', 'role', 'content']) &&
             data.role in ['user', 'assistant'] &&
             data.content is string && data.content.size() > 0 &&
             data.user_id is string && data.user_id.size() > 0;
    }
    
    // Users collection - users can read/write their own profile
    match /users/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
      allow create: if isAuthenticated() && isOwner(userId) && isValidUser();
      allow update: if isAuthenticated() && isOwner(userId) && isValidUser();
      allow delete: if isAuthenticated() && isOwner(userId);
    }
    
    // Connected accounts - users can manage their own connected accounts
    match /connected_accounts/{accountId} {
      allow read, write: if isAuthenticated() && 
        isOwner(resource.data.user_id);
      allow create: if isAuthenticated() && 
        isOwner(request.resource.data.user_id) && isValidAccount();
      allow update: if isAuthenticated() && 
        isOwner(request.resource.data.user_id) && isValidAccount();
      allow delete: if isAuthenticated() && 
        isOwner(resource.data.user_id);
    }
    
    // Content plans - users can manage their own content plans
    match /content_plans/{planId} {
      allow read, write: if isAuthenticated() && 
        isOwner(resource.data.user_id);
      allow create: if isAuthenticated() && 
        isOwner(request.resource.data.user_id) && isValidPlan();
      allow update: if isAuthenticated() && 
        isOwner(request.resource.data.user_id) && isValidPlan();
      allow delete: if isAuthenticated() && 
        isOwner(resource.data.user_id);
      
      // Nested posts subcollection
      match /posts/{postId} {
        allow read, write: if isAuthenticated() && 
          isOwner(get(/databases/$(database)/documents/content_plans/$(planId)).data.user_id);
        allow create: if isAuthenticated() && 
          isOwner(get(/databases/$(database)/documents/content_plans/$(planId)).data.user_id);
        allow update: if isAuthenticated() && 
          isOwner(get(/databases/$(database)/documents/content_plans/$(planId)).data.user_id);
        allow delete: if isAuthenticated() && 
          isOwner(get(/databases/$(database)/documents/content_plans/$(planId)).data.user_id);
      }
    }
    
    // Chat messages - users can read/write their own messages
    match /chat_messages/{messageId} {
      allow read, write: if isAuthenticated() && 
        isOwner(resource.data.user_id);
      allow create: if isAuthenticated() && 
        isOwner(request.resource.data.user_id) && isValidMessage();
      allow update: if isAuthenticated() && 
        isOwner(request.resource.data.user_id) && isValidMessage();
      allow delete: if isAuthenticated() && 
        isOwner(resource.data.user_id);
    }
    
    // Analytics data - read-only for account owners
    match /analytics_data/{accountId} {
      allow read: if isAuthenticated() && 
        exists(/databases/$(database)/documents/connected_accounts/$(accountId)) &&
        isOwner(get(/databases/$(database)/documents/connected_accounts/$(accountId)).data.user_id);
      // Analytics data is typically written by backend services, not users
      allow write: if false;
    }
    
    // User sessions subcollection (if implemented)
    match /users/{userId}/sessions/{sessionId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Default deny rule for any other paths
    match /{document=**} {
      allow read, write: if false;
    }
  }
}