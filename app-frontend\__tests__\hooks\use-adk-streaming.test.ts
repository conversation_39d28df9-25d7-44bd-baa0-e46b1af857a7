/**
 * Tests for ADK EventSource streaming hook
 * 
 * Requirements covered: 2.2, 2.4, 5.3
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { useADKStreaming } from '@/hooks/use-adk-streaming';
import { StreamingChunk, ChatError } from '@/types/adk';

// Mock EventSource
class MockEventSource {
  public onopen: ((event: Event) => void) | null = null;
  public onmessage: ((event: MessageEvent) => void) | null = null;
  public onerror: ((event: Event) => void) | null = null;
  public readyState: number = EventSource.CONNECTING;
  public url: string;
  public withCredentials: boolean;

  private listeners: { [key: string]: ((event: any) => void)[] } = {};

  constructor(url: string, options?: { withCredentials?: boolean }) {
    this.url = url;
    this.withCredentials = options?.withCredentials || false;
    
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = EventSource.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 10);
  }

  addEventListener(type: string, listener: (event: any) => void) {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    this.listeners[type].push(listener);
  }

  removeEventListener(type: string, listener: (event: any) => void) {
    if (this.listeners[type]) {
      this.listeners[type] = this.listeners[type].filter(l => l !== listener);
    }
  }

  close() {
    this.readyState = EventSource.CLOSED;
  }

  // Test helper methods
  simulateMessage(data: string) {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data }));
    }
  }

  simulateError() {
    this.readyState = EventSource.CLOSED;
    if (this.onerror) {
      this.onerror(new Event('error'));
    }
  }

  simulateCustomEvent(type: string, data: any) {
    if (this.listeners[type]) {
      this.listeners[type].forEach(listener => {
        listener({ data: JSON.stringify(data) });
      });
    }
  }
}

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock EventSource
(global as any).EventSource = MockEventSource;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('useADKStreaming', () => {
  let mockEventSource: MockEventSource;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('test-token');
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true })
    });
  });

  afterEach(() => {
    if (mockEventSource) {
      mockEventSource.close();
    }
  });

  const defaultOptions = {
    userId: 'test-user',
    sessionId: 'test-session',
    agentName: 'test-agent'
  };

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useADKStreaming(defaultOptions));

    expect(result.current.isConnected).toBe(false);
    expect(result.current.isStreaming).toBe(false);
    expect(result.current.isReconnecting).toBe(false);
    expect(result.current.currentChunk).toBe(null);
    expect(result.current.error).toBe(null);
    expect(result.current.connectionAttempts).toBe(0);
  });

  it('should start streaming and establish connection', async () => {
    const onChunk = jest.fn();
    const onComplete = jest.fn();
    const onError = jest.fn();

    const { result } = renderHook(() => 
      useADKStreaming({
        ...defaultOptions,
        onChunk,
        onComplete,
        onError
      })
    );

    await act(async () => {
      await result.current.startStreaming('test-message-id', 'Hello, world!');
    });

    expect(result.current.isStreaming).toBe(true);
    expect(mockFetch).toHaveBeenCalledWith(
      'http://localhost:8000/api/chat/send-message-stream',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }),
        body: JSON.stringify({
          message: 'Hello, world!',
          session_id: 'test-session',
          user_id: 'test-user',
          agent_name: 'test-agent',
          message_id: 'test-message-id'
        })
      })
    );

    // Wait for EventSource to connect
    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });
  });

  it('should handle streaming chunks correctly', async () => {
    const onChunk = jest.fn();
    const { result } = renderHook(() => 
      useADKStreaming({
        ...defaultOptions,
        onChunk
      })
    );

    await act(async () => {
      await result.current.startStreaming('test-message-id', 'Hello');
    });

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    // Simulate receiving a streaming chunk
    const chunk: StreamingChunk = {
      content: 'Hello, ',
      done: false,
      message_id: 'test-message-id',
      metadata: { author: 'test-agent' }
    };

    act(() => {
      // Access the EventSource instance and simulate message
      const eventSourceConstructor = (global as any).EventSource;
      const instances = eventSourceConstructor.instances || [];
      if (instances.length > 0) {
        instances[instances.length - 1].simulateMessage(JSON.stringify(chunk));
      }
    });

    expect(onChunk).toHaveBeenCalledWith(chunk);
    expect(result.current.currentChunk).toEqual(chunk);
  });

  it('should handle streaming completion', async () => {
    const onComplete = jest.fn();
    const { result } = renderHook(() => 
      useADKStreaming({
        ...defaultOptions,
        onComplete
      })
    );

    await act(async () => {
      await result.current.startStreaming('test-message-id', 'Hello');
    });

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    // Simulate completion chunk
    const completionChunk: StreamingChunk = {
      content: 'world!',
      done: true,
      message_id: 'test-message-id',
      metadata: { author: 'test-agent' }
    };

    act(() => {
      const eventSourceConstructor = (global as any).EventSource;
      const instances = eventSourceConstructor.instances || [];
      if (instances.length > 0) {
        instances[instances.length - 1].simulateMessage(JSON.stringify(completionChunk));
      }
    });

    expect(onComplete).toHaveBeenCalledWith('test-message-id');
    expect(result.current.isStreaming).toBe(false);
  });

  it('should handle connection errors', async () => {
    const onError = jest.fn();
    const { result } = renderHook(() => 
      useADKStreaming({
        ...defaultOptions,
        onError,
        autoReconnect: false
      })
    );

    await act(async () => {
      await result.current.startStreaming('test-message-id', 'Hello');
    });

    // Simulate connection error
    act(() => {
      const eventSourceConstructor = (global as any).EventSource;
      const instances = eventSourceConstructor.instances || [];
      if (instances.length > 0) {
        instances[instances.length - 1].simulateError();
      }
    });

    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Streaming connection failed',
        error_code: 'STREAM_ERROR'
      })
    );
    expect(result.current.error).toBeTruthy();
  });

  it('should handle interruption', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ success: true })
    });

    const { result } = renderHook(() => useADKStreaming(defaultOptions));

    await act(async () => {
      await result.current.startStreaming('test-message-id', 'Hello');
    });

    await act(async () => {
      await result.current.interruptStreaming();
    });

    expect(mockFetch).toHaveBeenCalledWith(
      'http://localhost:8000/api/chat/interrupt',
      expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({
          session_id: 'test-session',
          message_id: 'test-message-id'
        })
      })
    );
    expect(result.current.isStreaming).toBe(false);
  });

  it('should handle missing required parameters', async () => {
    const onError = jest.fn();
    const { result } = renderHook(() => 
      useADKStreaming({
        userId: '',
        sessionId: undefined,
        onError
      })
    );

    await act(async () => {
      await result.current.startStreaming('test-message-id', 'Hello');
    });

    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Missing required parameters',
        error_code: 'MISSING_PARAMS'
      })
    );
  });

  it('should attempt reconnection on error when autoReconnect is enabled', async () => {
    jest.useFakeTimers();
    
    const onError = jest.fn();
    const { result } = renderHook(() => 
      useADKStreaming({
        ...defaultOptions,
        onError,
        autoReconnect: true,
        maxReconnectAttempts: 2,
        reconnectDelay: 1000
      })
    );

    await act(async () => {
      await result.current.startStreaming('test-message-id', 'Hello');
    });

    // Simulate error
    act(() => {
      const eventSourceConstructor = (global as any).EventSource;
      const instances = eventSourceConstructor.instances || [];
      if (instances.length > 0) {
        instances[instances.length - 1].simulateError();
      }
    });

    expect(result.current.isReconnecting).toBe(true);
    expect(result.current.connectionAttempts).toBe(1);

    // Fast-forward time to trigger reconnection
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    jest.useRealTimers();
  });

  it('should stop reconnection after max attempts', async () => {
    jest.useFakeTimers();
    
    const { result } = renderHook(() => 
      useADKStreaming({
        ...defaultOptions,
        autoReconnect: true,
        maxReconnectAttempts: 1,
        reconnectDelay: 100
      })
    );

    // Simulate multiple errors
    for (let i = 0; i < 2; i++) {
      await act(async () => {
        await result.current.startStreaming('test-message-id', 'Hello');
      });

      act(() => {
        const eventSourceConstructor = (global as any).EventSource;
        const instances = eventSourceConstructor.instances || [];
        if (instances.length > 0) {
          instances[instances.length - 1].simulateError();
        }
      });

      act(() => {
        jest.advanceTimersByTime(100);
      });
    }

    expect(result.current.connectionAttempts).toBe(2);
    expect(result.current.isReconnecting).toBe(false);

    jest.useRealTimers();
  });

  it('should clear error state', () => {
    const { result } = renderHook(() => useADKStreaming(defaultOptions));

    // Set an error state
    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBe(null);
  });

  it('should cleanup on unmount', () => {
    const { result, unmount } = renderHook(() => useADKStreaming(defaultOptions));

    act(() => {
      result.current.startStreaming('test-message-id', 'Hello');
    });

    unmount();

    // EventSource should be closed
    expect(result.current.isConnected).toBe(false);
    expect(result.current.isStreaming).toBe(false);
  });
});