import { NextRequest, NextResponse } from 'next/server';

/**
 * Health check endpoint for Cloud Run
 * Provides basic application health status
 */
export async function GET(request: NextRequest) {
  try {
    // Basic health check - can be extended with dependency checks
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'social-media-frontend',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: {
        used: process.memoryUsage().heapUsed,
        total: process.memoryUsage().heapTotal,
        external: process.memoryUsage().external,
        rss: process.memoryUsage().rss
      },
      checks: {
        node_version: process.version,
        api_url: process.env.NEXT_PUBLIC_API_URL || 'not configured'
      }
    };

    return NextResponse.json(healthData, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'social-media-frontend',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { 
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
  }
}

/**
 * Readiness check endpoint
 * Checks if the application is ready to serve traffic
 */
export async function HEAD(request: NextRequest) {
  try {
    // Quick readiness check - just verify basic functionality
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}