/**
 * Enhanced Chat Type Definitions
 * 
 * TypeScript interfaces for enhanced chat functionality with ADK integration.
 * These types extend the existing chat system to support ADK features.
 * 
 * Requirements covered: 1.2, 10.1, 10.3
 */

import { 
  MessageRole, 
  EnhancedChatMessage, 
  ChatSession, 
  StreamingChunk, 
  ADKAgentInfo,
  ChatError,
  ToolExecution,
  FunctionCall,
  FunctionResponse
} from './adk';

// Extended Chat Types
export interface ChatState {
  messages: EnhancedChatMessage[];
  currentSession: ChatSession | null;
  isLoading: boolean;
  isStreaming: boolean;
  error: ChatError | null;
  selectedAgent: string | null;
  availableAgents: ADKAgentInfo[];
  streamingMessage: Partial<EnhancedChatMessage> | null;
}

export interface ChatActions {
  sendMessage: (message: string) => Promise<void>;
  selectAgent: (agentName: string) => Promise<void>;
  createNewSession: () => Promise<void>;
  loadHistory: (sessionId: string) => Promise<void>;
  clearHistory: () => void;
  interruptStreaming: () => void;
  retryLastMessage: () => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
}

export interface ChatContextValue extends ChatState, ChatActions {
  userId: string;
  sessionId: string | null;
}

// Message Display Types
export interface MessageDisplayProps {
  message: EnhancedChatMessage;
  isStreaming?: boolean;
  showTimestamp?: boolean;
  showAgent?: boolean;
  showFunctionCalls?: boolean;
  onRetry?: () => void;
  onDelete?: () => void;
  className?: string;
}

export interface MessageListProps {
  messages: EnhancedChatMessage[];
  isLoading?: boolean;
  streamingMessage?: Partial<EnhancedChatMessage>;
  onMessageRetry?: (messageId: string) => void;
  onMessageDelete?: (messageId: string) => void;
  className?: string;
}

export interface MessageInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  showAgentSelector?: boolean;
  selectedAgent?: string;
  availableAgents?: ADKAgentInfo[];
  onAgentSelect?: (agentName: string) => void;
  className?: string;
}

// Streaming and Real-time Types
export interface StreamingIndicatorProps {
  isStreaming: boolean;
  agentName?: string;
  canInterrupt?: boolean;
  onInterrupt?: () => void;
  className?: string;
}

export interface TypingIndicatorProps {
  show: boolean;
  agentName?: string;
  message?: string;
  className?: string;
}

export interface ConnectionStatusProps {
  isConnected: boolean;
  isReconnecting?: boolean;
  lastError?: ChatError;
  onReconnect?: () => void;
  className?: string;
}

// Function Call and Tool Display Types
export interface FunctionCallCardProps {
  functionCall: FunctionCall;
  response?: FunctionResponse;
  execution?: ToolExecution;
  expanded?: boolean;
  onToggleExpanded?: () => void;
  className?: string;
}

export interface ToolExecutionListProps {
  executions: ToolExecution[];
  showDetails?: boolean;
  onExecutionClick?: (execution: ToolExecution) => void;
  className?: string;
}

export interface ToolExecutionDetailProps {
  execution: ToolExecution;
  onClose?: () => void;
  className?: string;
}

// Agent Selection and Management Types
export interface AgentCardProps {
  agent: ADKAgentInfo;
  isSelected?: boolean;
  onSelect?: () => void;
  showCapabilities?: boolean;
  showTools?: boolean;
  className?: string;
}

export interface AgentListProps {
  agents: ADKAgentInfo[];
  selectedAgent?: string;
  onAgentSelect: (agentName: string) => void;
  layout?: 'grid' | 'list';
  showSearch?: boolean;
  className?: string;
}

export interface AgentSwitchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentAgent?: string;
  availableAgents: ADKAgentInfo[];
  onAgentSelect: (agentName: string) => void;
  preserveHistory?: boolean;
  onPreserveHistoryChange?: (preserve: boolean) => void;
}

// Session Management Types
export interface SessionListProps {
  sessions: ChatSession[];
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onSessionDelete?: (sessionId: string) => void;
  onNewSession?: () => void;
  className?: string;
}

export interface SessionCardProps {
  session: ChatSession;
  isActive?: boolean;
  onSelect?: () => void;
  onDelete?: () => void;
  showPreview?: boolean;
  className?: string;
}

export interface NewSessionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  availableAgents: ADKAgentInfo[];
  onCreateSession: (agentName: string) => void;
  defaultAgent?: string;
}

// Chat Layout and UI Types
export interface ChatLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  showSidebar?: boolean;
  onToggleSidebar?: () => void;
  className?: string;
}

export interface ChatHeaderProps {
  currentAgent?: ADKAgentInfo;
  sessionInfo?: ChatSession;
  onAgentChange?: () => void;
  onNewSession?: () => void;
  onSessionHistory?: () => void;
  showConnectionStatus?: boolean;
  className?: string;
}

export interface ChatSidebarProps {
  sessions: ChatSession[];
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onNewSession: () => void;
  onSessionDelete?: (sessionId: string) => void;
  isCollapsed?: boolean;
  className?: string;
}

// Error and Status Types
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export interface ChatErrorDisplayProps {
  error: ChatError;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  className?: string;
}

export interface LoadingStateProps {
  message?: string;
  showSpinner?: boolean;
  className?: string;
}

// Settings and Configuration Types
export interface ChatSettingsProps {
  settings: ChatSettings;
  onSettingsChange: (settings: Partial<ChatSettings>) => void;
  className?: string;
}

export interface ChatSettings {
  theme: 'light' | 'dark' | 'auto';
  fontSize: 'small' | 'medium' | 'large';
  showTimestamps: boolean;
  showAgentNames: boolean;
  showFunctionCalls: boolean;
  enableNotifications: boolean;
  autoScrollToBottom: boolean;
  maxMessagesInView: number;
  streamingEnabled: boolean;
  defaultAgent?: string;
}

// Event Types
export interface ChatEventHandlers {
  onMessageSent?: (message: EnhancedChatMessage) => void;
  onMessageReceived?: (message: EnhancedChatMessage) => void;
  onStreamingStart?: (messageId: string) => void;
  onStreamingChunk?: (chunk: StreamingChunk) => void;
  onStreamingComplete?: (messageId: string) => void;
  onStreamingError?: (error: ChatError) => void;
  onAgentChanged?: (agentName: string) => void;
  onSessionChanged?: (sessionId: string) => void;
  onError?: (error: ChatError) => void;
  onReconnect?: () => void;
}

// Utility Types
export interface ChatMetrics {
  totalMessages: number;
  averageResponseTime: number;
  successRate: number;
  activeAgents: string[];
  sessionDuration: number;
  functionCallsCount: number;
}

export interface ChatAnalytics {
  messagesSent: number;
  messagesReceived: number;
  averageMessageLength: number;
  mostUsedAgent: string;
  totalStreamingTime: number;
  errorRate: number;
}

// Search and Filter Types
export interface MessageSearchProps {
  onSearch: (query: string) => void;
  onFilter: (filters: MessageFilters) => void;
  placeholder?: string;
  className?: string;
}

export interface MessageFilters {
  role?: MessageRole;
  agentName?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  hasErrors?: boolean;
  hasFunctionCalls?: boolean;
  searchQuery?: string;
}

export interface SearchResult {
  message: EnhancedChatMessage;
  matchScore: number;
  matchedText: string;
}

// Export and Import Types
export interface ChatExportOptions {
  format: 'json' | 'csv' | 'txt';
  includeMetadata: boolean;
  includeFunctionCalls: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  agentFilter?: string[];
}

export interface ChatImportOptions {
  format: 'json';
  preserveIds: boolean;
  mergeWithExisting: boolean;
  validateMessages: boolean;
}

// Accessibility Types
export interface A11yProps {
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-selected'?: boolean;
  role?: string;
  tabIndex?: number;
}

// Performance Types
export interface PerformanceMetrics {
  renderTime: number;
  messageLoadTime: number;
  streamingLatency: number;
  memoryUsage: number;
  networkRequests: number;
}

export interface OptimizationSettings {
  virtualScrolling: boolean;
  lazyLoadImages: boolean;
  debounceTyping: number;
  maxCachedMessages: number;
  preloadNextPage: boolean;
}