#!/usr/bin/env python3
"""
Test Google Search and Grounding Capabilities
Tests the enhanced Google Search agent and News Content agent for latest news research.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add agents directory to path
agents_dir = os.path.join(os.path.dirname(__file__), 'agents')
sys.path.insert(0, agents_dir)

async def test_google_search_agent():
    """Test the enhanced Google Search agent."""
    print("🔍 Testing Google Search Agent...")
    
    try:
        # Import the Google Search agent
        from google_search_agent.agent import root_agent as search_agent
        
        print(f"✅ Google Search Agent loaded: {search_agent.name}")
        print(f"   Model: {search_agent.model}")
        print(f"   Tools: {len(search_agent.tools)}")
        
        # Test with a simple query (this would require actual ADK runtime)
        print("   Agent is ready for Google Search queries")
        return True
        
    except Exception as e:
        print(f"❌ Error testing Google Search agent: {e}")
        return False

async def test_news_content_agent():
    """Test the News Content agent."""
    print("\n📰 Testing News Content Agent...")
    
    try:
        # Import the News Content agent
        from news_content_agent.agent import root_agent as news_agent
        
        print(f"✅ News Content Agent loaded: {news_agent.name}")
        print(f"   Model: {news_agent.model}")
        print(f"   Sub-agents: {len(news_agent.sub_agents)}")
        for sub_agent in news_agent.sub_agents:
            print(f"     • {sub_agent.name}")
        print(f"   Tools: {len(news_agent.tools)}")
        
        print("   Agent is ready for news research + content creation")
        return True
        
    except Exception as e:
        print(f"❌ Error testing News Content agent: {e}")
        return False

async def test_main_coordinator():
    """Test the main coordinator with news capabilities."""
    print("\n🎯 Testing Main Coordinator with News Capabilities...")
    
    try:
        # Import the main coordinator
        from agent import root_agent as coordinator
        
        print(f"✅ Main Coordinator loaded: {coordinator.name}")
        print(f"   Model: {coordinator.model}")
        print(f"   Sub-agents: {len(coordinator.sub_agents)}")
        for sub_agent in coordinator.sub_agents:
            print(f"     • {sub_agent.name}")
        print(f"   Tools: {len(coordinator.tools)}")
        
        # Check if news content agent is included
        news_agent_found = any(
            'news' in agent.name.lower() 
            for agent in coordinator.sub_agents
        )
        
        if news_agent_found:
            print("   ✅ News Content capabilities integrated")
        else:
            print("   ⚠️ News Content agent not found in sub-agents")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Main Coordinator: {e}")
        return False

def check_environment():
    """Check if environment is properly configured for Google Search."""
    print("🔧 Checking Environment Configuration...")
    
    # Check for Google API key
    google_api_key = os.getenv('GOOGLE_API_KEY')
    use_vertex_ai = os.getenv('USE_VERTEX_AI', 'false').lower() == 'true'
    gcp_project = os.getenv('GCP_PROJECT_ID')
    
    if google_api_key:
        print("   ✅ GOOGLE_API_KEY found")
    elif use_vertex_ai and gcp_project:
        print("   ✅ Vertex AI configuration found")
        print(f"      Project: {gcp_project}")
    else:
        print("   ⚠️ No Google API configuration found")
        print("      Set GOOGLE_API_KEY for Google AI Studio")
        print("      Or set USE_VERTEX_AI=true and GCP_PROJECT_ID for Vertex AI")
    
    # Check ADK installation
    try:
        import google.adk
        print(f"   ✅ Google ADK installed: {google.adk.__version__}")
    except ImportError:
        print("   ❌ Google ADK not installed")
        return False
    
    # Check for google_search tool
    try:
        from google.adk.tools import google_search
        print("   ✅ google_search tool available")
    except ImportError:
        print("   ❌ google_search tool not available")
        return False
    
    return True

async def main():
    """Main test function."""
    print("🚀 Google Search and Grounding Capabilities Test")
    print("=" * 50)
    
    # Check environment first
    env_ok = check_environment()
    if not env_ok:
        print("\n❌ Environment check failed. Please configure your environment.")
        return
    
    # Test individual components
    search_ok = await test_google_search_agent()
    news_ok = await test_news_content_agent()
    coordinator_ok = await test_main_coordinator()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Google Search Agent: {'✅ PASS' if search_ok else '❌ FAIL'}")
    print(f"   News Content Agent: {'✅ PASS' if news_ok else '❌ FAIL'}")
    print(f"   Main Coordinator: {'✅ PASS' if coordinator_ok else '❌ FAIL'}")
    
    if all([search_ok, news_ok, coordinator_ok]):
        print("\n🎉 All tests passed! Your agents are ready for Google Search and grounding.")
        print("\n💡 Usage Examples:")
        print("   • 'Give me the latest news on AI and create an Instagram post'")
        print("   • 'Research trending topics in social media marketing'")
        print("   • 'Find recent developments in [your industry] and suggest content ideas'")
        print("   • 'What's the latest news on [competitor] and how can we respond?'")
    else:
        print("\n⚠️ Some tests failed. Check the errors above and fix the issues.")

if __name__ == "__main__":
    asyncio.run(main())