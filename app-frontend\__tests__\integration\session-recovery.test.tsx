import React, { ReactNode } from 'react';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSessionPersistence } from '@/hooks/use-session-persistence';
import { useChatHistory } from '@/hooks/use-chat-history';

// Mock fetch
global.fetch = jest.fn();

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        retryDelay: 0,
        staleTime: 0,
        cacheTime: 0,
      },
    },
    logger: {
      log: () => {},
      warn: () => {},
      error: () => {},
    },
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Session Recovery Integration', () => {
  const mockUser = {
    userId: 'test-user',
    agentName: 'test-agent',
  };

  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
    localStorage.clear();
    sessionStorage.clear();
  });

  it('should integrate session persistence with chat history recovery', async () => {
    // Mock successful chat history response
    const mockMessages = [
      {
        id: 'msg-1',
        session_id: 'recovered-session',
        role: 'user',
        content: 'Hello',
        user_id: 'test-user',
        timestamp: new Date().toISOString(),
        agent_name: 'test-agent',
      },
    ];

    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({
        messages: mockMessages,
        total_count: 1,
        has_more: false,
      }),
    });

    // First, create a session persistence hook
    const { result: sessionResult } = renderHook(
      () => useSessionPersistence(mockUser),
      { wrapper: createWrapper() }
    );

    // Save a session
    act(() => {
      sessionResult.current.saveSession('recovered-session', { test: 'data' });
    });

    expect(sessionResult.current.sessionId).toBe('recovered-session');
    expect(sessionResult.current.hasStoredSession).toBe(true);

    // Now create a chat history hook that should use the recovered session
    const { result: historyResult } = renderHook(
      () => useChatHistory({
        sessionId: sessionResult.current.sessionId || undefined,
        userId: mockUser.userId,
        agentName: mockUser.agentName,
      }),
      { wrapper: createWrapper() }
    );

    // Wait for chat history to load
    await waitFor(() => {
      expect(historyResult.current.isLoading).toBe(false);
    });

    // Verify that chat history was fetched with the recovered session ID
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('session_id=recovered-session'),
      expect.any(Object)
    );

    expect(historyResult.current.messages).toEqual(mockMessages);
  });

  it('should handle session recovery failure gracefully', async () => {
    // Mock failed chat history response
    (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    const { result: sessionResult } = renderHook(
      () => useSessionPersistence(mockUser),
      { wrapper: createWrapper() }
    );

    // Save a session
    act(() => {
      sessionResult.current.saveSession('failed-session');
    });

    // Create chat history hook with the session
    const { result: historyResult } = renderHook(
      () => useChatHistory({
        sessionId: sessionResult.current.sessionId || undefined,
        userId: mockUser.userId,
        agentName: mockUser.agentName,
      }),
      { wrapper: createWrapper() }
    );

    // Wait for error state
    await waitFor(() => {
      expect(historyResult.current.isError).toBe(true);
    });

    // Session should still be preserved even if history fetch fails
    expect(sessionResult.current.sessionId).toBe('failed-session');
    expect(sessionResult.current.hasStoredSession).toBe(true);
  });

  it('should clear session and history together', async () => {
    const { result: sessionResult } = renderHook(
      () => useSessionPersistence(mockUser),
      { wrapper: createWrapper() }
    );

    const { result: historyResult } = renderHook(
      () => useChatHistory({
        sessionId: sessionResult.current.sessionId || undefined,
        userId: mockUser.userId,
        agentName: mockUser.agentName,
      }),
      { wrapper: createWrapper() }
    );

    // Save a session
    act(() => {
      sessionResult.current.saveSession('clear-test-session');
    });

    expect(sessionResult.current.sessionId).toBe('clear-test-session');

    // Clear the session
    act(() => {
      sessionResult.current.clearSession();
    });

    expect(sessionResult.current.sessionId).toBe(null);
    expect(sessionResult.current.hasStoredSession).toBe(false);

    // Clear history cache
    act(() => {
      historyResult.current.clearHistory();
    });

    expect(historyResult.current.messages).toEqual([]);
  });

  it('should handle multiple sessions correctly', async () => {
    const { result } = renderHook(
      () => useSessionPersistence(mockUser),
      { wrapper: createWrapper() }
    );

    // Save multiple sessions
    act(() => {
      result.current.saveSession('session-1', { order: 1 });
    });

    act(() => {
      result.current.saveSession('session-2', { order: 2 });
    });

    act(() => {
      result.current.saveSession('session-3', { order: 3 });
    });

    const storedSessions = result.current.getStoredSessions();
    expect(storedSessions).toHaveLength(3);
    
    // Sessions should be ordered by most recent first
    expect(storedSessions[0].sessionId).toBe('session-3');
    expect(storedSessions[1].sessionId).toBe('session-2');
    expect(storedSessions[2].sessionId).toBe('session-1');

    // Current session should be the last one saved
    expect(result.current.sessionId).toBe('session-3');
  });
});