#!/usr/bin/env python3
"""
BigQuery Setup Script for Social Media Manager
This script initializes the BigQuery dataset and tables for analytics.
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add the app directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

from app.services.bigquery_service import BigQueryService
from app.core.config import Settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main setup function"""
    print("🚀 BigQuery Setup for Social Media Manager")
    print("=" * 50)
    
    # Initialize settings
    settings = Settings()
    
    # Display configuration
    print(f"Project ID: {settings.bigquery_project_id}")
    print(f"Dataset ID: {settings.bigquery_dataset_id}")
    print()
    
    # Initialize BigQuery service
    bq_service = BigQueryService()
    
    # Health check first
    print("🔍 Performing health check...")
    health_status = await bq_service.health_check()
    
    if health_status["status"] == "error":
        print(f"❌ BigQuery health check failed: {health_status['message']}")
        print("\nPlease ensure:")
        print("1. Google Cloud credentials are properly configured")
        print("2. BigQuery API is enabled")
        print("3. Service account has necessary permissions")
        return False
    
    print(f"✅ Health check passed: {health_status['message']}")
    print()
    
    # Setup tables
    print("🏗️  Setting up BigQuery tables...")
    success = await bq_service.setup_tables()
    
    if success:
        print("✅ BigQuery setup completed successfully!")
        print()
        print("📊 Created tables:")
        tables = [
            "user_metrics_daily",
            "account_metrics_daily", 
            "content_performance",
            "engagement_events",
            "platform_insights",
            "ai_predictions",
            "chat_analytics"
        ]
        
        for table in tables:
            print(f"  • {table}")
        
        print()
        print("🔗 Access your data:")
        print(f"  BigQuery Console: https://console.cloud.google.com/bigquery?project={settings.bigquery_project_id}")
        print(f"  Dataset: {settings.bigquery_project_id}.{settings.bigquery_dataset_id}")
        
        return True
    else:
        print("❌ BigQuery setup failed!")
        print("\nCommon issues:")
        print("1. Insufficient permissions (need BigQuery Admin or Data Editor)")
        print("2. Project billing not enabled")
        print("3. BigQuery API not enabled")
        return False

def validate_prerequisites():
    """Validate prerequisites before setup"""
    print("🔍 Validating prerequisites...")
    
    try:
        from google.cloud import bigquery
        print("✅ Google Cloud BigQuery library installed")
    except ImportError:
        print("❌ Google Cloud BigQuery library not found")
        print("Install with: pip install google-cloud-bigquery")
        return False
    
    # Check if settings are configured
    settings = Settings()
    
    if not settings.bigquery_project_id:
        print("❌ BigQuery project ID not configured")
        print("Set BIGQUERY_PROJECT_ID environment variable")
        return False
    
    print(f"✅ Project ID configured: {settings.bigquery_project_id}")
    
    if not settings.bigquery_dataset_id:
        print("⚠️  Dataset ID not configured, using default: social_media_analytics")
    
    print("✅ Prerequisites validated")
    print()
    return True

if __name__ == "__main__":
    if not validate_prerequisites():
        sys.exit(1)
    
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Setup failed with error: {e}")
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)