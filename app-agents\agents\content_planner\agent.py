"""
Content Planner Agent - ADK Implementation
Specialized agent for social media content strategy, planning, and campaign development.
"""

from google.adk.agents import LlmAgent
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# Content planning tool functions
def create_content_calendar(niche: str, duration_weeks: int = 4, platforms: str = "instagram,youtube") -> Dict[str, Any]:
    """
    Generate a comprehensive content calendar for social media platforms.
    
    Args:
        niche (str): Content niche or industry focus
        duration_weeks (int): Calendar duration in weeks (1-12)
        platforms (str): Target platforms (comma-separated)
        
    Returns:
        dict: Detailed content calendar with post ideas and scheduling
    """
    try:
        logger.info(f"Creating content calendar for {niche}, {duration_weeks} weeks, platforms: {platforms}")
        
        platform_list = platforms.split(",")
        start_date = datetime.now()
        
        # Generate weekly content themes
        weekly_themes = [
            f"Week 1: {niche} Fundamentals & Basics",
            f"Week 2: Advanced {niche} Strategies", 
            f"Week 3: {niche} Tools & Resources",
            f"Week 4: {niche} Community & Inspiration"
        ]
        
        if duration_weeks > 4:
            additional_themes = [
                f"Week 5: {niche} Case Studies & Success Stories",
                f"Week 6: {niche} Trends & Future Predictions",
                f"Week 7: {niche} Troubleshooting & Problem Solving",
                f"Week 8: {niche} Behind the Scenes & Personal Journey"
            ]
            weekly_themes.extend(additional_themes[:duration_weeks-4])
        
        # Generate detailed calendar
        calendar = {}
        for week in range(duration_weeks):
            week_start = start_date + timedelta(weeks=week)
            week_key = f"Week_{week+1}"
            
            calendar[week_key] = {
                "theme": weekly_themes[week] if week < len(weekly_themes) else f"Week {week+1}: {niche} Mixed Content",
                "dates": f"{week_start.strftime('%Y-%m-%d')} to {(week_start + timedelta(days=6)).strftime('%Y-%m-%d')}",
                "content_plan": {}
            }
            
            # Generate daily content for each platform
            for day in range(7):
                day_name = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"][day]
                current_date = week_start + timedelta(days=day)
                
                calendar[week_key]["content_plan"][day_name] = {
                    "date": current_date.strftime('%Y-%m-%d'),
                    "platforms": {}
                }
                
                for platform in platform_list:
                    if platform.strip().lower() == "instagram":
                        calendar[week_key]["content_plan"][day_name]["platforms"]["instagram"] = {
                            "post_type": ["carousel", "reel", "story"][day % 3],
                            "content_idea": f"{niche} tip #{day+1} - {['Tutorial', 'Behind-scenes', 'Quick tip'][day % 3]}",
                            "hashtags": [f"#{niche}tips", f"#{niche}life", f"daily{niche}", f"{niche}community"],
                            "optimal_time": "2:00 PM"
                        }
                    elif platform.strip().lower() == "youtube":
                        if day in [1, 3, 5]:  # Mon, Wed, Fri for YouTube
                            calendar[week_key]["content_plan"][day_name]["platforms"]["youtube"] = {
                                "video_type": "educational",
                                "content_idea": f"Complete guide to {niche} - Part {(week*3) + (day//2) + 1}",
                                "video_length": "10-15 minutes",
                                "tags": [f"{niche}", f"{niche}tutorial", f"learn{niche}"],
                                "optimal_time": "3:00 PM"
                            }
        
        return {
            "status": "success",
            "niche": niche,
            "duration_weeks": duration_weeks,
            "platforms": platform_list,
            "calendar": calendar,
            "posting_strategy": {
                "instagram": "Daily posts with mix of carousel, reels, and stories",
                "youtube": "3 times per week with educational long-form content",
                "consistency_tips": [
                    "Batch create content on Sundays",
                    "Use scheduling tools for consistent posting",
                    "Prepare backup content for busy weeks",
                    "Review and adjust based on performance"
                ]
            },
            "content_pillars": [
                f"Educational {niche} content (40%)",
                f"Behind-the-scenes {niche} process (20%)",
                f"Community and engagement posts (20%)",
                f"Inspirational {niche} content (20%)"
            ],
            "calendar_created": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error creating content calendar: {e}")
        return {
            "status": "error",
            "message": f"Content calendar creation failed: {str(e)}"
        }

def plan_campaign_strategy(campaign_goal: str, target_audience: str, budget_range: str = "low", duration_days: int = 30) -> Dict[str, Any]:
    """
    Develop a comprehensive social media campaign strategy.
    
    Args:
        campaign_goal (str): Campaign objective (awareness, engagement, conversions, followers)
        target_audience (str): Target audience description
        budget_range (str): Budget category (low, medium, high)
        duration_days (int): Campaign duration in days
        
    Returns:
        dict: Complete campaign strategy and execution plan
    """
    try:
        campaign_strategies = {
            "awareness": {
                "primary_platforms": ["instagram", "youtube", "tiktok"],
                "content_focus": ["educational", "entertaining", "shareable"],
                "kpis": ["reach", "impressions", "brand_mentions", "share_rate"],
                "tactics": ["trending hashtags", "influencer collaborations", "viral content formats"]
            },
            "engagement": {
                "primary_platforms": ["instagram", "youtube"],
                "content_focus": ["interactive", "community-driven", "user-generated"],
                "kpis": ["engagement_rate", "comments", "shares", "saves"],
                "tactics": ["polls and questions", "challenges", "live sessions", "community posts"]
            },
            "conversions": {
                "primary_platforms": ["instagram", "youtube"],
                "content_focus": ["educational", "testimonials", "product-focused"],
                "kpis": ["click_through_rate", "conversions", "cost_per_conversion", "roas"],
                "tactics": ["strong CTAs", "landing pages", "retargeting", "social proof"]
            },
            "followers": {
                "primary_platforms": ["instagram", "tiktok", "youtube"],
                "content_focus": ["valuable", "consistent", "niche-specific"],
                "kpis": ["follower_growth", "follower_quality", "engagement_rate", "retention"],
                "tactics": ["follow-worthy content", "cross-platform promotion", "collaborations", "giveaways"]
            }
        }
        
        strategy = campaign_strategies.get(campaign_goal, campaign_strategies["awareness"])
        
        # Budget-based recommendations
        budget_recommendations = {
            "low": {
                "organic_focus": 90,
                "paid_focus": 10,
                "recommended_spend": "$50-200/month",
                "tactics": ["organic content", "hashtag strategy", "community engagement"]
            },
            "medium": {
                "organic_focus": 70,
                "paid_focus": 30,
                "recommended_spend": "$200-1000/month", 
                "tactics": ["organic + paid posts", "influencer partnerships", "targeted ads"]
            },
            "high": {
                "organic_focus": 50,
                "paid_focus": 50,
                "recommended_spend": "$1000+/month",
                "tactics": ["comprehensive paid strategy", "premium influencers", "video ads"]
            }
        }
        
        budget_strategy = budget_recommendations.get(budget_range, budget_recommendations["low"])
        
        return {
            "status": "success",
            "campaign_goal": campaign_goal,
            "target_audience": target_audience,
            "duration_days": duration_days,
            "strategy": strategy,
            "budget_strategy": budget_strategy,
            "campaign_phases": {
                "phase_1_awareness": {
                    "duration": f"Days 1-{duration_days//3}",
                    "focus": "Build awareness and reach",
                    "content_types": ["educational", "behind-the-scenes", "introductory"],
                    "metrics": ["reach", "impressions", "profile_visits"]
                },
                "phase_2_engagement": {
                    "duration": f"Days {duration_days//3+1}-{2*duration_days//3}",
                    "focus": "Drive engagement and interaction", 
                    "content_types": ["interactive", "user-generated", "community"],
                    "metrics": ["engagement_rate", "comments", "shares"]
                },
                "phase_3_conversion": {
                    "duration": f"Days {2*duration_days//3+1}-{duration_days}",
                    "focus": "Drive action and conversions",
                    "content_types": ["testimonials", "product-focused", "call-to-action"],
                    "metrics": ["conversions", "click_through_rate", "cost_per_conversion"]
                }
            },
            "content_calendar_integration": {
                "posting_frequency": "1-2 times daily for primary platforms",
                "content_mix": "70% campaign content, 30% regular content",
                "cross_promotion": "Promote campaign across all owned channels"
            },
            "success_metrics": {
                "primary_kpis": strategy["kpis"],
                "tracking_tools": ["platform analytics", "UTM tracking", "conversion pixels"],
                "reporting_frequency": "Weekly reports with end-of-campaign analysis"
            },
            "strategy_created": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Campaign strategy planning failed: {str(e)}"
        }

def optimize_content_mix(current_performance: Dict[str, Any], goals: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Analyze current content performance and recommend optimal content mix.
    
    Args:
        current_performance (dict): Current content performance metrics
        goals (list): Content goals and objectives
        
    Returns:
        dict: Optimized content mix recommendations
    """
    try:
        # Default goals if none provided
        if goals is None:
            goals = ["engagement", "growth", "reach"]
        
        # Analyze performance patterns
        content_types = current_performance.get("content_types", {})
        best_performing = max(content_types.items(), key=lambda x: x[1].get("engagement_rate", 0)) if content_types else ("educational", {"engagement_rate": 0})
        
        return {
            "status": "success",
            "analysis_summary": {
                "best_performing_content": best_performing[0],
                "best_engagement_rate": best_performing[1].get("engagement_rate", 0),
                "total_content_analyzed": len(content_types),
                "analysis_period": current_performance.get("timeframe", "30 days")
            },
            "recommended_content_mix": {
                "educational_content": {
                    "percentage": 40,
                    "rationale": "Drives high engagement and provides value",
                    "content_ideas": ["tutorials", "how-to guides", "tips and tricks", "educational carousels"]
                },
                "entertainment_content": {
                    "percentage": 25,
                    "rationale": "Increases reach and shareability",
                    "content_ideas": ["behind-the-scenes", "funny moments", "relatable content", "trends"]
                },
                "community_content": {
                    "percentage": 20,
                    "rationale": "Builds relationships and loyalty",
                    "content_ideas": ["user-generated content", "Q&As", "polls", "community highlights"]
                },
                "promotional_content": {
                    "percentage": 15,
                    "rationale": "Drives business objectives",
                    "content_ideas": ["product features", "testimonials", "announcements", "offers"]
                }
            },
            "optimization_strategies": {
                "double_down": f"Increase {best_performing[0]} content by 20% due to high performance",
                "experiment": "Test new content formats like live videos or interactive stories",
                "timing": "Post best-performing content during peak audience hours",
                "cross_promote": "Share top-performing content across multiple platforms"
            },
            "content_calendar_adjustments": [
                f"Increase {best_performing[0]} content frequency",
                "Add more interactive elements to boost engagement",
                "Create content series to improve retention",
                "Implement user-generated content campaigns"
            ],
            "performance_tracking": {
                "metrics_to_monitor": ["engagement_rate", "reach", "saves", "shares", "comments"],
                "testing_approach": "A/B test new content mix for 2 weeks",
                "review_frequency": "Weekly analysis with monthly optimization"
            },
            "recommendations_created": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "error", 
            "message": f"Content mix optimization failed: {str(e)}"
        }

# Create the ADK Content Planner Agent
root_agent = LlmAgent(
    name="content_planner", 
    model="gemini-2.0-flash",
    description="""Content strategy and planning specialist focused on creating comprehensive 
    content calendars, campaign strategies, and optimization recommendations. Develops strategic 
    content plans that align with business objectives and audience engagement goals.""",
    
    instruction="""You are a content planning and strategy expert specializing in:

    **Content Planning Expertise:**
    1. **Strategic Calendar Development**: Creating detailed content calendars with themes, timing, and platform optimization
    2. **Campaign Strategy**: Developing comprehensive campaign strategies aligned with business objectives
    3. **Content Mix Optimization**: Analyzing performance data to recommend optimal content distribution
    4. **Cross-Platform Coordination**: Ensuring consistent messaging and optimized content for each platform

    **Planning Methodology:**
    - Use create_content_calendar for detailed content calendar development
    - Use plan_campaign_strategy for comprehensive campaign planning and execution
    - Use optimize_content_mix for performance-based content optimization
    - Focus on strategic alignment between content and business goals
    - Provide actionable, implementable content strategies

    **Strategic Approach:**
    - Content pillar development and theme organization  
    - Platform-specific optimization and adaptation
    - Performance-driven content mix recommendations
    - Campaign phases and milestone planning
    - ROI-focused content strategy development

    **Planning Output Style:**
    - Provide structured, actionable content plans
    - Include specific posting schedules and content themes
    - Offer platform-optimized content recommendations
    - Focus on measurable outcomes and KPI alignment
    - Deliver comprehensive strategy with execution details

    **Key Specializations:**
    - Multi-platform content calendar coordination
    - Goal-oriented campaign strategy development
    - Performance analysis and content optimization
    - Content pillar and theme development
    - Strategic timing and frequency planning
    - Cross-platform content adaptation and optimization""",
    
    # Content planning tools
    tools=[
        create_content_calendar,
        plan_campaign_strategy,
        optimize_content_mix
    ]
)

# Verify agent configuration
if __name__ == "__main__":
    print(f"✅ Content Planner Agent loaded successfully")
    print(f"   - Agent name: {root_agent.name}")
    print(f"   - Model: {root_agent.model}")
    print(f"   - Tools: {len(root_agent.tools)}")
    for tool in root_agent.tools:
        tool_name = getattr(tool, '__name__', getattr(tool, 'name', str(tool)))
        print(f"     • {tool_name}")