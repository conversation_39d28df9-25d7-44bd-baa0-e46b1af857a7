"""
Comprehensive tests for ADK error handling system.

This module tests the error handling hierarchy, middleware, and integration
with services to ensure proper error handling and fallback behavior.
"""

import pytest
import json
import asyncio
from unittest.mock import AsyncMock, Mock, patch
from datetime import datetime
from typing import Dict, Any

import httpx
from fastapi import HTTPException, status
from fastapi.testclient import TestClient
from starlette.requests import Request
from starlette.responses import Response

from app.core.error_handling import (
    ADKIntegrationError,
    ADKServerUnavailableError,
    ADKAgentNotFoundError,
    ADKSessionError,
    ADKStreamingError,
    ADKTimeoutError,
    ADKAuthenticationError,
    ADKValidationError,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rrorSeverity,
    handle_adk_error,
    create_error_stream
)
from app.core.middleware import (
    ADKErrorHandlingMiddleware,
    ADKHealthCheckMiddleware
)
from app.models.chat_models import StreamingChunk


class TestADKIntegrationErrorHierarchy:
    """Test the ADK error hierarchy and base functionality"""
    
    def test_base_adk_integration_error(self):
        """Test base ADK integration error creation and serialization"""
        error = ADKIntegrationError(
            message="Test error",
            error_code="TEST_ERROR",
            severity=ErrorSeverity.HIGH,
            details={"test_key": "test_value"},
            original_error=ValueError("Original error")
        )
        
        assert error.message == "Test error"
        assert error.error_code == "TEST_ERROR"
        assert error.severity == ErrorSeverity.HIGH
        assert error.details == {"test_key": "test_value"}
        assert isinstance(error.original_error, ValueError)
        
        # Test serialization
        error_dict = error.to_dict()
        assert error_dict["error_type"] == "ADKIntegrationError"
        assert error_dict["message"] == "Test error"
        assert error_dict["error_code"] == "TEST_ERROR"
        assert error_dict["severity"] == "high"
        assert error_dict["details"] == {"test_key": "test_value"}
        assert "Original error" in error_dict["original_error"]
    
    def test_server_unavailable_error(self):
        """Test ADK server unavailable error"""
        error = ADKServerUnavailableError(
            message="Custom unavailable message",
            details={"server_url": "http://localhost:8001"},
            original_error=httpx.ConnectError("Connection failed")
        )
        
        assert error.message == "Custom unavailable message"
        assert error.error_code == "ADK_SERVER_UNAVAILABLE"
        assert error.severity == ErrorSeverity.HIGH
        assert error.details["server_url"] == "http://localhost:8001"
        assert isinstance(error.original_error, httpx.ConnectError)
    
    def test_server_unavailable_error_defaults(self):
        """Test ADK server unavailable error with defaults"""
        error = ADKServerUnavailableError()
        
        assert error.message == "ADK server is currently unavailable"
        assert error.error_code == "ADK_SERVER_UNAVAILABLE"
        assert error.severity == ErrorSeverity.HIGH
    
    def test_agent_not_found_error(self):
        """Test ADK agent not found error"""
        available_agents = ["agent1", "agent2", "agent3"]
        error = ADKAgentNotFoundError(
            agent_name="missing_agent",
            available_agents=available_agents,
            original_error=KeyError("Agent not found")
        )
        
        assert "missing_agent" in error.message
        assert error.error_code == "ADK_AGENT_NOT_FOUND"
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.details["agent_name"] == "missing_agent"
        assert error.details["available_agents"] == available_agents
    
    def test_session_error(self):
        """Test ADK session error"""
        error = ADKSessionError(
            message="Session creation failed",
            session_id="test_session_123",
            user_id="test_user_456",
            original_error=Exception("Database error")
        )
        
        assert error.message == "Session creation failed"
        assert error.error_code == "ADK_SESSION_ERROR"
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.details["session_id"] == "test_session_123"
        assert error.details["user_id"] == "test_user_456"
    
    def test_streaming_error(self):
        """Test ADK streaming error"""
        event_data = {"content": {"parts": [{"text": "test"}]}}
        error = ADKStreamingError(
            message="Streaming failed",
            event_data=event_data,
            original_error=json.JSONDecodeError("Invalid JSON", "", 0)
        )
        
        assert error.message == "Streaming failed"
        assert error.error_code == "ADK_STREAMING_ERROR"
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.details["event_data"] == event_data
    
    def test_timeout_error(self):
        """Test ADK timeout error"""
        error = ADKTimeoutError(
            message="Request timed out after 30 seconds",
            timeout_seconds=30.0,
            original_error=asyncio.TimeoutError()
        )
        
        assert "30 seconds" in error.message
        assert error.error_code == "ADK_TIMEOUT"
        assert error.severity == ErrorSeverity.HIGH
        assert error.details["timeout_seconds"] == 30.0
    
    def test_authentication_error(self):
        """Test ADK authentication error"""
        error = ADKAuthenticationError(
            message="Invalid credentials",
            user_id="test_user",
            original_error=Exception("Auth failed")
        )
        
        assert error.message == "Invalid credentials"
        assert error.error_code == "ADK_AUTH_ERROR"
        assert error.severity == ErrorSeverity.HIGH
        assert error.details["user_id"] == "test_user"
    
    def test_validation_error(self):
        """Test ADK validation error"""
        validation_errors = ["Field 'name' is required", "Field 'age' must be positive"]
        error = ADKValidationError(
            message="Validation failed",
            validation_errors=validation_errors,
            original_error=ValueError("Invalid data")
        )
        
        assert error.message == "Validation failed"
        assert error.error_code == "ADK_VALIDATION_ERROR"
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.details["validation_errors"] == validation_errors


class TestErrorHandler:
    """Test the ErrorHandler class functionality"""
    
    @pytest.fixture
    def error_handler(self):
        """Create error handler for testing"""
        return ErrorHandler()
    
    def test_get_user_friendly_message_server_unavailable(self, error_handler):
        """Test user-friendly message for server unavailable error"""
        error = ADKServerUnavailableError()
        message = error_handler.get_user_friendly_message(error)
        
        assert "temporarily unavailable" in message.lower()
        assert "try again" in message.lower()
    
    def test_get_user_friendly_message_agent_not_found(self, error_handler):
        """Test user-friendly message for agent not found error"""
        available_agents = ["agent1", "agent2"]
        error = ADKAgentNotFoundError(
            agent_name="missing_agent",
            available_agents=available_agents
        )
        message = error_handler.get_user_friendly_message(error)
        
        assert "not available" in message.lower()
        assert "agent1" in message
        assert "agent2" in message
    
    def test_get_user_friendly_message_agent_not_found_no_available(self, error_handler):
        """Test user-friendly message for agent not found without available agents"""
        error = ADKAgentNotFoundError(agent_name="missing_agent")
        message = error_handler.get_user_friendly_message(error)
        
        assert "not available" in message.lower()
        assert "default assistant" in message.lower()
    
    def test_get_user_friendly_message_session_error(self, error_handler):
        """Test user-friendly message for session error"""
        error = ADKSessionError(message="Session failed")
        message = error_handler.get_user_friendly_message(error)
        
        assert "conversation session" in message.lower()
        assert "new session" in message.lower()
    
    def test_get_user_friendly_message_streaming_error(self, error_handler):
        """Test user-friendly message for streaming error"""
        error = ADKStreamingError(message="Stream failed")
        message = error_handler.get_user_friendly_message(error)
        
        assert "response stream" in message.lower()
        assert "try sending" in message.lower()
    
    def test_get_user_friendly_message_timeout_error(self, error_handler):
        """Test user-friendly message for timeout error"""
        error = ADKTimeoutError()
        message = error_handler.get_user_friendly_message(error)
        
        assert "longer than expected" in message.lower()
        assert "try again" in message.lower()
    
    def test_get_user_friendly_message_authentication_error(self, error_handler):
        """Test user-friendly message for authentication error"""
        error = ADKAuthenticationError()
        message = error_handler.get_user_friendly_message(error)
        
        assert "authentication failed" in message.lower()
        assert "refresh" in message.lower()
    
    def test_get_user_friendly_message_validation_error(self, error_handler):
        """Test user-friendly message for validation error"""
        error = ADKValidationError(message="Invalid request")
        message = error_handler.get_user_friendly_message(error)
        
        assert "request format" in message.lower()
        assert "try again" in message.lower()
    
    def test_get_user_friendly_message_generic_error(self, error_handler):
        """Test user-friendly message for generic error"""
        error = ADKIntegrationError(message="Unknown error")
        message = error_handler.get_user_friendly_message(error)
        
        assert "unexpected issue" in message.lower()
        assert "try again" in message.lower()
    
    def test_create_error_response(self, error_handler):
        """Test error response creation"""
        error = ADKServerUnavailableError(
            message="Server down",
            details={"server": "localhost:8001"}
        )
        
        response = error_handler.create_error_response(error)
        
        assert response["error"] is True
        assert "temporarily unavailable" in response["message"].lower()
        assert response["error_code"] == "ADK_SERVER_UNAVAILABLE"
        assert response["severity"] == "high"
    
    def test_create_error_response_with_fallback(self, error_handler):
        """Test error response creation with fallback message"""
        error = ADKIntegrationError(message="Test error")
        fallback_message = "Custom fallback message"
        
        response = error_handler.create_error_response(error, fallback_message)
        
        assert response["message"] == fallback_message
    
    def test_create_error_response_generic_exception(self, error_handler):
        """Test error response creation from generic exception"""
        generic_error = ValueError("Generic error")
        
        response = error_handler.create_error_response(generic_error)
        
        assert response["error"] is True
        assert "unexpected issue" in response["message"].lower()
        assert response["error_code"] == "ADK_ERROR"
    
    def test_create_error_stream_chunk(self, error_handler):
        """Test error streaming chunk creation"""
        error = ADKStreamingError(message="Stream failed")
        
        chunk = error_handler.create_error_stream_chunk(error, "test_message_id")
        
        assert isinstance(chunk, StreamingChunk)
        assert chunk.done is True
        assert chunk.message_id == "test_message_id"
        assert chunk.metadata["error"] is True
        assert chunk.metadata["error_code"] == "ADK_STREAMING_ERROR"
        assert chunk.metadata["severity"] == "medium"
    
    def test_create_http_exception(self, error_handler):
        """Test HTTP exception creation"""
        error = ADKServerUnavailableError()
        
        http_exc = error_handler.create_http_exception(error)
        
        assert isinstance(http_exc, HTTPException)
        assert http_exc.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        assert http_exc.detail["error"] is True
        assert http_exc.detail["error_code"] == "ADK_SERVER_UNAVAILABLE"
    
    def test_create_http_exception_agent_not_found(self, error_handler):
        """Test HTTP exception creation for agent not found"""
        error = ADKAgentNotFoundError(agent_name="missing")
        
        http_exc = error_handler.create_http_exception(error)
        
        assert http_exc.status_code == status.HTTP_404_NOT_FOUND
    
    def test_create_http_exception_authentication_error(self, error_handler):
        """Test HTTP exception creation for authentication error"""
        error = ADKAuthenticationError()
        
        http_exc = error_handler.create_http_exception(error)
        
        assert http_exc.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_create_http_exception_validation_error(self, error_handler):
        """Test HTTP exception creation for validation error"""
        error = ADKValidationError(message="Invalid data")
        
        http_exc = error_handler.create_http_exception(error)
        
        assert http_exc.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_create_http_exception_timeout_error(self, error_handler):
        """Test HTTP exception creation for timeout error"""
        error = ADKTimeoutError()
        
        http_exc = error_handler.create_http_exception(error)
        
        assert http_exc.status_code == status.HTTP_408_REQUEST_TIMEOUT


class TestConvenienceFunctions:
    """Test convenience functions for error handling"""
    
    def test_handle_adk_error_function(self):
        """Test handle_adk_error convenience function"""
        error = ADKServerUnavailableError()
        
        response = handle_adk_error(error)
        
        assert response["error"] is True
        assert response["error_code"] == "ADK_SERVER_UNAVAILABLE"
        assert "temporarily unavailable" in response["message"].lower()
    
    def test_handle_adk_error_with_fallback(self):
        """Test handle_adk_error with fallback message"""
        error = ADKIntegrationError(message="Test error")
        fallback = "Custom fallback"
        
        response = handle_adk_error(error, fallback)
        
        assert response["message"] == fallback
    
    def test_create_error_stream_function(self):
        """Test create_error_stream convenience function"""
        error = ADKStreamingError(message="Stream error")
        
        stream_data = create_error_stream(error, "test_id")
        
        assert stream_data.startswith("data: ")
        assert stream_data.endswith("\n\n")
        
        # Parse the JSON data
        json_data = stream_data[6:-2]  # Remove "data: " and "\n\n"
        chunk_data = json.loads(json_data)
        
        assert chunk_data["done"] is True
        assert chunk_data["message_id"] == "test_id"
        assert chunk_data["metadata"]["error"] is True


class TestADKErrorHandlingMiddleware:
    """Test the ADK error handling middleware"""
    
    @pytest.fixture
    def mock_app(self):
        """Create mock ASGI app"""
        async def app(scope, receive, send):
            if scope["path"] == "/error":
                raise ADKServerUnavailableError("Test error")
            elif scope["path"] == "/generic_error":
                raise ValueError("Generic error")
            else:
                response = Response("OK", status_code=200)
                await response(scope, receive, send)
        return app
    
    @pytest.fixture
    def middleware(self, mock_app):
        """Create middleware instance"""
        return ADKErrorHandlingMiddleware(mock_app, enable_fallback=True)
    
    @pytest.mark.asyncio
    async def test_middleware_handles_adk_error(self, middleware):
        """Test middleware handles ADK integration errors"""
        # Create mock request
        request = Mock(spec=Request)
        request.url.path = "/error"
        request.headers = {}
        request.state = Mock()
        
        # Mock call_next to raise ADK error
        async def call_next(req):
            raise ADKServerUnavailableError("Test error")
        
        response = await middleware.dispatch(request, call_next)
        
        assert response.status_code == 503
        assert "application/json" in response.media_type
    
    @pytest.mark.asyncio
    async def test_middleware_handles_streaming_error(self, middleware):
        """Test middleware handles streaming endpoint errors"""
        # Create mock streaming request
        request = Mock(spec=Request)
        request.url.path = "/stream"
        request.headers = {"Accept": "text/event-stream"}
        request.state = Mock()
        
        # Mock call_next to raise ADK error
        async def call_next(req):
            raise ADKStreamingError("Stream error")
        
        response = await middleware.dispatch(request, call_next)
        
        assert response.media_type == "text/event-stream"
        assert hasattr(response, 'body_iterator')
    
    @pytest.mark.asyncio
    async def test_middleware_wraps_generic_errors(self, middleware):
        """Test middleware wraps ADK-related generic errors"""
        request = Mock(spec=Request)
        request.url.path = "/test"
        request.headers = {}
        request.state = Mock()
        
        # Mock call_next to raise generic error with ADK keywords
        async def call_next(req):
            raise ConnectionError("ADK connection failed")
        
        response = await middleware.dispatch(request, call_next)
        
        assert response.status_code == 503  # Should be wrapped as server unavailable
    
    @pytest.mark.asyncio
    async def test_middleware_ignores_non_adk_errors(self, middleware):
        """Test middleware ignores non-ADK related errors"""
        request = Mock(spec=Request)
        request.url.path = "/test"
        request.headers = {}
        request.state = Mock()
        
        # Mock call_next to raise non-ADK error
        async def call_next(req):
            raise ValueError("Regular validation error")
        
        with pytest.raises(ValueError):
            await middleware.dispatch(request, call_next)
    
    def test_middleware_detects_streaming_endpoints(self, middleware):
        """Test middleware correctly detects streaming endpoints"""
        # Test path-based detection
        request1 = Mock(spec=Request)
        request1.url.path = "/api/stream"
        request1.headers = {}
        assert middleware._is_streaming_endpoint(request1) is True
        
        # Test header-based detection
        request2 = Mock(spec=Request)
        request2.url.path = "/api/chat"
        request2.headers = {"Accept": "text/event-stream"}
        assert middleware._is_streaming_endpoint(request2) is True
        
        # Test non-streaming endpoint
        request3 = Mock(spec=Request)
        request3.url.path = "/api/status"
        request3.headers = {}
        assert middleware._is_streaming_endpoint(request3) is False
    
    def test_middleware_detects_adk_related_errors(self, middleware):
        """Test middleware correctly detects ADK-related errors"""
        # Test ADK-related errors
        adk_error = Exception("ADK server connection failed")
        assert middleware._is_adk_related_error(adk_error) is True
        
        timeout_error = Exception("Request timeout waiting for agent")
        assert middleware._is_adk_related_error(timeout_error) is True
        
        session_error = Exception("Session management failed")
        assert middleware._is_adk_related_error(session_error) is True
        
        # Test non-ADK errors
        generic_error = Exception("Database connection failed")
        assert middleware._is_adk_related_error(generic_error) is False
    
    def test_middleware_wraps_generic_errors_correctly(self, middleware):
        """Test middleware wraps generic errors with appropriate ADK error types"""
        # Test timeout error wrapping
        timeout_error = Exception("Request timeout")
        wrapped = middleware._wrap_generic_error(timeout_error)
        assert isinstance(wrapped, ADKTimeoutError)
        
        # Test connection error wrapping
        connection_error = Exception("Connection refused")
        wrapped = middleware._wrap_generic_error(connection_error)
        assert isinstance(wrapped, ADKServerUnavailableError)
        
        # Test generic error wrapping
        generic_error = Exception("Unknown error")
        wrapped = middleware._wrap_generic_error(generic_error)
        assert isinstance(wrapped, ADKIntegrationError)


class TestADKHealthCheckMiddleware:
    """Test the ADK health check middleware with circuit breaker"""
    
    @pytest.fixture
    def mock_app(self):
        """Create mock ASGI app"""
        async def app(scope, receive, send):
            response = Response("OK", status_code=200)
            await response(scope, receive, send)
        return app
    
    @pytest.fixture
    def health_middleware(self, mock_app):
        """Create health check middleware instance"""
        return ADKHealthCheckMiddleware(
            mock_app,
            health_check_interval=1,  # Short interval for testing
            failure_threshold=2,      # Low threshold for testing
            recovery_timeout=5        # Short timeout for testing
        )
    
    def test_middleware_detects_adk_endpoints(self, health_middleware):
        """Test middleware correctly detects ADK endpoints"""
        # Test ADK endpoints
        request1 = Mock(spec=Request)
        request1.url.path = "/api/chat/send"
        assert health_middleware._is_adk_endpoint(request1) is True
        
        request2 = Mock(spec=Request)
        request2.url.path = "/api/agents/list"
        assert health_middleware._is_adk_endpoint(request2) is True
        
        request3 = Mock(spec=Request)
        request3.url.path = "/api/sessions/create"
        assert health_middleware._is_adk_endpoint(request3) is True
        
        # Test non-ADK endpoints
        request4 = Mock(spec=Request)
        request4.url.path = "/api/health"
        assert health_middleware._is_adk_endpoint(request4) is False
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_opens_after_failures(self, health_middleware):
        """Test circuit breaker opens after threshold failures"""
        request = Mock(spec=Request)
        request.url.path = "/api/chat/send"
        request.headers = {}
        
        # Simulate failures
        async def failing_call_next(req):
            raise ADKServerUnavailableError("Server down")
        
        # First failure
        with pytest.raises(ADKServerUnavailableError):
            await health_middleware.dispatch(request, failing_call_next)
        assert health_middleware.failure_count == 1
        assert health_middleware.circuit_open is False
        
        # Second failure - should open circuit
        with pytest.raises(ADKServerUnavailableError):
            await health_middleware.dispatch(request, failing_call_next)
        assert health_middleware.failure_count == 2
        assert health_middleware.circuit_open is True
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_blocks_requests_when_open(self, health_middleware):
        """Test circuit breaker blocks requests when open"""
        # Force circuit open
        health_middleware.circuit_open = True
        health_middleware.failure_count = 5
        
        request = Mock(spec=Request)
        request.url.path = "/api/chat/send"
        request.headers = {}
        
        async def normal_call_next(req):
            return Response("OK", status_code=200)
        
        response = await health_middleware.dispatch(request, normal_call_next)
        
        # Should return error response without calling next
        assert response.status_code == 503
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_allows_non_adk_requests(self, health_middleware):
        """Test circuit breaker allows non-ADK requests when open"""
        # Force circuit open
        health_middleware.circuit_open = True
        health_middleware.failure_count = 5
        
        request = Mock(spec=Request)
        request.url.path = "/api/health"  # Non-ADK endpoint
        request.headers = {}
        
        async def normal_call_next(req):
            return Response("OK", status_code=200)
        
        response = await health_middleware.dispatch(request, normal_call_next)
        
        # Should allow the request through
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_resets_on_success(self, health_middleware):
        """Test circuit breaker resets failure count on successful ADK request"""
        # Set some failures
        health_middleware.failure_count = 1
        
        request = Mock(spec=Request)
        request.url.path = "/api/chat/send"
        request.headers = {}
        
        async def successful_call_next(req):
            return Response("OK", status_code=200)
        
        response = await health_middleware.dispatch(request, successful_call_next)
        
        # Should reset failure count
        assert response.status_code == 200
        assert health_middleware.failure_count == 0
    
    def test_circuit_breaker_streaming_endpoint_detection(self, health_middleware):
        """Test circuit breaker correctly detects streaming endpoints"""
        request1 = Mock(spec=Request)
        request1.url.path = "/api/stream"
        request1.headers = {}
        assert health_middleware._is_streaming_endpoint(request1) is True
        
        request2 = Mock(spec=Request)
        request2.url.path = "/api/chat"
        request2.headers = {"Accept": "text/event-stream"}
        assert health_middleware._is_streaming_endpoint(request2) is True
        
        request3 = Mock(spec=Request)
        request3.url.path = "/api/status"
        request3.headers = {}
        assert health_middleware._is_streaming_endpoint(request3) is False


class TestIntegrationWithServices:
    """Test error handling integration with ADK services"""
    
    @pytest.mark.asyncio
    async def test_adk_service_error_propagation(self):
        """Test that ADK service properly propagates error handling"""
        from app.services.adk_service import ADKService
        
        # Mock HTTP client to simulate server unavailable
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.get.side_effect = httpx.ConnectError("Connection failed")
            
            adk_service = ADKService(base_url="http://localhost:8001")
            
            with pytest.raises(ADKServerUnavailableError) as exc_info:
                await adk_service.list_agents()
            
            error = exc_info.value
            assert error.error_code == "ADK_SERVER_UNAVAILABLE"
            assert isinstance(error.original_error, httpx.ConnectError)
    
    @pytest.mark.asyncio
    async def test_session_manager_error_handling(self):
        """Test session manager error handling"""
        from app.services.session_manager import SessionManager
        
        # Mock ADK service to raise errors
        mock_adk_service = AsyncMock()
        mock_adk_service.create_session.side_effect = ADKServerUnavailableError()
        
        session_manager = SessionManager(mock_adk_service)
        
        with pytest.raises(ADKServerUnavailableError):
            await session_manager.get_or_create_session(
                user_id="test_user",
                agent_name="test_agent"
            )
    
    def test_event_transformer_error_handling(self):
        """Test event transformer handles malformed events gracefully"""
        from app.services.event_transformer import ADKEventTransformer
        
        transformer = ADKEventTransformer()
        
        # Test with malformed event
        malformed_event = {"invalid": "structure"}
        
        # Should not raise exception, but handle gracefully
        try:
            chunk = transformer.transform_event_to_chunk(malformed_event)
            # Should create a valid chunk even with malformed input
            assert hasattr(chunk, 'content')
            assert hasattr(chunk, 'done')
            assert hasattr(chunk, 'message_id')
        except Exception as e:
            # If it does raise, it should be an ADK error
            assert isinstance(e, ADKIntegrationError)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])