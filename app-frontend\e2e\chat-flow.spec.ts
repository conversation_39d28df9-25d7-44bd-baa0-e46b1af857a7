import { test, expect } from '@playwright/test';

test.describe('Chat Flow E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the backend API for consistent testing
    await page.route('**/api/chat', async (route) => {
      const request = route.request();
      const body = JSON.parse(request.postData() || '{}');
      
      // Simulate streaming response
      const streamResponse = new ReadableStream({
        start(controller) {
          const chunks = [
            'data: {"content": "Hello! I\'m your social media manager AI assistant. "}\n\n',
            'data: {"content": "I can help you analyze your social media performance, "}\n\n',
            'data: {"content": "create content plans, and provide insights. "}\n\n',
            'data: {"content": "What would you like to know about your accounts?"}\n\n',
            'data: {"done": true}\n\n'
          ];
          
          let index = 0;
          const interval = setInterval(() => {
            if (index < chunks.length) {
              controller.enqueue(new TextEncoder().encode(chunks[index++]));
            } else {
              clearInterval(interval);
              controller.close();
            }
          }, 100);
        }
      });

      await route.fulfill({
        status: 200,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
        body: streamResponse,
      });
    });

    // Mock account data API
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: [
            {
              id: 'yt-test',
              platform: 'youtube',
              handle: '@testchannel',
              avatar: 'https://example.com/avatar.jpg',
              lastSync: '2024-01-01T12:00:00Z',
              metrics: {
                followers: 15420,
                engagement: 4.2,
                reach: 50000,
                growth: {
                  followers: 7.8,
                  engagement: 2.1,
                },
              },
            }
          ]
        })
      });
    });

    // Navigate to the application
    await page.goto('/');
  });

  test('should display welcome message when no chat history exists', async ({ page }) => {
    // Check for welcome elements
    await expect(page.getByText('Welcome to your Social Media Manager')).toBeVisible();
    await expect(page.getByText('I\'m here to help you analyze your social media performance')).toBeVisible();
    
    // Check for quick action buttons
    await expect(page.getByRole('button', { name: /analyze youtube/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /check instagram/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /content plan/i })).toBeVisible();
  });

  test('should send message when user types and clicks send', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Initially send button should be disabled
    await expect(sendButton).toBeDisabled();
    
    // Type a message
    await messageInput.fill('Tell me about my YouTube performance');
    
    // Send button should now be enabled
    await expect(sendButton).toBeEnabled();
    
    // Click send
    await sendButton.click();
    
    // Verify user message appears
    await expect(page.getByText('Tell me about my YouTube performance')).toBeVisible();
    
    // Verify AI response appears (streaming)
    await expect(page.getByText('Hello! I\'m your social media manager AI assistant.')).toBeVisible({ timeout: 10000 });
    await expect(page.getByText('What would you like to know about your accounts?')).toBeVisible({ timeout: 10000 });
    
    // Input should be cleared
    await expect(messageInput).toHaveValue('');
  });

  test('should send message when user presses Enter', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    
    // Type a message and press Enter
    await messageInput.fill('How is my engagement rate?');
    await messageInput.press('Enter');
    
    // Verify message was sent
    await expect(page.getByText('How is my engagement rate?')).toBeVisible();
    await expect(page.getByText('Hello! I\'m your social media manager AI assistant.')).toBeVisible({ timeout: 10000 });
  });

  test('should not send message when user presses Shift+Enter', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    
    // Type a message and press Shift+Enter
    await messageInput.fill('First line');
    await messageInput.press('Shift+Enter');
    await messageInput.type('Second line');
    
    // Message should not be sent, text should contain newline
    await expect(messageInput).toHaveValue('First line\nSecond line');
    
    // No messages should appear in chat
    await expect(page.getByText('First line')).not.toBeVisible();
  });

  test('should populate input when quick action button is clicked', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const youtubeButton = page.getByRole('button', { name: /analyze youtube/i });
    
    // Click YouTube quick action
    await youtubeButton.click();
    
    // Input should be populated
    await expect(messageInput).toHaveValue('Analyze my YouTube performance');
  });

  test('should show typing indicator during response', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Send a message
    await messageInput.fill('Test message');
    await sendButton.click();
    
    // Look for typing indicator
    await expect(page.getByText('Analyzing...')).toBeVisible({ timeout: 5000 });
    
    // Typing indicator should disappear when response is complete
    await expect(page.getByText('Analyzing...')).not.toBeVisible({ timeout: 15000 });
  });

  test('should display timestamps for messages', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Send a message
    await messageInput.fill('Check timestamp');
    await sendButton.click();
    
    // Wait for messages to appear
    await expect(page.getByText('Check timestamp')).toBeVisible();
    
    // Look for timestamp pattern (e.g., "12:34 PM" or "12:34")
    await expect(page.locator('text=/\\d{1,2}:\\d{2}/')).toBeVisible({ timeout: 10000 });
  });

  test('should handle multiple messages in conversation', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Send first message
    await messageInput.fill('First message');
    await sendButton.click();
    
    // Wait for response
    await expect(page.getByText('Hello! I\'m your social media manager AI assistant.')).toBeVisible({ timeout: 10000 });
    
    // Send second message
    await messageInput.fill('Second message');
    await sendButton.click();
    
    // Both user messages should be visible
    await expect(page.getByText('First message')).toBeVisible();
    await expect(page.getByText('Second message')).toBeVisible();
  });

  test('should auto-scroll to bottom when new messages arrive', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Send multiple messages to create scroll
    for (let i = 1; i <= 3; i++) {
      await messageInput.fill(`Message ${i}`);
      await sendButton.click();
      await page.waitForTimeout(1000); // Wait between messages
    }
    
    // The last message should be visible (indicating auto-scroll)
    await expect(page.getByText('Message 3')).toBeVisible({ timeout: 10000 });
  });

  test('should handle textarea auto-resize', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    
    // Get initial height
    const initialHeight = await messageInput.evaluate(el => el.clientHeight);
    
    // Type a long message with line breaks
    const longMessage = 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5';
    await messageInput.fill(longMessage);
    
    // Height should increase
    const newHeight = await messageInput.evaluate(el => el.clientHeight);
    expect(newHeight).toBeGreaterThan(initialHeight);
  });

  test('should preserve message history on page refresh', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Send a message
    await messageInput.fill('Test persistence');
    await sendButton.click();
    
    // Wait for response
    await expect(page.getByText('Hello! I\'m your social media manager AI assistant.')).toBeVisible({ timeout: 10000 });
    
    // Refresh the page
    await page.reload();
    
    // Message history should persist (depending on implementation)
    // This might require localStorage or session storage
    // For now, we'll check that the page loads correctly
    await expect(page.getByText('Welcome to your Social Media Manager')).toBeVisible();
  });

  test('should display error message when API fails', async ({ page }) => {
    // Override the route to simulate API failure
    await page.route('**/api/chat', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Send a message
    await messageInput.fill('This will fail');
    await sendButton.click();
    
    // User message should appear
    await expect(page.getByText('This will fail')).toBeVisible();
    
    // Error handling should occur (message might be removed or error shown)
    // This depends on the implementation of error handling
    await page.waitForTimeout(2000);
  });

  test('should disable send button during message sending', async ({ page }) => {
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Type message
    await messageInput.fill('Testing send state');
    await expect(sendButton).toBeEnabled();
    
    // Click send
    await sendButton.click();
    
    // Button should be disabled during sending
    await expect(sendButton).toBeDisabled();
    
    // Wait for response to complete
    await expect(page.getByText('Hello! I\'m your social media manager AI assistant.')).toBeVisible({ timeout: 10000 });
    
    // Button should be enabled again (if input is not empty)
    // Since input is cleared, button should remain disabled
    await expect(sendButton).toBeDisabled();
  });

  test('should handle keyboard accessibility', async ({ page }) => {
    // Tab to the message input
    await page.keyboard.press('Tab');
    
    // Input should be focused
    const messageInput = page.getByRole('textbox');
    await expect(messageInput).toBeFocused();
    
    // Type and send with keyboard
    await page.keyboard.type('Keyboard test');
    await page.keyboard.press('Enter');
    
    // Message should be sent
    await expect(page.getByText('Keyboard test')).toBeVisible();
  });

  test('should handle responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Elements should still be visible and usable
    await expect(page.getByText('Welcome to your Social Media Manager')).toBeVisible();
    
    const messageInput = page.getByRole('textbox');
    const sendButton = page.getByRole('button', { name: /send/i });
    
    // Should be able to interact on mobile
    await messageInput.fill('Mobile test');
    await sendButton.click();
    
    await expect(page.getByText('Mobile test')).toBeVisible();
  });
});