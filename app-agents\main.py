from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

# Import chat router with error handling
try:
    from app.routers.chat_enhanced import router as chat_router
    CHAT_ROUTER_AVAILABLE = True
except Exception as e:
    print(f"Warning: Chat router not available: {e}")
    CHAT_ROUTER_AVAILABLE = False
    chat_router = None
from app.core.observability import observability, get_health_status
from app.core.feature_flags import get_feature_flags

load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    observability.logger.info("Starting Social Media Manager API")
    yield
    # Shutdown
    observability.logger.info("Shutting down Social Media Manager API")

app = FastAPI(
    title="Social Media Manager API",
    description="AI-powered social media management and analytics platform",
    version="1.0.0",
    lifespan=lifespan
)

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://yourdomain.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers (only if the chat router imported successfully)
if CHAT_ROUTER_AVAILABLE and chat_router is not None:
    app.include_router(chat_router, prefix="/api/chat", tags=["chat"])
else:
    # Chat router failed to import — log and continue without the router.
    # This allows the API to start for smoke testing even when ADK-related
    # optional dependencies are missing.
    observability.logger.info("Chat router not included: ADK/chat router unavailable")

@app.get("/")
async def root():
    return {"message": "Social Media Manager API", "version": "1.0.0"}

# Health check is now handled by the health router
# Basic fallback health endpoint
@app.get("/ping")
async def ping():
    return {"status": "pong"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )