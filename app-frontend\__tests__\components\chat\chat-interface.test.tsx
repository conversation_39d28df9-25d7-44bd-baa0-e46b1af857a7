import React from 'react'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ChatInterface } from '@/components/chat/chat-interface'
import { renderWithProviders, mockMessage, mockFetch, mockApiResponse } from '../utils/test-utils'

// Mock the custom hooks
jest.mock('@/hooks/use-chat-history', () => ({
  useChatHistory: jest.fn(),
}))

jest.mock('@/hooks/use-send-message', () => ({
  useSendMessage: jest.fn(),
}))

const mockUseChatHistory = require('@/hooks/use-chat-history').useChatHistory
const mockUseSendMessage = require('@/hooks/use-send-message').useSendMessage

describe('ChatInterface', () => {
  const mockSendMessage = jest.fn()
  
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseSendMessage.mockReturnValue({
      mutateAsync: mockSendMessage,
      isPending: false,
    })
  })

  describe('Initial State', () => {
    it('shows loading spinner when chat history is loading', () => {
      mockUseChatHistory.mockReturnValue({
        data: undefined,
        isLoading: true,
      })

      renderWithProviders(<ChatInterface />)
      
      expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument()
    })

    it('shows welcome message when no messages exist', () => {
      mockUseChatHistory.mockReturnValue({
        data: [],
        isLoading: false,
      })

      renderWithProviders(<ChatInterface />)
      
      expect(screen.getByText('Welcome to your Social Media Manager')).toBeInTheDocument()
      expect(screen.getByText(/I'm here to help you analyze/)).toBeInTheDocument()
    })

    it('shows quick action buttons in welcome state', () => {
      mockUseChatHistory.mockReturnValue({
        data: [],
        isLoading: false,
      })

      renderWithProviders(<ChatInterface />)
      
      expect(screen.getByRole('button', { name: /analyze youtube/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /check instagram/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /content plan/i })).toBeInTheDocument()
    })
  })

  describe('Message Display', () => {
    it('displays user and assistant messages correctly', () => {
      const messages = [
        mockMessage({ role: 'user', content: 'Hello there' }),
        mockMessage({ 
          id: 'assistant-1',
          role: 'assistant', 
          content: 'Hi! How can I help you today?',
          timestamp: new Date('2024-01-01T12:01:00Z')
        }),
      ]

      mockUseChatHistory.mockReturnValue({
        data: messages,
        isLoading: false,
      })

      renderWithProviders(<ChatInterface />)
      
      expect(screen.getByText('Hello there')).toBeInTheDocument()
      expect(screen.getByText('Hi! How can I help you today?')).toBeInTheDocument()
    })

    it('shows timestamps for messages', () => {
      const messages = [
        mockMessage({ 
          content: 'Test message',
          timestamp: new Date('2024-01-01T12:00:00Z')
        }),
      ]

      mockUseChatHistory.mockReturnValue({
        data: messages,
        isLoading: false,
      })

      renderWithProviders(<ChatInterface />)
      
      // Check for timestamp (format may vary based on locale)
      expect(screen.getByText(/12:00/)).toBeInTheDocument()
    })

    it('displays typing indicator when assistant is responding', () => {
      mockUseChatHistory.mockReturnValue({
        data: [],
        isLoading: false,
      })

      renderWithProviders(<ChatInterface />)
      
      // Simulate typing state by having pending mutation
      mockUseSendMessage.mockReturnValue({
        mutateAsync: mockSendMessage,
        isPending: true,
      })

      // Re-render with typing state
      renderWithProviders(<ChatInterface />)
      
      expect(screen.getByText('Analyzing...')).toBeInTheDocument()
    })
  })

  describe('User Interactions', () => {
    beforeEach(() => {
      mockUseChatHistory.mockReturnValue({
        data: [],
        isLoading: false,
      })
    })

    it('allows user to type in the input field', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ChatInterface />)
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'Test message')
      
      expect(input).toHaveValue('Test message')
    })

    it('sends message when send button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ChatInterface />)
      
      const input = screen.getByRole('textbox')
      const sendButton = screen.getByRole('button', { name: /send/i })
      
      await user.type(input, 'Test message')
      await user.click(sendButton)
      
      expect(mockSendMessage).toHaveBeenCalledWith('Test message')
    })

    it('sends message when Enter is pressed', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ChatInterface />)
      
      const input = screen.getByRole('textbox')
      
      await user.type(input, 'Test message')
      await user.keyboard('{Enter}')
      
      expect(mockSendMessage).toHaveBeenCalledWith('Test message')
    })

    it('does not send message when Shift+Enter is pressed', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ChatInterface />)
      
      const input = screen.getByRole('textbox')
      
      await user.type(input, 'Test message')
      await user.keyboard('{Shift>}{Enter}{/Shift}')
      
      expect(mockSendMessage).not.toHaveBeenCalled()
    })

    it('clears input after sending message', async () => {
      const user = userEvent.setup()
      mockSendMessage.mockResolvedValue(undefined)
      
      renderWithProviders(<ChatInterface />)
      
      const input = screen.getByRole('textbox')
      const sendButton = screen.getByRole('button', { name: /send/i })
      
      await user.type(input, 'Test message')
      await user.click(sendButton)
      
      await waitFor(() => {
        expect(input).toHaveValue('')
      })
    })

    it('disables send button when input is empty', () => {
      renderWithProviders(<ChatInterface />)
      
      const sendButton = screen.getByRole('button', { name: /send/i })
      expect(sendButton).toBeDisabled()
    })

    it('disables send button when message is being sent', () => {
      mockUseSendMessage.mockReturnValue({
        mutateAsync: mockSendMessage,
        isPending: true,
      })

      renderWithProviders(<ChatInterface />)
      
      const sendButton = screen.getByRole('button', { name: /send/i })
      expect(sendButton).toBeDisabled()
    })

    it('populates input when quick action button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ChatInterface />)
      
      const youtubeButton = screen.getByRole('button', { name: /analyze youtube/i })
      await user.click(youtubeButton)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveValue('Analyze my YouTube performance')
    })
  })

  describe('Error Handling', () => {
    beforeEach(() => {
      mockUseChatHistory.mockReturnValue({
        data: [],
        isLoading: false,
      })
    })

    it('handles send message errors gracefully', async () => {
      const user = userEvent.setup()
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {})
      
      mockSendMessage.mockRejectedValue(new Error('Network error'))
      
      renderWithProviders(<ChatInterface />)
      
      const input = screen.getByRole('textbox')
      const sendButton = screen.getByRole('button', { name: /send/i })
      
      await user.type(input, 'Test message')
      await user.click(sendButton)
      
      await waitFor(() => {
        expect(consoleError).toHaveBeenCalledWith('Failed to send message:', expect.any(Error))
      })
      
      consoleError.mockRestore()
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      mockUseChatHistory.mockReturnValue({
        data: [],
        isLoading: false,
      })
    })

    it('has proper ARIA labels and roles', () => {
      renderWithProviders(<ChatInterface />)
      
      expect(screen.getByRole('textbox')).toHaveAttribute('placeholder', expect.stringContaining('Ask me about'))
      expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument()
    })

    it('maintains focus management properly', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ChatInterface />)
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      expect(input).toHaveFocus()
    })
  })
})