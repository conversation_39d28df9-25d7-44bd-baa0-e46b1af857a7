/**
 * Tool Execution Detail Component Tests
 * 
 * Tests for the ToolExecutionDetail component that shows detailed information
 * about tool executions from ADK agents.
 * 
 * Requirements covered: 4.5, 10.2
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ToolExecutionDetail } from '../tool-execution-detail';
import { ToolExecution, FunctionCall, FunctionResponse } from '@/types/adk';

// Mock the UI components
jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children, className }: any) => (
    <div className={`dialog-content ${className}`}>{children}</div>
  ),
  DialogHeader: ({ children }: any) => <div className="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2 className="dialog-title">{children}</h2>,
  DialogClose: ({ children, asChild, ...props }: any) => 
    asChild ? React.cloneElement(children, props) : <button {...props}>{children}</button>
}));

jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>
      {React.Children.map(children, child => 
        React.cloneElement(child, { activeTab: value, onTabChange: onValueChange })
      )}
    </div>
  ),
  TabsList: ({ children }: any) => <div className="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value, activeTab, onTabChange }: any) => (
    <button 
      onClick={() => onTabChange?.(value)}
      className={activeTab === value ? 'active' : ''}
    >
      {children}
    </button>
  ),
  TabsContent: ({ children, value, activeTab }: any) => 
    activeTab === value ? <div className="tab-content">{children}</div> : null
}));

jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => (
    <div className={`card ${className}`}>{children}</div>
  ),
  CardContent: ({ children, className }: any) => (
    <div className={`card-content ${className}`}>{children}</div>
  ),
  CardHeader: ({ children, className }: any) => (
    <div className={`card-header ${className}`}>{children}</div>
  )
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className, ...props }: any) => (
    <button onClick={onClick} className={className} {...props}>
      {children}
    </button>
  )
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className }: any) => (
    <span className={`badge ${className}`}>{children}</span>
  )
}));

jest.mock('@/components/ui/scroll-area', () => ({
  ScrollArea: ({ children, className }: any) => (
    <div className={`scroll-area ${className}`}>{children}</div>
  )
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(() => Promise.resolve())
  }
});

describe('ToolExecutionDetail', () => {
  const mockExecution: ToolExecution = {
    id: 'exec_123',
    name: 'search_web',
    status: 'completed',
    input: {
      query: 'test search',
      limit: 10
    },
    output: {
      results: ['result1', 'result2'],
      count: 2
    },
    duration_ms: 1500
  };

  const mockFunctionCall: FunctionCall = {
    id: 'call_123',
    name: 'search_web',
    arguments: {
      query: 'test search',
      limit: 10
    }
  };

  const mockFunctionResponse: FunctionResponse = {
    id: 'call_123',
    name: 'search_web',
    content: 'Search completed successfully',
    success: true
  };

  const defaultProps = {
    execution: mockExecution,
    functionCall: mockFunctionCall,
    functionResponse: mockFunctionResponse,
    isOpen: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the dialog when open', () => {
    render(<ToolExecutionDetail {...defaultProps} />);
    
    expect(screen.getByTestId('dialog')).toBeInTheDocument();
    expect(screen.getByText('Tool Execution Details')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<ToolExecutionDetail {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });

  it('displays execution status and basic information', () => {
    render(<ToolExecutionDetail {...defaultProps} />);
    
    expect(screen.getByText('search_web')).toBeInTheDocument();
    expect(screen.getByText('exec_123')).toBeInTheDocument();
    expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    expect(screen.getByText('1.5s')).toBeInTheDocument();
  });

  it('shows error information for failed executions', () => {
    const failedExecution: ToolExecution = {
      ...mockExecution,
      status: 'failed',
      error: 'Network timeout error'
    };

    render(
      <ToolExecutionDetail 
        {...defaultProps} 
        execution={failedExecution}
      />
    );
    
    expect(screen.getByText('FAILED')).toBeInTheDocument();
    expect(screen.getByText('Network timeout error')).toBeInTheDocument();
  });

  it('displays function call information in overview tab', () => {
    render(<ToolExecutionDetail {...defaultProps} />);
    
    expect(screen.getByText('Function Call')).toBeInTheDocument();
    expect(screen.getByText('search_web')).toBeInTheDocument();
    expect(screen.getByText(/test search/)).toBeInTheDocument();
  });

  it('displays function response information in overview tab', () => {
    render(<ToolExecutionDetail {...defaultProps} />);
    
    expect(screen.getByText('Function Response')).toBeInTheDocument();
    expect(screen.getByText('Search completed successfully')).toBeInTheDocument();
    expect(screen.getByText('Success')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    const user = userEvent.setup();
    render(<ToolExecutionDetail {...defaultProps} />);
    
    // Should start on overview tab
    expect(screen.getByText('Function Call')).toBeInTheDocument();
    
    // Switch to input tab
    await user.click(screen.getByText('Input'));
    expect(screen.getByText('Input Data')).toBeInTheDocument();
    
    // Switch to output tab
    await user.click(screen.getByText('Output'));
    expect(screen.getByText('Output Data')).toBeInTheDocument();
    
    // Switch to raw tab
    await user.click(screen.getByText('Raw Data'));
    expect(screen.getByText('Raw Execution Data')).toBeInTheDocument();
  });

  it('displays input data in JSON format', async () => {
    const user = userEvent.setup();
    render(<ToolExecutionDetail {...defaultProps} />);
    
    await user.click(screen.getByText('Input'));
    
    expect(screen.getByText(/test search/)).toBeInTheDocument();
    expect(screen.getByText(/10/)).toBeInTheDocument();
  });

  it('displays output data in JSON format', async () => {
    const user = userEvent.setup();
    render(<ToolExecutionDetail {...defaultProps} />);
    
    await user.click(screen.getByText('Output'));
    
    expect(screen.getByText(/result1/)).toBeInTheDocument();
    expect(screen.getByText(/result2/)).toBeInTheDocument();
  });

  it('copies content when copy button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnCopy = jest.fn();
    
    render(
      <ToolExecutionDetail 
        {...defaultProps} 
        onCopy={mockOnCopy}
      />
    );
    
    // Find and click a copy button (there should be several)
    const copyButtons = screen.getAllByRole('button');
    const copyButton = copyButtons.find(button => 
      button.querySelector('svg') // Looking for copy icon
    );
    
    if (copyButton) {
      await user.click(copyButton);
      
      expect(navigator.clipboard.writeText).toHaveBeenCalled();
      expect(mockOnCopy).toHaveBeenCalled();
    }
  });

  it('calls onClose when close button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnClose = jest.fn();
    
    render(
      <ToolExecutionDetail 
        {...defaultProps} 
        onClose={mockOnClose}
      />
    );
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onExport when export button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnExport = jest.fn();
    
    render(
      <ToolExecutionDetail 
        {...defaultProps} 
        onExport={mockOnExport}
      />
    );
    
    const exportButton = screen.getByText('Export');
    await user.click(exportButton);
    
    expect(mockOnExport).toHaveBeenCalledWith(mockExecution);
  });

  it('handles execution without function call', () => {
    render(
      <ToolExecutionDetail 
        execution={mockExecution}
        isOpen={true}
      />
    );
    
    expect(screen.getByText('search_web')).toBeInTheDocument();
    expect(screen.queryByText('Function Call')).not.toBeInTheDocument();
  });

  it('handles execution without function response', () => {
    render(
      <ToolExecutionDetail 
        execution={mockExecution}
        functionCall={mockFunctionCall}
        isOpen={true}
      />
    );
    
    expect(screen.getByText('Function Call')).toBeInTheDocument();
    expect(screen.queryByText('Function Response')).not.toBeInTheDocument();
  });

  it('shows failed function response correctly', () => {
    const failedResponse: FunctionResponse = {
      ...mockFunctionResponse,
      success: false,
      error: 'API rate limit exceeded'
    };

    render(
      <ToolExecutionDetail 
        {...defaultProps}
        functionResponse={failedResponse}
      />
    );
    
    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByText('API rate limit exceeded')).toBeInTheDocument();
  });

  it('formats duration correctly for different time ranges', () => {
    const testCases = [
      { duration_ms: 500, expected: '500ms' },
      { duration_ms: 1500, expected: '1.5s' },
      { duration_ms: 65000, expected: '1.1m' }
    ];

    testCases.forEach(({ duration_ms, expected }) => {
      const execution = { ...mockExecution, duration_ms };
      const { rerender } = render(
        <ToolExecutionDetail 
          execution={execution}
          isOpen={true}
        />
      );
      
      expect(screen.getByText(expected)).toBeInTheDocument();
      
      rerender(<div />); // Clear for next test
    });
  });

  it('handles missing or undefined data gracefully', () => {
    const minimalExecution: ToolExecution = {
      id: 'minimal',
      name: 'minimal_tool',
      status: 'pending'
    };

    render(
      <ToolExecutionDetail 
        execution={minimalExecution}
        isOpen={true}
      />
    );
    
    expect(screen.getByText('minimal_tool')).toBeInTheDocument();
    expect(screen.getByText('PENDING')).toBeInTheDocument();
    expect(screen.getByText('N/A')).toBeInTheDocument(); // Duration should show N/A
  });

  it('applies custom className', () => {
    const { container } = render(
      <ToolExecutionDetail 
        {...defaultProps}
        className="custom-class"
      />
    );
    
    expect(container.querySelector('.dialog-content')).toHaveClass('custom-class');
  });

  it('shows copy confirmation when content is copied', async () => {
    const user = userEvent.setup();
    render(<ToolExecutionDetail {...defaultProps} />);
    
    // Find and click a copy button
    const copyButtons = screen.getAllByRole('button');
    const copyButton = copyButtons.find(button => 
      button.querySelector('svg')
    );
    
    if (copyButton) {
      await user.click(copyButton);
      
      // Should show confirmation message
      await waitFor(() => {
        expect(screen.getByText(/copied to clipboard/i)).toBeInTheDocument();
      });
    }
  });
});