# OAuth Implementation and Deployment Guide

This guide covers the complete setup and deployment of OAuth flows for YouTube and Instagram integration with secure token storage using Google Cloud Secret Manager.

## Overview

The OAuth implementation provides:
- ✅ **YouTube OAuth 2.0** with Google OAuth flows
- ✅ **Instagram Graph API OAuth** with Facebook OAuth
- ✅ **YouTube Read-only** connections via public URLs
- ✅ **Secure Token Storage** in Google Cloud Secret Manager
- ✅ **Automatic Token Refresh** for expired tokens
- ✅ **CSRF Protection** with state validation
- ✅ **Error Handling** and graceful fallbacks

## Architecture

### Components

1. **OAuthService** - Main OAuth orchestration
2. **SecretManagerService** - Secure credential storage
3. **Auth Router** - API endpoints for OAuth flows
4. **Platform Services** - YouTube and Instagram API integration
5. **Firestore** - Connection metadata storage

### Flow Diagrams

#### YouTube OAuth Flow
```
Frontend → Backend → Google OAuth → YouTube API → Secret Manager → Firestore
   ↓         ↓           ↓             ↓              ↓             ↓
1. Connect  2. Auth URL  3. User Auth  4. Get Channel 5. Store     6. Save
   Request     Generated    & Code       Data          Tokens       Connection
```

#### Instagram OAuth Flow
```
Frontend → Backend → Facebook OAuth → Instagram API → Secret Manager → Firestore
   ↓         ↓           ↓               ↓               ↓             ↓
1. Connect  2. Auth URL  3. User Auth   4. Get Business 5. Store     6. Save
   Request     Generated    & Code        Account Data   Tokens       Connection
```

## Prerequisites

### 1. Google Cloud Platform Setup

```bash
# Create or select GCP project
gcloud projects create social-media-manager-prod --name="Social Media Manager"
gcloud config set project social-media-manager-prod

# Enable required APIs
gcloud services enable secretmanager.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable youtube.googleapis.com

# Create service account
gcloud iam service-accounts create social-media-backend \
    --display-name="Social Media Backend Service"

# Grant necessary permissions
gcloud projects add-iam-policy-binding social-media-manager-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.admin"

gcloud projects add-iam-policy-binding social-media-manager-prod \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/datastore.user"

# Create and download service account key
gcloud iam service-accounts keys create ./service-account-key.json \
    --iam-account=<EMAIL>
```

### 2. YouTube API Setup

1. **Enable YouTube Data API v3**:
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Navigate to APIs & Services → Library
   - Search for "YouTube Data API v3" and enable it

2. **Create OAuth 2.0 Credentials**:
   - Go to APIs & Services → Credentials
   - Click "Create Credentials" → "OAuth client ID"
   - Application type: "Web application"
   - Name: "Social Media Manager YouTube"
   - Authorized redirect URIs:
     - `http://localhost:8000/api/auth/callback/youtube` (development)
     - `https://your-domain.com/api/auth/callback/youtube` (production)

3. **Create API Key** (for public data):
   - Click "Create Credentials" → "API key"
   - Restrict the key to YouTube Data API v3

### 3. Instagram API Setup

1. **Create Facebook App**:
   - Go to [Facebook Developers](https://developers.facebook.com)
   - Create new app → "Business" type
   - Add "Instagram Graph API" product

2. **Configure OAuth Settings**:
   - Go to Instagram Graph API → Settings
   - Add redirect URIs:
     - `http://localhost:8000/api/auth/callback/instagram` (development)
     - `https://your-domain.com/api/auth/callback/instagram` (production)

3. **Get App Credentials**:
   - Note your App ID and App Secret
   - Submit for Instagram Business review if needed

## Environment Configuration

### Development Environment

Create `.env` file in `app-agents` directory:

```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=social-media-manager-dev
GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json

# YouTube API
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
YOUTUBE_API_KEY=your-youtube-api-key

# Instagram API
INSTAGRAM_APP_ID=your-facebook-app-id
INSTAGRAM_APP_SECRET=your-facebook-app-secret

# Secret Manager
SECRET_KEY=your-secret-key-for-jwt-tokens

# Database
FIRESTORE_PROJECT_ID=social-media-manager-dev

# API URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000
```

### Production Environment

Use Google Cloud Secret Manager for production:

```bash
# Store sensitive configuration in Secret Manager
gcloud secrets create app-config --data-file=production-config.json

# Example production-config.json
{
  "google_client_id": "prod-google-oauth-client-id",
  "google_client_secret": "prod-google-oauth-client-secret",
  "youtube_api_key": "prod-youtube-api-key",
  "instagram_app_id": "prod-facebook-app-id",
  "instagram_app_secret": "prod-facebook-app-secret",
  "secret_key": "prod-secret-key",
  "frontend_url": "https://your-domain.com",
  "backend_url": "https://api.your-domain.com"
}
```

## Deployment Steps

### 1. Firestore Setup

```bash
# Deploy Firestore rules and indexes
cd path/to/project
firebase deploy --only firestore

# Verify Firestore setup
python scripts/setup_firestore.py
```

### 2. Secret Manager Setup

```bash
# Initialize Secret Manager
python -c "from app.services.secret_manager_service import SecretManagerService; 
import asyncio; 
asyncio.run(SecretManagerService().health_check())"
```

### 3. Application Deployment

#### Local Development

```bash
# Install dependencies
cd app-agents
pip install -r requirements.txt

# Run database seeder
python scripts/seed_database.py

# Start backend server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# In another terminal, start frontend
cd app-frontend
npm install
npm run dev
```

#### Production (Cloud Run)

```bash
# Build and deploy backend
gcloud run deploy social-media-backend \
    --source=./app-agents \
    --platform=managed \
    --region=us-central1 \
    --allow-unauthenticated \
    --set-env-vars="GOOGLE_CLOUD_PROJECT=social-media-manager-prod" \
    --service-account=<EMAIL>

# Deploy frontend
cd app-frontend
npm run build
gcloud run deploy social-media-frontend \
    --source=. \
    --platform=managed \
    --region=us-central1 \
    --allow-unauthenticated
```

## Testing OAuth Flows

### 1. Test Environment Setup

```bash
# Create test users
curl -X POST "http://localhost:8000/api/auth/token" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test_user_123"}'
```

### 2. YouTube OAuth Testing

```bash
# 1. Initiate OAuth flow
curl -X POST "http://localhost:8000/api/auth/connect/youtube" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'

# 2. Complete flow (after user authorization)
# The OAuth callback will be handled automatically

# 3. Check connection status
curl -X GET "http://localhost:8000/api/auth/status/youtube" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN"
```

### 3. Instagram OAuth Testing

```bash
# 1. Initiate OAuth flow
curl -X POST "http://localhost:8000/api/auth/connect/instagram" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'

# 2. Complete flow (after user authorization)
# The OAuth callback will be handled automatically

# 3. Check connection status
curl -X GET "http://localhost:8000/api/auth/status/instagram" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN"
```

### 4. YouTube Read-only Testing

```bash
# Connect YouTube channel by URL
curl -X POST "http://localhost:8000/api/auth/connect/youtube" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"channel_url": "https://youtube.com/@testchannel"}'
```

## Security Considerations

### 1. Token Security

- ✅ **Encryption**: All tokens stored encrypted in Secret Manager
- ✅ **Access Control**: IAM policies restrict Secret Manager access
- ✅ **Rotation**: Automatic token refresh before expiration
- ✅ **Revocation**: Proper token revocation on disconnect

### 2. OAuth Security

- ✅ **CSRF Protection**: State parameter validation
- ✅ **Secure Redirects**: Whitelist allowed redirect URIs
- ✅ **Scope Limitation**: Minimal required permissions
- ✅ **Error Handling**: No sensitive data in error messages

### 3. Production Hardening

```bash
# Enable audit logging
gcloud logging sinks create oauth-audit-sink \
    bigquery.googleapis.com/projects/social-media-manager-prod/datasets/audit_logs \
    --log-filter='resource.type="gce_instance" AND protoPayload.serviceName="secretmanager.googleapis.com"'

# Set up monitoring alerts
gcloud alpha monitoring policies create --policy-from-file=oauth-alerting-policy.yaml
```

## Monitoring and Troubleshooting

### 1. Health Checks

```bash
# Check Secret Manager health
curl "http://localhost:8000/health/secret-manager"

# Check OAuth service status
curl "http://localhost:8000/health/oauth"

# Check connected accounts
curl -X GET "http://localhost:8000/api/accounts" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Common Issues

#### Invalid Redirect URI
```
Error: redirect_uri_mismatch
Solution: Update OAuth app settings with correct redirect URIs
```

#### Secret Manager Permission Denied
```
Error: Permission denied on Secret Manager
Solution: Check service account IAM permissions
```

#### YouTube API Quota Exceeded
```
Error: quotaExceeded
Solution: Implement exponential backoff and request quota increase
```

#### Instagram Business Account Required
```
Error: Instagram account must be Business or Creator
Solution: Convert Instagram account to Business type
```

### 3. Debugging Tools

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Monitor Secret Manager operations
gcloud logging read 'resource.type="secret_manager_secret"' --limit=50

# Monitor OAuth flows
gcloud logging read 'jsonPayload.message:"OAuth"' --limit=50
```

## Performance Optimization

### 1. Caching Strategy

- **Token Caching**: Cache valid tokens in memory for 5 minutes
- **Channel Data**: Cache YouTube channel data for 1 hour
- **Connection Status**: Cache connection status for 10 minutes

### 2. Rate Limiting

```python
# Implement rate limiting for OAuth endpoints
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@router.post("/connect/{platform}")
@limiter.limit("5/minute")
async def connect_platform():
    # OAuth connection logic
    pass
```

### 3. Monitoring Metrics

- OAuth flow completion rates
- Token refresh success rates
- API quota usage
- Error rates by platform
- Connection success rates

## Backup and Recovery

### 1. Secret Backup

```bash
# Export secrets for backup (development only)
python scripts/backup_secrets.py --output secrets-backup.json

# Restore secrets from backup
python scripts/restore_secrets.py --input secrets-backup.json
```

### 2. Connection Recovery

```bash
# Rebuild connections from Firestore
python scripts/rebuild_connections.py --user-id user123

# Refresh all expired tokens
python scripts/refresh_expired_tokens.py
```

## Future Enhancements

### Planned Features

1. **Additional Platforms**: TikTok, X (Twitter) OAuth integration
2. **Advanced Security**: Certificate-based authentication
3. **Multi-tenant**: Support for organization accounts
4. **Webhook Integration**: Real-time platform notifications
5. **Analytics**: Detailed OAuth usage analytics

### API Extensions

```python
# Planned endpoints
POST /api/auth/connect/bulk        # Bulk platform connections
GET  /api/auth/connections/health  # Connection health checks
POST /api/auth/tokens/rotate       # Manual token rotation
GET  /api/auth/usage/analytics     # OAuth usage statistics
```

This comprehensive OAuth implementation provides a secure, scalable foundation for social media platform integration with room for future expansion.