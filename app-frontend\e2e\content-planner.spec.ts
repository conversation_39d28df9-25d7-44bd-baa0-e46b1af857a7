import { test, expect } from '@playwright/test';

test.describe('Content Planner E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock content plans API
    await page.route('**/api/plans', async (route) => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            status: 'success',
            data: [
              {
                id: 'plan-1',
                title: 'Weekly Content Plan',
                timeframe_start: '2024-01-01T00:00:00Z',
                timeframe_end: '2024-01-07T23:59:59Z',
                platforms: ['youtube', 'instagram'],
                posts: [
                  {
                    id: 'post-1',
                    title: 'Tutorial: Getting Started',
                    platform: 'youtube',
                    scheduled_time: '2024-01-02T10:00:00Z',
                    content_type: 'video',
                    status: 'scheduled',
                  },
                  {
                    id: 'post-2',
                    title: 'Behind the Scenes',
                    platform: 'instagram',
                    scheduled_time: '2024-01-03T14:00:00Z',
                    content_type: 'reel',
                    status: 'draft',
                  }
                ]
              }
            ]
          })
        });
      } else if (route.request().method() === 'POST') {
        const body = JSON.parse(route.request().postData() || '{}');
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            status: 'success',
            data: {
              id: 'new-plan-' + Date.now(),
              title: `${body.timeframe}-day Content Plan`,
              timeframe_start: new Date().toISOString(),
              timeframe_end: new Date(Date.now() + body.timeframe * 24 * 60 * 60 * 1000).toISOString(),
              platforms: body.platforms,
              posts: []
            }
          })
        });
      }
    });

    // Mock accounts API
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: [
            {
              id: 'yt-1',
              platform: 'youtube',
              handle: '@testchannel',
              metrics: { followers: 15420 }
            }
          ]
        })
      });
    });

    await page.goto('/planner');
  });

  test('should display content planner interface', async ({ page }) => {
    // Check for main planner elements
    await expect(page.getByRole('heading', { name: /content planner/i })).toBeVisible();
    
    // Should show generate plan button or existing plans
    const generateButton = page.getByRole('button', { name: /generate.*plan/i });
    const existingPlans = page.getByText(/weekly content plan/i);
    
    // Either generate button or existing plans should be visible
    await expect(generateButton.or(existingPlans)).toBeVisible();
  });

  test('should open generate plan modal', async ({ page }) => {
    // Look for generate plan button
    const generateButton = page.getByRole('button', { name: /generate.*plan/i }).first();
    
    if (await generateButton.isVisible()) {
      await generateButton.click();
      
      // Modal should open
      await expect(page.getByText('Generate Content Plan')).toBeVisible();
      await expect(page.getByText(/Create a personalized content strategy/)).toBeVisible();
    }
  });

  test('should complete plan generation workflow', async ({ page }) => {
    // Open generate plan modal
    const generateButton = page.getByRole('button', { name: /generate.*plan/i }).first();
    if (await generateButton.isVisible()) {
      await generateButton.click();
    }

    // Step 1: Select timeframe and platforms
    if (await page.getByText('Select Timeframe & Platforms').isVisible()) {
      // Select 14 days
      await page.getByText('14 Days').click();
      
      // Select YouTube platform
      await page.getByText('YouTube').click();
      
      // Proceed to goals
      await page.getByRole('button', { name: /next.*goals/i }).click();
    }

    // Step 2: Select goals
    if (await page.getByText('What are your goals?').isVisible()) {
      // Select a goal
      await page.getByText('Increase followers').click();
      
      // Proceed to content types
      await page.getByRole('button', { name: /next/i }).click();
    }

    // Step 3: Select content types
    if (await page.getByText('Choose Content Types').isVisible()) {
      // Select content type
      await page.getByText('Educational tutorials').click();
      
      // Generate plan
      const finalGenerateButton = page.getByRole('button', { name: /generate plan/i });
      await finalGenerateButton.click();
    }

    // Should show generating state
    await expect(page.getByText(/generating.*plan/i)).toBeVisible({ timeout: 5000 });

    // Should complete and show success
    await expect(page.getByText(/plan.*generated.*successfully/i)).toBeVisible({ timeout: 10000 });
  });

  test('should validate form inputs in generate modal', async ({ page }) => {
    // Open modal
    const generateButton = page.getByRole('button', { name: /generate.*plan/i }).first();
    if (await generateButton.isVisible()) {
      await generateButton.click();
    }

    // Next button should be disabled without platform selection
    const nextButton = page.getByRole('button', { name: /next.*goals/i });
    await expect(nextButton).toBeDisabled();

    // Select platform
    await page.getByText('YouTube').click();
    
    // Next button should now be enabled
    await expect(nextButton).toBeEnabled();
  });

  test('should display existing content plans', async ({ page }) => {
    // Should show existing plans
    await expect(page.getByText('Weekly Content Plan')).toBeVisible();
    
    // Should show plan details
    await expect(page.getByText('Tutorial: Getting Started')).toBeVisible();
    await expect(page.getByText('Behind the Scenes')).toBeVisible();
  });

  test('should show calendar view of planned content', async ({ page }) => {
    // Look for calendar view toggle or calendar elements
    const calendarView = page.locator('[data-testid="calendar-view"]').or(
      page.locator('.calendar').or(
        page.getByRole('button', { name: /calendar/i })
      )
    );

    if (await calendarView.isVisible()) {
      await calendarView.click();
    }

    // Should show calendar with events
    // This depends on the specific calendar implementation
    await expect(page.locator('body')).toBeVisible(); // Basic check that page doesn't crash
  });

  test('should filter plans by platform', async ({ page }) => {
    // Look for platform filter
    const platformFilter = page.locator('select').or(
      page.getByRole('button', { name: /filter/i })
    );

    if (await platformFilter.isVisible()) {
      // Apply filter (implementation depends on UI)
      if (await platformFilter.evaluate(el => el.tagName) === 'SELECT') {
        await platformFilter.selectOption('youtube');
      } else {
        await platformFilter.click();
        await page.getByText('YouTube').click();
      }

      // Results should be filtered
      await expect(page.getByText('youtube')).toBeVisible();
    }
  });

  test('should handle post status updates', async ({ page }) => {
    // Mock post update API
    await page.route('**/api/posts/*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: { status: 'published' }
        })
      });
    });

    // Look for post status controls
    const statusButton = page.getByRole('button', { name: /draft/i }).or(
      page.getByRole('button', { name: /scheduled/i })
    );

    if (await statusButton.isVisible()) {
      await statusButton.click();
      
      // Should show status options
      const publishOption = page.getByText('Published').or(
        page.getByRole('button', { name: /publish/i })
      );
      
      if (await publishOption.isVisible()) {
        await publishOption.click();
      }
    }
  });

  test('should show post details in modal', async ({ page }) => {
    // Click on a post to view details
    const postTitle = page.getByText('Tutorial: Getting Started');
    if (await postTitle.isVisible()) {
      await postTitle.click();
      
      // Should open post details modal
      await expect(page.getByText('Post Details')).toBeVisible();
      await expect(page.getByText('Tutorial: Getting Started')).toBeVisible();
    }
  });

  test('should handle plan deletion', async ({ page }) => {
    // Mock delete API
    await page.route('**/api/plans/*', async (route) => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ status: 'success' })
        });
      }
    });

    // Look for delete button
    const deleteButton = page.getByRole('button', { name: /delete/i }).or(
      page.locator('[data-testid="delete-plan"]')
    );

    if (await deleteButton.isVisible()) {
      await deleteButton.click();
      
      // Should show confirmation dialog
      const confirmButton = page.getByRole('button', { name: /confirm/i }).or(
        page.getByRole('button', { name: /yes/i })
      );
      
      if (await confirmButton.isVisible()) {
        await confirmButton.click();
      }
    }
  });

  test('should export plan to CSV/PDF', async ({ page }) => {
    // Mock export functionality
    await page.route('**/api/plans/*/export', async (route) => {
      await route.fulfill({
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': 'attachment; filename="content-plan.pdf"'
        },
        body: Buffer.from('Mock PDF content')
      });
    });

    // Look for export button
    const exportButton = page.getByRole('button', { name: /export/i }).or(
      page.getByRole('button', { name: /download/i })
    );

    if (await exportButton.isVisible()) {
      // Set up download handler
      const downloadPromise = page.waitForEvent('download');
      
      await exportButton.click();
      
      // Should trigger download
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toContain('.pdf');
    }
  });

  test('should handle responsive design in planner', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Planner should still be accessible
    await expect(page.getByRole('heading', { name: /content planner/i })).toBeVisible();
    
    // Content should adapt to mobile layout
    await expect(page.locator('body')).toBeVisible();
  });

  test('should search through planned content', async ({ page }) => {
    // Look for search input
    const searchInput = page.getByRole('textbox', { name: /search/i }).or(
      page.getByPlaceholder(/search/i)
    );

    if (await searchInput.isVisible()) {
      // Search for content
      await searchInput.fill('Tutorial');
      
      // Should filter results
      await expect(page.getByText('Tutorial: Getting Started')).toBeVisible();
      
      // Other content might be hidden
      // This depends on implementation
    }
  });

  test('should handle bulk operations on posts', async ({ page }) => {
    // Look for bulk selection
    const selectAllCheckbox = page.locator('input[type="checkbox"]').first();
    
    if (await selectAllCheckbox.isVisible()) {
      await selectAllCheckbox.check();
      
      // Should show bulk actions
      const bulkActions = page.getByText('Bulk Actions').or(
        page.getByRole('button', { name: /actions/i })
      );
      
      if (await bulkActions.isVisible()) {
        await bulkActions.click();
        
        // Should show action options
        await expect(page.getByText('Delete Selected')).toBeVisible();
      }
    }
  });

  test('should integrate with AI content suggestions', async ({ page }) => {
    // Mock AI suggestions API
    await page.route('**/api/ai/suggestions', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: {
            suggestions: [
              {
                title: 'Top 10 Tips for Success',
                description: 'Educational content based on trending topics',
                platforms: ['youtube', 'instagram']
              }
            ]
          }
        })
      });
    });

    // Look for AI suggestions button
    const aiSuggestionsButton = page.getByRole('button', { name: /ai.*suggest/i }).or(
      page.getByRole('button', { name: /suggest.*content/i })
    );

    if (await aiSuggestionsButton.isVisible()) {
      await aiSuggestionsButton.click();
      
      // Should show AI suggestions
      await expect(page.getByText('Top 10 Tips for Success')).toBeVisible();
    }
  });
});