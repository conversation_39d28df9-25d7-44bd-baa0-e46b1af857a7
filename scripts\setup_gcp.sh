#!/bin/bash
# Google Cloud Platform Setup Script for Social Media Manager
# This script configures the complete GCP infrastructure

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
DEFAULT_PROJECT_ID=""
DEFAULT_REGION="us-central1"
DEFAULT_ZONE="us-central1-a"

# Functions for colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}"
    echo "============================================"
    echo "$1"
    echo "============================================"
    echo -e "${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        echo "Download from: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n 1 > /dev/null; then
        print_error "Not authenticated with gcloud. Please run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check completed"
}

# Function to get or set project configuration
setup_project_config() {
    print_header "PROJECT CONFIGURATION"
    
    # Get current project if set
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
    
    if [ -z "$CURRENT_PROJECT" ]; then
        echo "No project currently configured."
        read -p "Enter your Google Cloud Project ID: " PROJECT_ID
    else
        echo "Current project: $CURRENT_PROJECT"
        read -p "Use current project? (y/N): " USE_CURRENT
        if [[ $USE_CURRENT =~ ^[Yy]$ ]]; then
            PROJECT_ID=$CURRENT_PROJECT
        else
            read -p "Enter your Google Cloud Project ID: " PROJECT_ID
        fi
    fi
    
    # Set project
    gcloud config set project $PROJECT_ID
    
    # Set region and zone
    read -p "Enter region [$DEFAULT_REGION]: " REGION
    REGION=${REGION:-$DEFAULT_REGION}
    
    read -p "Enter zone [$DEFAULT_ZONE]: " ZONE
    ZONE=${ZONE:-$DEFAULT_ZONE}
    
    gcloud config set compute/region $REGION
    gcloud config set compute/zone $ZONE
    
    print_success "Project configuration completed"
    echo "  Project: $PROJECT_ID"
    echo "  Region: $REGION"
    echo "  Zone: $ZONE"
}

# Function to enable required APIs
enable_apis() {
    print_header "ENABLING GOOGLE CLOUD APIS"
    
    apis=(
        "run.googleapis.com"                    # Cloud Run
        "cloudbuild.googleapis.com"            # Cloud Build
        "containerregistry.googleapis.com"     # Container Registry
        "artifactregistry.googleapis.com"      # Artifact Registry
        "secretmanager.googleapis.com"         # Secret Manager
        "firestore.googleapis.com"             # Firestore
        "bigquery.googleapis.com"              # BigQuery
        "logging.googleapis.com"               # Cloud Logging
        "monitoring.googleapis.com"            # Cloud Monitoring
        "cloudtrace.googleapis.com"            # Cloud Trace
        "iam.googleapis.com"                   # Identity and Access Management
        "youtube.googleapis.com"               # YouTube Data API
        "oauth2.googleapis.com"                # OAuth 2.0
    )
    
    for api in "${apis[@]}"; do
        print_status "Enabling $api..."
        gcloud services enable $api --project=$PROJECT_ID
    done
    
    print_success "All APIs enabled successfully"
}

# Function to create service accounts
create_service_accounts() {
    print_header "CREATING SERVICE ACCOUNTS"
    
    # Backend service account
    print_status "Creating backend service account..."
    if ! gcloud iam service-accounts describe social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com --project=$PROJECT_ID &> /dev/null; then
        gcloud iam service-accounts create social-media-backend \
            --display-name="Social Media Backend Service" \
            --description="Service account for Social Media Manager backend" \
            --project=$PROJECT_ID
        print_success "Backend service account created"
    else
        print_warning "Backend service account already exists"
    fi
    
    # Assign roles to backend service account
    backend_roles=(
        "roles/secretmanager.secretAccessor"
        "roles/datastore.user"
        "roles/bigquery.user"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/cloudtrace.agent"
    )
    
    for role in "${backend_roles[@]}"; do
        print_status "Assigning role: $role"
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="$role"
    done
    
    # Analytics service account
    print_status "Creating analytics service account..."
    if ! gcloud iam service-accounts describe social-media-analytics@${PROJECT_ID}.iam.gserviceaccount.com --project=$PROJECT_ID &> /dev/null; then
        gcloud iam service-accounts create social-media-analytics \
            --display-name="Social Media Analytics Service" \
            --description="Service account for analytics and data processing" \
            --project=$PROJECT_ID
        print_success "Analytics service account created"
    else
        print_warning "Analytics service account already exists"
    fi
    
    # Assign roles to analytics service account
    analytics_roles=(
        "roles/bigquery.admin"
        "roles/datastore.user"
        "roles/secretmanager.secretAccessor"
    )
    
    for role in "${analytics_roles[@]}"; do
        print_status "Assigning role: $role"
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:social-media-analytics@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="$role"
    done
    
    print_success "Service accounts setup completed"
}

# Function to setup Firestore
setup_firestore() {
    print_header "CONFIGURING FIRESTORE"
    
    print_status "Checking Firestore database..."
    
    # Check if Firestore is already configured
    if gcloud firestore databases describe --database="(default)" --project=$PROJECT_ID &> /dev/null; then
        print_warning "Firestore database already exists"
    else
        print_status "Creating Firestore database..."
        gcloud firestore databases create --database="(default)" --location=$REGION --project=$PROJECT_ID
        print_success "Firestore database created"
    fi
    
    print_success "Firestore setup completed"
}

# Function to setup BigQuery
setup_bigquery() {
    print_header "CONFIGURING BIGQUERY"
    
    print_status "Creating BigQuery dataset..."
    
    # Create analytics dataset
    if ! bq ls -d --project_id=$PROJECT_ID social_media_analytics &> /dev/null; then
        bq mk \
            --dataset \
            --location=$REGION \
            --description="Social Media Manager Analytics Data Warehouse" \
            --label=app:social-media-manager \
            --label=env:production \
            ${PROJECT_ID}:social_media_analytics
        print_success "BigQuery dataset created"
    else
        print_warning "BigQuery dataset already exists"
    fi
    
    print_success "BigQuery setup completed"
}

# Function to setup Secret Manager
setup_secret_manager() {
    print_header "CONFIGURING SECRET MANAGER"
    
    print_status "Setting up secret placeholders..."
    
    # List of secrets to create (without values)
    secrets=(
        "app-secret-key"
        "google-client-id"
        "google-client-secret"
        "youtube-api-key"
        "instagram-app-id"
        "instagram-app-secret"
        "google-search-api-key"
        "google-search-engine-id"
    )
    
    for secret in "${secrets[@]}"; do
        if ! gcloud secrets describe $secret --project=$PROJECT_ID &> /dev/null; then
            echo "placeholder-value-update-after-deployment" | gcloud secrets create $secret \
                --data-file=- \
                --replication-policy="automatic" \
                --project=$PROJECT_ID \
                --labels=app=social-media-manager,env=production
            print_success "Created secret: $secret"
        else
            print_warning "Secret $secret already exists"
        fi
    done
    
    print_warning "⚠️  Remember to update secret values after deployment!"
    print_warning "Use: gcloud secrets versions add SECRET_NAME --data-file=FILE"
    
    print_success "Secret Manager setup completed"
}

# Function to setup Cloud Build
setup_cloud_build() {
    print_header "CONFIGURING CLOUD BUILD"
    
    # Enable Cloud Build API (already done in enable_apis)
    print_status "Configuring Cloud Build..."
    
    # Grant Cloud Build access to necessary services
    PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
    
    # Grant Cloud Run Admin role to Cloud Build service account
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${PROJECT_NUMBER}@cloudbuild.gserviceaccount.com" \
        --role="roles/run.admin"
    
    # Grant Service Account User role
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${PROJECT_NUMBER}@cloudbuild.gserviceaccount.com" \
        --role="roles/iam.serviceAccountUser"
    
    print_success "Cloud Build setup completed"
}

# Function to setup Container Registry
setup_container_registry() {
    print_header "CONFIGURING CONTAINER REGISTRY"
    
    print_status "Configuring Container Registry permissions..."
    
    # Configure Docker to use gcloud as a credential helper
    gcloud auth configure-docker --quiet
    
    print_success "Container Registry setup completed"
}

# Function to setup monitoring and logging
setup_monitoring() {
    print_header "CONFIGURING MONITORING & LOGGING"
    
    print_status "Setting up monitoring workspace..."
    
    # The monitoring workspace is created automatically when the monitoring API is enabled
    # and the first metric is written
    
    print_status "Configuring log retention..."
    
    # Set up log sink for important application logs
    if ! gcloud logging sinks describe social-media-app-logs --project=$PROJECT_ID &> /dev/null; then
        gcloud logging sinks create social-media-app-logs \
            bigquery.googleapis.com/projects/${PROJECT_ID}/datasets/social_media_analytics \
            --log-filter='resource.type="cloud_run_revision" AND labels."service-name"=("social-media-backend" OR "social-media-frontend")' \
            --project=$PROJECT_ID
        print_success "Log sink created"
    else
        print_warning "Log sink already exists"
    fi
    
    print_success "Monitoring and logging setup completed"
}

# Function to create environment configuration
create_env_config() {
    print_header "CREATING ENVIRONMENT CONFIGURATION"
    
    # Create environment file for deployment
    cat > deployment/.env.production << EOF
# Production Environment Configuration
GOOGLE_CLOUD_PROJECT=${PROJECT_ID}
PROJECT_ID=${PROJECT_ID}
REGION=${REGION}
ZONE=${ZONE}

# Service Configuration
ENV=production
DEBUG=false

# Database Configuration
FIRESTORE_PROJECT_ID=${PROJECT_ID}
BIGQUERY_PROJECT_ID=${PROJECT_ID}
BIGQUERY_DATASET_ID=social_media_analytics

# Service URLs (update after deployment)
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-backend-domain.com

# OAuth Configuration (values stored in Secret Manager)
# These are retrieved at runtime from Secret Manager
SECRET_KEY=\${SECRET_KEY}
GOOGLE_CLIENT_ID=\${GOOGLE_CLIENT_ID}
GOOGLE_CLIENT_SECRET=\${GOOGLE_CLIENT_SECRET}
YOUTUBE_API_KEY=\${YOUTUBE_API_KEY}
INSTAGRAM_APP_ID=\${INSTAGRAM_APP_ID}
INSTAGRAM_APP_SECRET=\${INSTAGRAM_APP_SECRET}
GOOGLE_SEARCH_API_KEY=\${GOOGLE_SEARCH_API_KEY}
GOOGLE_SEARCH_ENGINE_ID=\${GOOGLE_SEARCH_ENGINE_ID}
EOF
    
    print_success "Environment configuration created: deployment/.env.production"
}

# Function to run final validation
run_validation() {
    print_header "VALIDATION"
    
    print_status "Validating GCP setup..."
    
    # Check project exists and is accessible
    if gcloud projects describe $PROJECT_ID &> /dev/null; then
        print_success "✓ Project accessible"
    else
        print_error "✗ Project not accessible"
        return 1
    fi
    
    # Check APIs are enabled
    enabled_apis=$(gcloud services list --enabled --format="value(name)" --project=$PROJECT_ID)
    required_apis=("run.googleapis.com" "firestore.googleapis.com" "bigquery.googleapis.com" "secretmanager.googleapis.com")
    
    for api in "${required_apis[@]}"; do
        if echo "$enabled_apis" | grep -q "$api"; then
            print_success "✓ $api enabled"
        else
            print_error "✗ $api not enabled"
        fi
    done
    
    # Check service accounts
    if gcloud iam service-accounts describe social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com --project=$PROJECT_ID &> /dev/null; then
        print_success "✓ Backend service account exists"
    else
        print_error "✗ Backend service account missing"
    fi
    
    # Check Firestore database
    if gcloud firestore databases describe --database="(default)" --project=$PROJECT_ID &> /dev/null; then
        print_success "✓ Firestore database exists"
    else
        print_error "✗ Firestore database missing"
    fi
    
    # Check BigQuery dataset
    if bq ls -d --project_id=$PROJECT_ID social_media_analytics &> /dev/null; then
        print_success "✓ BigQuery dataset exists"
    else
        print_error "✗ BigQuery dataset missing"
    fi
    
    print_success "Validation completed"
}

# Function to display next steps
show_next_steps() {
    print_header "NEXT STEPS"
    
    echo -e "${GREEN}🎉 GCP Infrastructure setup completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "1. Update secrets in Secret Manager with actual values:"
    echo "   gcloud secrets versions add SECRET_NAME --data-file=SECRET_FILE"
    echo ""
    echo "2. Run BigQuery setup:"
    echo "   cd scripts && python setup_bigquery.py"
    echo ""
    echo "3. Deploy applications:"
    echo "   cd deployment && ./deploy.sh"
    echo ""
    echo "4. Configure OAuth applications:"
    echo "   - Google: https://console.developers.google.com"
    echo "   - Instagram: https://developers.facebook.com"
    echo ""
    echo "5. Update DNS/Domain settings for custom domains"
    echo ""
    echo -e "${BLUE}🔗 Useful Links:${NC}"
    echo "  Cloud Console: https://console.cloud.google.com/home/<USER>"
    echo "  Secret Manager: https://console.cloud.google.com/security/secret-manager?project=${PROJECT_ID}"
    echo "  Firestore: https://console.cloud.google.com/firestore?project=${PROJECT_ID}"
    echo "  BigQuery: https://console.cloud.google.com/bigquery?project=${PROJECT_ID}"
    echo "  Cloud Run: https://console.cloud.google.com/run?project=${PROJECT_ID}"
}

# Main execution function
main() {
    print_header "SOCIAL MEDIA MANAGER - GCP SETUP"
    
    check_prerequisites
    setup_project_config
    enable_apis
    create_service_accounts
    setup_firestore
    setup_bigquery
    setup_secret_manager
    setup_cloud_build
    setup_container_registry
    setup_monitoring
    create_env_config
    run_validation
    show_next_steps
    
    print_success "🚀 Setup completed successfully!"
}

# Handle script interruption
trap 'echo -e "\n${RED}Setup interrupted${NC}"; exit 1' INT

# Run main function
main "$@"