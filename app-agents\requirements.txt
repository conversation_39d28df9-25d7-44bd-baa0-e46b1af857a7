# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Google Cloud & ADK
google-adk
google-cloud-firestore==2.13.1
google-cloud-secret-manager==2.17.0
google-cloud-bigquery==3.13.0
google-cloud-logging==3.8.0
google-generativeai>=0.8.0
google-genai>=0.9.0
google-ai-generativelanguage>=0.6.0

# Google Search API (optional - for custom search implementations)
google-api-python-client>=2.108.0

# ADK Session and State Management Dependencies
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0  # PostgreSQL support for production sessions
alembic>=1.13.0  # Database migrations
opentelemetry-api>=1.21.0
opentelemetry-sdk>=1.21.0
opentelemetry-exporter-gcp-trace>=1.6.0

# OAuth & Authentication
authlib==1.2.1
requests==2.31.0
httpx==0.25.2

# YouTube & Instagram APIs
google-api-python-client==2.108.0
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
# instagrapi>=2.0.0  # Commenting out due to pydantic conflicts

# Data processing
pandas==2.1.3
numpy==1.25.2
pydantic>=1.10.9,<3.0.0
pydantic-settings==2.1.0
python-dateutil==2.8.2

# Background tasks
celery[redis]==5.3.4
redis>=4.5.2,<5.0.0

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# Environment
python-dotenv==1.0.0