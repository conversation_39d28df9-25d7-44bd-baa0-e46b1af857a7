"use client";

import { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Settings, 
  Trash2, 
  RefreshCw, 
  Clock, 
  MessageSquare, 
  User, 
  Bot,
  Download,
  Upload,
  AlertCircle
} from "lucide-react";
import { useSessionPersistence, SessionStorageData } from "@/hooks/use-session-persistence";
import { useChatHistory } from "@/hooks/use-chat-history";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

export interface SessionManagerProps {
  userId: string;
  currentAgentName: string;
  currentSessionId?: string;
  onSessionSelect?: (sessionData: SessionStorageData) => void;
  onSessionClear?: () => void;
  className?: string;
}

export function SessionManager({
  userId,
  currentAgentName,
  currentSessionId,
  onSessionSelect,
  onSessionClear,
  className
}: SessionManagerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedSession, setSelectedSession] = useState<SessionStorageData | null>(null);

  const {
    sessionData: currentSessionData,
    getStoredSessions,
    removeStoredSession,
    clearAllSessions,
    clearSession
  } = useSessionPersistence({
    userId,
    agentName: currentAgentName
  });

  const storedSessions = getStoredSessions();

  const handleSessionSelect = useCallback((sessionData: SessionStorageData) => {
    onSessionSelect?.(sessionData);
    setIsOpen(false);
  }, [onSessionSelect]);

  const handleSessionDelete = useCallback((sessionId: string) => {
    removeStoredSession(sessionId);
    if (sessionId === currentSessionId) {
      onSessionClear?.();
    }
  }, [removeStoredSession, currentSessionId, onSessionClear]);

  const handleClearAllSessions = useCallback(() => {
    clearAllSessions();
    onSessionClear?.();
  }, [clearAllSessions, onSessionClear]);

  const handleClearCurrentSession = useCallback(() => {
    clearSession();
    onSessionClear?.();
  }, [clearSession, onSessionClear]);

  const exportSessions = useCallback(() => {
    const exportData = {
      userId,
      exportDate: new Date().toISOString(),
      sessions: storedSessions
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `adk-sessions-${userId}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [userId, storedSessions]);

  const importSessions = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target?.result as string);
        if (importData.sessions && Array.isArray(importData.sessions)) {
          // Here you would implement the import logic
          // For now, just log the data
          console.log('Import data:', importData);
          alert('Import functionality would be implemented here');
        }
      } catch (error) {
        console.error('Failed to import sessions:', error);
        alert('Failed to import sessions. Please check the file format.');
      }
    };
    reader.readAsText(file);
  }, []);

  const formatLastActivity = (lastActivity: string) => {
    try {
      return formatDistanceToNow(new Date(lastActivity), { addSuffix: true });
    } catch {
      return 'Unknown';
    }
  };

  const getSessionStatus = (sessionData: SessionStorageData) => {
    const isActive = sessionData.sessionId === currentSessionId;
    const lastActivity = new Date(sessionData.lastActivity);
    const isRecent = Date.now() - lastActivity.getTime() < 60 * 60 * 1000; // 1 hour

    if (isActive) return { label: 'Active', variant: 'default' as const };
    if (isRecent) return { label: 'Recent', variant: 'secondary' as const };
    return { label: 'Inactive', variant: 'outline' as const };
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className={cn("h-8 w-8 p-0", className)}>
          <Settings className="w-4 h-4" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Session Management
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Session Info */}
          {currentSessionData && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <MessageSquare className="w-4 h-4" />
                  Current Session
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Session ID:</span>
                    <div className="font-mono text-xs mt-1">
                      {currentSessionData.sessionId}
                    </div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Agent:</span>
                    <div className="flex items-center gap-1 mt-1">
                      <Bot className="w-3 h-3" />
                      {currentSessionData.agentName}
                    </div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Messages:</span>
                    <div className="mt-1">{currentSessionData.messageCount}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Last Activity:</span>
                    <div className="mt-1">{formatLastActivity(currentSessionData.lastActivity)}</div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Trash2 className="w-3 h-3 mr-1" />
                        Clear Current Session
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Clear Current Session</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will clear the current chat session and remove it from storage. 
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleClearCurrentSession}>
                          Clear Session
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Session History */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Session History ({storedSessions.length})
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={exportSessions}>
                    <Download className="w-3 h-3 mr-1" />
                    Export
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <label>
                      <Upload className="w-3 h-3 mr-1" />
                      Import
                      <input
                        type="file"
                        accept=".json"
                        onChange={importSessions}
                        className="hidden"
                      />
                    </label>
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {storedSessions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No stored sessions found</p>
                </div>
              ) : (
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {storedSessions.map((session, index) => {
                      const status = getSessionStatus(session);
                      const isCurrentSession = session.sessionId === currentSessionId;
                      
                      return (
                        <div
                          key={session.sessionId}
                          className={cn(
                            "flex items-center justify-between p-3 rounded-lg border transition-colors",
                            isCurrentSession 
                              ? "bg-primary/5 border-primary/20" 
                              : "hover:bg-muted/50"
                          )}
                        >
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge variant={status.variant} className="text-xs">
                                {status.label}
                              </Badge>
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Bot className="w-3 h-3" />
                                {session.agentName}
                              </div>
                            </div>
                            
                            <div className="text-xs text-muted-foreground font-mono truncate">
                              {session.sessionId}
                            </div>
                            
                            <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                              <span>{session.messageCount} messages</span>
                              <span>{formatLastActivity(session.lastActivity)}</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-1 ml-2">
                            {!isCurrentSession && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleSessionSelect(session)}
                                className="h-7 px-2 text-xs"
                              >
                                Load
                              </Button>
                            )}
                            
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Session</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This will permanently delete the session and all its data. 
                                    This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => handleSessionDelete(session.sessionId)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>

          {/* Bulk Actions */}
          {storedSessions.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  Bulk Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="w-3 h-3 mr-1" />
                        Clear All Sessions
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Clear All Sessions</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will permanently delete all stored sessions ({storedSessions.length} sessions) 
                          and their data. This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={handleClearAllSessions}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Clear All Sessions
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}