#!/usr/bin/env python3
"""
Sample Data Seeder for Social Media Manager

This script generates realistic sample data for development and demo purposes.
It creates sample users, connected accounts, metrics, content plans, and chat history.
"""

import json
import random
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any
import uuid

class SampleDataGenerator:
    """Generates realistic sample data for the social media manager application"""
    
    def __init__(self):
        self.now = datetime.now(timezone.utc)
        self.platforms = ['youtube', 'instagram', 'twitter', 'tiktok']
        
        # Sample content types by platform
        self.content_types = {
            'youtube': ['tutorial', 'review', 'vlog', 'educational', 'entertainment', 'interview'],
            'instagram': ['post', 'story', 'reel', 'carousel', 'igtv'],
            'twitter': ['tweet', 'thread', 'retweet', 'quote_tweet'],
            'tiktok': ['video', 'duet', 'challenge', 'dance', 'comedy']
        }
        
        # Sample goals and objectives
        self.goals = [
            'Increase followers',
            'Boost engagement',
            'Drive website traffic',
            'Build brand awareness',
            'Generate leads',
            'Promote products/services',
            'Establish thought leadership',
            'Community building'
        ]
        
        # Sample content topics
        self.content_topics = [
            'Tech tutorials', 'Productivity tips', 'Behind the scenes',
            'Industry insights', 'How-to guides', 'Product reviews',
            'Success stories', 'Q&A sessions', 'Live events',
            'Trending topics', 'Educational content', 'Entertainment'
        ]

    def generate_users(self, count: int = 3) -> List[Dict]:
        """Generate sample users"""
        users = []
        
        sample_users = [
            {
                'name': 'Alex Chen',
                'email': '<EMAIL>',
                'bio': 'Tech entrepreneur and content creator sharing insights about startups and innovation.',
                'avatar': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
                'industry': 'Technology'
            },
            {
                'name': 'Maria Rodriguez',
                'email': '<EMAIL>', 
                'bio': 'Digital marketing strategist helping businesses grow their online presence.',
                'avatar': 'https://images.unsplash.com/photo-1494790108755-2616b5e4d89d?w=150',
                'industry': 'Marketing'
            },
            {
                'name': 'David Johnson',
                'email': '<EMAIL>',
                'bio': 'Fitness coach and wellness advocate inspiring healthy lifestyle choices.',
                'avatar': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
                'industry': 'Health & Fitness'
            }
        ]
        
        for i in range(min(count, len(sample_users))):
            user_data = sample_users[i]
            users.append({
                'id': f'user_{i+1}',
                'name': user_data['name'],
                'email': user_data['email'],
                'bio': user_data['bio'],
                'avatar': user_data['avatar'],
                'industry': user_data['industry'],
                'created_at': (self.now - timedelta(days=random.randint(30, 365))).isoformat(),
                'last_active': (self.now - timedelta(hours=random.randint(1, 48))).isoformat(),
                'subscription_plan': random.choice(['free', 'pro', 'enterprise']),
                'preferences': {
                    'notifications': True,
                    'auto_post': False,
                    'ai_suggestions': True,
                    'weekly_reports': True
                }
            })
        
        return users

    def generate_connected_accounts(self, user_id: str) -> List[Dict]:
        """Generate connected social media accounts for a user"""
        accounts = []
        
        # Define realistic account data by platform
        platform_data = {
            'youtube': {
                'handles': ['@TechWithAlex', '@InnovationDaily', '@StartupStories'],
                'follower_ranges': (5000, 50000),
                'engagement_ranges': (3.5, 8.2),
                'content_frequency': 'weekly'
            },
            'instagram': {
                'handles': ['@digitalmarketingpro', '@lifestyleinspired', '@creativecontent'],
                'follower_ranges': (2000, 25000),
                'engagement_ranges': (2.8, 6.5),
                'content_frequency': 'daily'
            },
            'twitter': {
                'handles': ['@techthoughts', '@marketingmind', '@fitnessjourney'],
                'follower_ranges': (1000, 15000),
                'engagement_ranges': (1.5, 4.2),
                'content_frequency': 'multiple_daily'
            }
        }
        
        # Generate 2-3 accounts per user
        selected_platforms = random.sample(list(platform_data.keys()), random.randint(2, 3))
        
        for platform in selected_platforms:
            data = platform_data[platform]
            
            account = {
                'id': f'{platform}_{user_id}_{random.randint(1000, 9999)}',
                'user_id': user_id,
                'platform': platform,
                'handle': random.choice(data['handles']),
                'display_name': random.choice(data['handles']).replace('@', ''),
                'avatar': f'https://images.unsplash.com/photo-{random.randint(*************, *************)}?w=150',
                'connected_at': (self.now - timedelta(days=random.randint(7, 180))).isoformat(),
                'last_sync': (self.now - timedelta(hours=random.randint(1, 24))).isoformat(),
                'status': 'active',
                'permissions': ['read', 'write', 'analytics'],
                'metrics': self.generate_account_metrics(platform, data),
                'account_info': {
                    'verified': random.choice([True, False]),
                    'business_account': platform == 'instagram' and random.choice([True, False]),
                    'follower_count': random.randint(*data['follower_ranges']),
                    'following_count': random.randint(100, 2000),
                    'post_count': random.randint(50, 500)
                }
            }
            
            accounts.append(account)
        
        return accounts

    def generate_account_metrics(self, platform: str, platform_data: Dict) -> Dict:
        """Generate realistic metrics for an account"""
        followers = random.randint(*platform_data['follower_ranges'])
        engagement_rate = round(random.uniform(*platform_data['engagement_ranges']), 2)
        
        # Calculate derived metrics
        avg_likes = int(followers * (engagement_rate / 100) * 0.7)
        avg_comments = int(avg_likes * 0.1)
        avg_shares = int(avg_likes * 0.05)
        
        base_metrics = {
            'followers': followers,
            'following': random.randint(100, 2000),
            'engagement_rate': engagement_rate,
            'avg_likes': avg_likes,
            'avg_comments': avg_comments,
            'avg_shares': avg_shares,
            'growth_rate': round(random.uniform(-2.0, 15.0), 1),
            'reach': int(followers * random.uniform(1.2, 3.0)),
            'impressions': int(followers * random.uniform(2.0, 5.0))
        }
        
        # Platform-specific metrics
        if platform == 'youtube':
            base_metrics.update({
                'subscribers': followers,
                'views': random.randint(50000, 500000),
                'watch_time': random.randint(10000, 100000),  # minutes
                'avg_view_duration': round(random.uniform(2.5, 8.0), 1),  # minutes
                'videos_published': random.randint(20, 150)
            })
        elif platform == 'instagram':
            base_metrics.update({
                'stories_views': random.randint(5000, 30000),
                'profile_visits': random.randint(2000, 15000),
                'website_clicks': random.randint(100, 2000),
                'saves': random.randint(500, 5000)
            })
        elif platform == 'twitter':
            base_metrics.update({
                'tweets': random.randint(500, 5000),
                'retweets': random.randint(1000, 10000),
                'mentions': random.randint(200, 2000),
                'link_clicks': random.randint(300, 3000)
            })
        
        return base_metrics

    def generate_content_plans(self, user_id: str, accounts: List[Dict]) -> List[Dict]:
        """Generate sample content plans"""
        plans = []
        
        for i in range(random.randint(2, 4)):
            start_date = self.now + timedelta(days=random.randint(-14, 30))
            duration = random.choice([7, 14, 30])
            end_date = start_date + timedelta(days=duration)
            
            plan = {
                'id': f'plan_{user_id}_{i+1}_{int(start_date.timestamp())}',
                'user_id': user_id,
                'title': random.choice([
                    f'{duration}-Day Growth Strategy',
                    f'Q{random.randint(1,4)} Content Calendar',
                    'Product Launch Campaign',
                    'Engagement Boost Plan',
                    'Brand Awareness Drive'
                ]),
                'description': random.choice([
                    'Comprehensive content strategy to increase engagement and followers',
                    'Multi-platform campaign focusing on brand awareness',
                    'Educational content series to establish thought leadership',
                    'Product promotion with authentic storytelling approach'
                ]),
                'timeframe_start': start_date.isoformat(),
                'timeframe_end': end_date.isoformat(),
                'status': random.choice(['draft', 'active', 'completed', 'paused']),
                'platforms': [acc['platform'] for acc in random.sample(accounts, random.randint(1, len(accounts)))],
                'goals': random.sample(self.goals, random.randint(2, 4)),
                'target_metrics': {
                    'follower_growth': random.randint(5, 25),  # percentage
                    'engagement_increase': random.randint(10, 40),  # percentage
                    'content_frequency': random.choice(['daily', 'weekly', 'bi-weekly'])
                },
                'posts': self.generate_planned_posts(start_date, end_date, accounts),
                'created_at': (start_date - timedelta(days=random.randint(1, 7))).isoformat(),
                'updated_at': (self.now - timedelta(hours=random.randint(1, 72))).isoformat()
            }
            
            plans.append(plan)
        
        return plans

    def generate_planned_posts(self, start_date: datetime, end_date: datetime, accounts: List[Dict]) -> List[Dict]:
        """Generate planned posts for a content plan"""
        posts = []
        current_date = start_date
        post_id = 1
        
        while current_date <= end_date:
            # Generate 1-3 posts per day
            daily_posts = random.randint(1, 3)
            
            for _ in range(daily_posts):
                account = random.choice(accounts)
                platform = account['platform']
                
                # Schedule posts at realistic times
                post_time = current_date.replace(
                    hour=random.choice([9, 11, 14, 16, 18, 20]),
                    minute=random.choice([0, 15, 30, 45]),
                    second=0,
                    microsecond=0
                )
                
                post = {
                    'id': f'post_{post_id}_{int(post_time.timestamp())}',
                    'title': self.generate_post_title(platform),
                    'description': self.generate_post_description(platform),
                    'content_type': random.choice(self.content_types[platform]),
                    'platform': platform,
                    'account_id': account['id'],
                    'scheduled_time': post_time.isoformat(),
                    'status': random.choice(['draft', 'scheduled', 'published', 'failed']),
                    'tags': random.sample([
                        '#productivity', '#innovation', '#business', '#technology',
                        '#marketing', '#growth', '#tips', '#tutorial', '#inspiration'
                    ], random.randint(2, 5)),
                    'estimated_reach': random.randint(1000, 10000),
                    'content_pillars': random.sample([
                        'Education', 'Entertainment', 'Inspiration', 'Behind-the-scenes'
                    ], random.randint(1, 2)),
                    'ai_generated': random.choice([True, False]),
                    'performance_prediction': {
                        'expected_engagement': round(random.uniform(2.0, 8.0), 1),
                        'optimal_posting_time': True if random.random() > 0.3 else False
                    }
                }
                
                posts.append(post)
                post_id += 1
            
            current_date += timedelta(days=1)
        
        return posts

    def generate_post_title(self, platform: str) -> str:
        """Generate realistic post titles by platform"""
        titles_by_platform = {
            'youtube': [
                '5 Productivity Tips That Changed My Life',
                'Behind the Scenes: Building a Startup',
                'Complete Guide to Social Media Marketing',
                'My Morning Routine for Success',
                'Review: Top Tools for Content Creators'
            ],
            'instagram': [
                'Monday Motivation: Start Strong',
                'Coffee and Creativity Session',
                'Swipe for Business Tips →',
                'Weekend Vibes and Reflections',
                'New Product Launch Coming Soon!'
            ],
            'twitter': [
                'Hot take: Social media strategy in 2024',
                'Thread: 10 lessons learned this year',
                'Quick tip for better content creation',
                'Industry insight: What\'s changing',
                'Personal update and big announcement'
            ]
        }
        
        return random.choice(titles_by_platform.get(platform, titles_by_platform['instagram']))

    def generate_post_description(self, platform: str) -> str:
        """Generate realistic post descriptions"""
        descriptions = [
            'Sharing valuable insights from my recent experience...',
            'Let me know what you think in the comments below!',
            'Save this post for later reference 📌',
            'What strategies have worked best for you?',
            'Breaking down complex topics into actionable steps',
            'Here\'s what I learned and how you can apply it',
            'Join the conversation and share your thoughts!',
            'Transform your approach with these proven methods'
        ]
        
        return random.choice(descriptions)

    def generate_chat_history(self, user_id: str, accounts: List[Dict]) -> List[Dict]:
        """Generate sample chat conversation history"""
        messages = []
        
        # Common user queries about social media management
        user_queries = [
            "How is my YouTube performance this month?",
            "Can you analyze my Instagram engagement rate?",
            "What content should I post next week?",
            "Create a content plan for the next 14 days",
            "How can I improve my follower growth?",
            "What are the best times to post on Instagram?",
            "Analyze my top performing posts",
            "Help me plan content for product launch",
            "What hashtags should I use for my fitness content?",
            "Compare my performance across platforms"
        ]
        
        # AI assistant responses
        ai_responses = [
            "Based on your analytics, your YouTube channel has shown strong growth with a 12.5% increase in subscribers this month. Your average view duration has improved to 4.2 minutes, indicating higher engagement.",
            "Your Instagram engagement rate is currently at 4.8%, which is above the industry average of 3.5%. Your Reels are performing particularly well with 35% higher engagement than regular posts.",
            "I recommend focusing on educational content based on your audience preferences. Consider creating 'How-to' tutorials and behind-the-scenes content for maximum engagement.",
            "I've generated a comprehensive 14-day content plan focusing on your top-performing content types. The plan includes 28 posts across YouTube, Instagram, and Twitter with optimal posting times.",
            "To improve follower growth, I suggest: 1) Posting consistently at optimal times, 2) Using trending hashtags relevant to your niche, 3) Engaging with your audience within the first hour of posting.",
            "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.",
            "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach)."
        ]
        
        # Generate conversation history
        for i in range(random.randint(8, 15)):
            # User message
            user_msg = {
                'id': f'msg_user_{i+1}_{int(self.now.timestamp())}',
                'user_id': user_id,
                'role': 'user',
                'content': random.choice(user_queries),
                'timestamp': (self.now - timedelta(days=random.randint(0, 30), 
                                                hours=random.randint(0, 23),
                                                minutes=random.randint(0, 59))).isoformat(),
                'session_id': f'session_{random.randint(1, 5)}',
                'metadata': {
                    'platform_context': random.choice([acc['platform'] for acc in accounts] + [None]),
                    'intent': random.choice(['analysis', 'planning', 'optimization', 'question'])
                }
            }
            messages.append(user_msg)
            
            # AI response
            ai_msg = {
                'id': f'msg_ai_{i+1}_{int(self.now.timestamp())}',
                'user_id': user_id,
                'role': 'assistant',
                'content': random.choice(ai_responses),
                'timestamp': (datetime.fromisoformat(user_msg['timestamp'].replace('Z', '+00:00')) + 
                            timedelta(seconds=random.randint(3, 30))).isoformat(),
                'session_id': user_msg['session_id'],
                'metadata': {
                    'response_type': 'analysis',
                    'confidence_score': round(random.uniform(0.85, 0.98), 2),
                    'processing_time': round(random.uniform(1.2, 8.5), 1),
                    'data_sources': random.sample(['youtube_api', 'instagram_api', 'analytics_db', 'ai_model'], 
                                                random.randint(1, 3))
                }
            }
            messages.append(ai_msg)
        
        # Sort by timestamp
        messages.sort(key=lambda x: x['timestamp'])
        
        return messages

    def generate_analytics_data(self, accounts: List[Dict]) -> Dict:
        """Generate historical analytics data for accounts"""
        analytics = {}
        
        for account in accounts:
            account_id = account['id']
            platform = account['platform']
            
            # Generate 30 days of historical data
            daily_metrics = []
            base_followers = account['account_info']['follower_count']
            
            for i in range(30):
                date = (self.now - timedelta(days=29-i)).date()
                
                # Simulate realistic fluctuations
                follower_change = random.randint(-50, 200)
                engagement_rate = round(random.uniform(2.0, 8.0), 2)
                
                daily_data = {
                    'date': date.isoformat(),
                    'followers': base_followers + (follower_change * i),
                    'engagement_rate': engagement_rate,
                    'posts_published': random.randint(0, 3),
                    'likes': random.randint(100, 2000),
                    'comments': random.randint(10, 200),
                    'shares': random.randint(5, 100),
                    'reach': random.randint(1000, 10000),
                    'impressions': random.randint(2000, 15000)
                }
                
                # Platform-specific metrics
                if platform == 'youtube':
                    daily_data.update({
                        'views': random.randint(1000, 20000),
                        'watch_time': random.randint(500, 5000),  # minutes
                        'subscribers_gained': random.randint(-5, 50)
                    })
                elif platform == 'instagram':
                    daily_data.update({
                        'story_views': random.randint(500, 3000),
                        'profile_visits': random.randint(100, 1000),
                        'saves': random.randint(20, 300)
                    })
                
                daily_metrics.append(daily_data)
            
            analytics[account_id] = {
                'account_id': account_id,
                'platform': platform,
                'daily_metrics': daily_metrics,
                'summary': {
                    'total_followers_gained': sum(day['followers'] for day in daily_metrics[-7:]) - 
                                            sum(day['followers'] for day in daily_metrics[-14:-7]),
                    'avg_engagement_rate': round(sum(day['engagement_rate'] for day in daily_metrics) / len(daily_metrics), 2),
                    'total_posts': sum(day['posts_published'] for day in daily_metrics),
                    'best_performing_day': max(daily_metrics, key=lambda x: x['engagement_rate'])['date']
                }
            }
        
        return analytics

    def generate_complete_dataset(self) -> Dict[str, Any]:
        """Generate complete sample dataset"""
        print("Generating comprehensive sample data...")
        
        # Generate users
        users = self.generate_users(3)
        print(f"✓ Generated {len(users)} sample users")
        
        # Generate accounts and related data for each user
        all_accounts = []
        all_plans = []
        all_messages = []
        all_analytics = {}
        
        for user in users:
            user_id = user['id']
            
            # Generate connected accounts
            accounts = self.generate_connected_accounts(user_id)
            all_accounts.extend(accounts)
            
            # Generate content plans
            plans = self.generate_content_plans(user_id, accounts)
            all_plans.extend(plans)
            
            # Generate chat history
            messages = self.generate_chat_history(user_id, accounts)
            all_messages.extend(messages)
            
            # Generate analytics data
            analytics = self.generate_analytics_data(accounts)
            all_analytics.update(analytics)
        
        print(f"✓ Generated {len(all_accounts)} connected accounts")
        print(f"✓ Generated {len(all_plans)} content plans")
        print(f"✓ Generated {len(all_messages)} chat messages")
        print(f"✓ Generated analytics for {len(all_analytics)} accounts")
        
        dataset = {
            'users': users,
            'connected_accounts': all_accounts,
            'content_plans': all_plans,
            'chat_messages': all_messages,
            'analytics_data': all_analytics,
            'metadata': {
                'generated_at': self.now.isoformat(),
                'version': '1.0',
                'description': 'Sample data for Social Media Manager development and demo',
                'total_records': {
                    'users': len(users),
                    'accounts': len(all_accounts),
                    'plans': len(all_plans),
                    'messages': len(all_messages),
                    'analytics_accounts': len(all_analytics)
                }
            }
        }
        
        return dataset

def main():
    """Main function to generate and save sample data"""
    generator = SampleDataGenerator()
    
    print("🚀 Starting sample data generation for Social Media Manager...")
    print("=" * 60)
    
    # Generate complete dataset
    dataset = generator.generate_complete_dataset()
    
    # Save to JSON file
    output_file = 'sample_data.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, indent=2, ensure_ascii=False)
    
    print("=" * 60)
    print(f"✅ Sample data generated successfully!")
    print(f"📁 Saved to: {output_file}")
    print(f"📊 Total data size: {len(json.dumps(dataset)) / 1024:.2f} KB")
    print("\n📋 Dataset Summary:")
    for key, value in dataset['metadata']['total_records'].items():
        print(f"   • {key.replace('_', ' ').title()}: {value}")
    
    print("\n🎯 Next steps:")
    print("   1. Import this data into your development database")
    print("   2. Use for frontend development and testing")
    print("   3. Demonstrate application features with realistic data")
    print("   4. Run tests against this sample dataset")

if __name__ == '__main__':
    main()