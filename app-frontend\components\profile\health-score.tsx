"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Target,
  Zap
} from "lucide-react";
import { AccountData } from "@/hooks/use-connected-accounts";

interface HealthScoreProps {
  accounts: AccountData[];
  timeframe: string;
}

export function HealthScore({ accounts, timeframe }: HealthScoreProps) {
  // Calculate health score based on multiple factors
  const calculateHealthScore = (): number => {
    if (!accounts || accounts.length === 0) return 0;

    let totalScore = 0;
    let factors = 0;

    // Growth momentum (40% weight)
    const avgGrowth = accounts.reduce((sum, acc) => sum + (acc.metrics?.growth?.followers || 0), 0) / accounts.length;
    const growthScore = Math.min(Math.max((avgGrowth + 10) / 20 * 100, 0), 100); // Normalize to 0-100
    totalScore += growthScore * 0.4;
    factors += 0.4;

    // Engagement quality (35% weight)
    const avgEngagement = accounts.reduce((sum, acc) => sum + (acc.metrics?.engagement || 0), 0) / accounts.length;
    const engagementScore = Math.min(avgEngagement * 20, 100); // 5% engagement = 100 score
    totalScore += engagementScore * 0.35;
    factors += 0.35;

    // Platform diversity (25% weight)
    const diversityScore = (accounts.length / 4) * 100; // Up to 4 platforms
    totalScore += Math.min(diversityScore, 100) * 0.25;
    factors += 0.25;

    return Math.round(totalScore / factors);
  };

  const healthScore = calculateHealthScore();
  
  const getHealthLevel = (score: number): { level: string; color: string; description: string } => {
    if (score >= 80) {
      return {
        level: "Excellent",
        color: "text-green-600 bg-green-50 border-green-200",
        description: "Your social media presence is thriving across all metrics!"
      };
    } else if (score >= 60) {
      return {
        level: "Good",
        color: "text-blue-600 bg-blue-50 border-blue-200",
        description: "Strong performance with room for optimization."
      };
    } else if (score >= 40) {
      return {
        level: "Fair",
        color: "text-yellow-600 bg-yellow-50 border-yellow-200",
        description: "Decent foundation but needs improvement in key areas."
      };
    } else {
      return {
        level: "Needs Work",
        color: "text-red-600 bg-red-50 border-red-200",
        description: "Focus on growth strategies and engagement improvement."
      };
    }
  };

  const health = getHealthLevel(healthScore);

  // Calculate individual component scores
  const avgGrowth = accounts.reduce((sum, acc) => sum + (acc.metrics?.growth?.followers || 0), 0) / accounts.length;
  const avgEngagement = accounts.reduce((sum, acc) => sum + (acc.metrics?.engagement || 0), 0) / accounts.length;
  const diversity = accounts.length;

  const components = [
    {
      label: "Growth Momentum",
      score: Math.min(Math.max((avgGrowth + 10) / 20 * 100, 0), 100),
      icon: TrendingUp,
      description: `${avgGrowth >= 0 ? '+' : ''}${avgGrowth.toFixed(1)}% average growth`,
      color: avgGrowth >= 5 ? "text-green-600" : avgGrowth >= 0 ? "text-yellow-600" : "text-red-600"
    },
    {
      label: "Engagement Quality",
      score: Math.min(avgEngagement * 20, 100),
      icon: Activity,
      description: `${avgEngagement.toFixed(1)}% average engagement rate`,
      color: avgEngagement >= 3 ? "text-green-600" : avgEngagement >= 1.5 ? "text-yellow-600" : "text-red-600"
    },
    {
      label: "Platform Reach",
      score: Math.min((diversity / 4) * 100, 100),
      icon: Target,
      description: `Active on ${diversity} platform${diversity !== 1 ? 's' : ''}`,
      color: diversity >= 3 ? "text-green-600" : diversity >= 2 ? "text-yellow-600" : "text-red-600"
    }
  ];

  return (
    <Card className={`${health.color} border-2`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-lg bg-current/10 flex items-center justify-center">
              <Zap className="w-6 h-6" />
            </div>
            <div>
              <CardTitle className="text-xl">Social Media Health Score</CardTitle>
              <p className="text-sm opacity-80">{health.description}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{healthScore}</div>
            <Badge variant="secondary" className="mt-1">
              {health.level}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Overall Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Health</span>
            <span className="font-medium">{healthScore}/100</span>
          </div>
          <Progress value={healthScore} className="h-3" />
        </div>

        {/* Component Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {components.map((component, index) => {
            const Icon = component.icon;
            return (
              <div key={index} className="bg-white/50 rounded-lg p-4 space-y-2">
                <div className="flex items-center gap-2">
                  <Icon className={`w-4 h-4 ${component.color}`} />
                  <span className="text-sm font-medium">{component.label}</span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Score</span>
                    <span className="font-medium">{Math.round(component.score)}</span>
                  </div>
                  <Progress value={component.score} className="h-2" />
                  <p className="text-xs text-muted-foreground">{component.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Recommendations */}
        <div className="bg-white/50 rounded-lg p-4">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Target className="w-4 h-4" />
            Recommendations
          </h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            {avgGrowth < 3 && (
              <li>• Focus on growth strategies and consistent posting schedules</li>
            )}
            {avgEngagement < 2 && (
              <li>• Improve engagement by creating more interactive content</li>
            )}
            {diversity < 2 && (
              <li>• Consider expanding to additional social media platforms</li>
            )}
            {healthScore >= 80 && (
              <li>• Great work! Maintain your current strategy and explore new content formats</li>
            )}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}