@echo off
setlocal EnableDelayedExpansion

REM Sample Data Generation Script for Social Media Manager (Windows)
REM This script automates the process of generating and seeding sample data

echo.
echo 🚀 Social Media Manager - Sample Data Setup
echo ==============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is required but not found. Please install Python 3.x
    pause
    exit /b 1
)
echo ✅ Python found

REM Change to script directory
cd /d "%~dp0"

REM Check Python dependencies
echo.
echo 📦 Checking Python dependencies...
python -c "import google.cloud.firestore" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  WARNING: google-cloud-firestore not found. Installing...
    pip install google-cloud-firestore
    if errorlevel 1 (
        echo ❌ ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)
echo ✅ Dependencies verified

REM Generate sample data
echo.
echo 🎲 Generating sample data...
python generate_sample_data.py
if errorlevel 1 (
    echo ❌ ERROR: Failed to generate sample data
    pause
    exit /b 1
)
echo ✅ Sample data generated successfully

REM Ask about database seeding
echo.
set /p seeddb="Do you want to seed the database with sample data? (y/N): "
if /i "!seeddb!"=="y" (
    echo.
    echo 🌱 Preparing to seed database...
    
    REM Check for Google Cloud credentials
    if "%GOOGLE_APPLICATION_CREDENTIALS%"=="" (
        where gcloud >nul 2>&1
        if errorlevel 1 (
            echo ⚠️  WARNING: Google Cloud credentials not found.
            echo Please set GOOGLE_APPLICATION_CREDENTIALS or run 'gcloud auth application-default login'
            echo Skipping database seeding...
            goto :show_next_steps
        )
    )
    
    REM Ask about clearing existing data
    set /p cleardata="Clear existing data before seeding? (y/N): "
    if /i "!cleardata!"=="y" (
        python seed_database.py --clear --env dev
    ) else (
        python seed_database.py --env dev
    )
    
    if errorlevel 1 (
        echo ❌ ERROR: Failed to seed database
        pause
        exit /b 1
    )
    echo ✅ Database seeded successfully
) else (
    echo ℹ️  Skipping database seeding
)

:show_next_steps
echo.
echo 🎉 Sample data setup complete!
echo.
echo 📋 Next steps:
echo    1. Start your backend server:
echo       cd ..\app-agents ^&^& python -m uvicorn app.main:app --reload
echo.
echo    2. Start your frontend application:
echo       cd ..\app-frontend ^&^& npm run dev
echo.
echo    3. Use sample users for testing:
echo       • <EMAIL> (Tech Entrepreneur)
echo       • <EMAIL> (Marketing Strategist)
echo       • <EMAIL> (Fitness Coach)
echo.
echo 📁 Generated files:
echo    • sample_data.json (Contains all sample data)
echo.
echo 🔗 Useful links:
echo    • Frontend: http://localhost:3000
echo    • Backend API: http://localhost:8000/docs
echo    • Firestore Console: https://console.firebase.google.com/
echo.

pause