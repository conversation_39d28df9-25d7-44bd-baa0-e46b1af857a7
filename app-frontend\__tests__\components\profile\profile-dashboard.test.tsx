import React from 'react'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ProfileDashboard } from '@/components/profile/profile-dashboard'
import { renderWithProviders, mockPlatformData } from '../../utils/test-utils'

// Mock the custom hooks
jest.mock('@/hooks/use-connected-accounts', () => ({
  useConnectedAccounts: jest.fn(),
}))

jest.mock('@/hooks/use-platform-metrics', () => ({
  usePlatformMetrics: jest.fn(),
}))

// Mock child components
jest.mock('@/components/profile/platform-tile', () => ({
  PlatformTile: ({ account }: any) => (
    <div data-testid={`platform-tile-${account.platform}`}>
      {account.platform} - {account.metrics?.followers} followers
    </div>
  ),
}))

jest.mock('@/components/profile/health-score', () => ({
  HealthScore: ({ accounts }: any) => (
    <div data-testid="health-score">
      Health Score for {accounts.length} accounts
    </div>
  ),
}))

jest.mock('@/components/profile/metrics-chart', () => ({
  MetricsChart: ({ data }: any) => (
    <div data-testid="metrics-chart">
      Chart with {data?.length || 0} data points
    </div>
  ),
}))

const mockUseConnectedAccounts = require('@/hooks/use-connected-accounts').useConnectedAccounts
const mockUsePlatformMetrics = require('@/hooks/use-platform-metrics').usePlatformMetrics

describe('ProfileDashboard', () => {
  const mockRefetch = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUsePlatformMetrics.mockReturnValue({
      data: null,
      isLoading: false,
    })
  })

  describe('Loading State', () => {
    it('shows loading skeleton when accounts are loading', () => {
      mockUseConnectedAccounts.mockReturnValue({
        data: undefined,
        isLoading: true,
        refetch: mockRefetch,
      })

      renderWithProviders(<ProfileDashboard />)
      
      // Should show 3 skeleton cards
      const skeletonCards = screen.getAllByRole('generic').filter(el => 
        el.className.includes('animate-pulse')
      )
      expect(skeletonCards).toHaveLength(3)
    })
  })

  describe('Empty State', () => {
    it('shows empty state when no accounts are connected', () => {
      mockUseConnectedAccounts.mockReturnValue({
        data: [],
        isLoading: false,
        refetch: mockRefetch,
      })

      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByText('No Connected Accounts')).toBeInTheDocument()
      expect(screen.getByText(/Connect your social media accounts/)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /connect your first account/i })).toBeInTheDocument()
    })

    it('redirects to connections page when connect button is clicked', async () => {
      const user = userEvent.setup()
      
      // Mock window.location
      delete (window as any).location
      window.location = { href: '' } as any

      mockUseConnectedAccounts.mockReturnValue({
        data: [],
        isLoading: false,
        refetch: mockRefetch,
      })

      renderWithProviders(<ProfileDashboard />)
      
      const connectButton = screen.getByRole('button', { name: /connect your first account/i })
      await user.click(connectButton)
      
      expect(window.location.href).toBe('/connections')
    })
  })

  describe('Dashboard with Connected Accounts', () => {
    const mockAccounts = [
      mockPlatformData('youtube', {
        id: 'yt-1',
        metrics: {
          followers: 10000,
          engagement: 5.2,
          growth: { followers: 2.1 },
        },
      }),
      mockPlatformData('instagram', {
        id: 'ig-1',
        metrics: {
          followers: 8500,
          engagement: 4.8,
          growth: { followers: 1.5 },
        },
      }),
    ]

    beforeEach(() => {
      mockUseConnectedAccounts.mockReturnValue({
        data: mockAccounts,
        isLoading: false,
        refetch: mockRefetch,
      })
    })

    it('renders dashboard header correctly', () => {
      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument()
      expect(screen.getByText(/Overview of your social media performance/)).toBeInTheDocument()
    })

    it('displays timeframe selector with correct options', () => {
      renderWithProviders(<ProfileDashboard />)
      
      const select = screen.getByRole('combobox')
      expect(select).toHaveValue('30d')
      
      // Check options
      expect(screen.getByRole('option', { name: /last 7 days/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /last 30 days/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /last 90 days/i })).toBeInTheDocument()
    })

    it('shows refresh button and calls refetch when clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfileDashboard />)
      
      const refreshButton = screen.getByRole('button', { name: /refresh/i })
      await user.click(refreshButton)
      
      expect(mockRefetch).toHaveBeenCalledTimes(1)
    })

    it('calculates and displays total followers correctly', () => {
      renderWithProviders(<ProfileDashboard />)
      
      // 10000 + 8500 = 18500
      expect(screen.getByText('18,500')).toBeInTheDocument()
      expect(screen.getByText('Total Followers')).toBeInTheDocument()
    })

    it('calculates and displays average engagement correctly', () => {
      renderWithProviders(<ProfileDashboard />)
      
      // (5.2 + 4.8) / 2 = 5.0
      expect(screen.getByText('5.0%')).toBeInTheDocument()
      expect(screen.getByText('Avg. Engagement')).toBeInTheDocument()
    })

    it('displays connected platforms count', () => {
      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByText('Connected Platforms')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('youtube, instagram')).toBeInTheDocument()
    })

    it('shows growth indicators with correct colors', () => {
      renderWithProviders(<ProfileDashboard />)
      
      // Should show positive growth
      const growthElements = screen.getAllByText(/\+.*%/)
      expect(growthElements.length).toBeGreaterThan(0)
    })

    it('renders health score component', () => {
      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByTestId('health-score')).toBeInTheDocument()
      expect(screen.getByText('Health Score for 2 accounts')).toBeInTheDocument()
    })

    it('renders platform tiles for each connected account', () => {
      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByTestId('platform-tile-youtube')).toBeInTheDocument()
      expect(screen.getByTestId('platform-tile-instagram')).toBeInTheDocument()
      expect(screen.getByText('youtube - 10000 followers')).toBeInTheDocument()
      expect(screen.getByText('instagram - 8500 followers')).toBeInTheDocument()
    })
  })

  describe('Timeframe Selection', () => {
    beforeEach(() => {
      mockUseConnectedAccounts.mockReturnValue({
        data: [mockPlatformData('youtube')],
        isLoading: false,
        refetch: mockRefetch,
      })
    })

    it('updates timeframe when selection changes', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfileDashboard />)
      
      const select = screen.getByRole('combobox')
      await user.selectOptions(select, '7d')
      
      expect(select).toHaveValue('7d')
    })

    it('calls usePlatformMetrics with updated timeframe', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfileDashboard />)
      
      const select = screen.getByRole('combobox')
      await user.selectOptions(select, '90d')
      
      // The hook should be called with the new timeframe
      await waitFor(() => {
        expect(mockUsePlatformMetrics).toHaveBeenCalledWith('90d')
      })
    })

    it('accepts custom initial timeframe prop', () => {
      renderWithProviders(<ProfileDashboard timeframe="7d" />)
      
      const select = screen.getByRole('combobox')
      expect(select).toHaveValue('7d')
    })
  })

  describe('Negative Growth Handling', () => {
    it('displays negative growth with correct styling', () => {
      const accountsWithNegativeGrowth = [
        mockPlatformData('youtube', {
          metrics: {
            followers: 10000,
            engagement: 5.2,
            growth: { followers: -1.2 },
          },
        }),
      ]

      mockUseConnectedAccounts.mockReturnValue({
        data: accountsWithNegativeGrowth,
        isLoading: false,
        refetch: mockRefetch,
      })

      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByText('-1.2%')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      mockUseConnectedAccounts.mockReturnValue({
        data: [mockPlatformData('youtube')],
        isLoading: false,
        refetch: mockRefetch,
      })
    })

    it('has proper ARIA labels and semantic HTML', () => {
      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByRole('combobox')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /refresh/i })).toBeInTheDocument()
    })

    it('maintains proper heading hierarchy', () => {
      renderWithProviders(<ProfileDashboard />)
      
      expect(screen.getByRole('heading', { level: 1, name: /analytics dashboard/i })).toBeInTheDocument()
      expect(screen.getByRole('heading', { level: 2, name: /platform performance/i })).toBeInTheDocument()
    })
  })
})