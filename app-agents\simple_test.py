#!/usr/bin/env python3
"""Simple test for ADK models"""

import sys
sys.path.append('app')

from app.models import (
    ADKContentPart, 
    ADKMessage, 
    MessageRole,
    ADKRunAgentRequest,
    StreamingChunk
)

print("Testing ADK models...")

# Test 1: Content Part
try:
    part = ADKContentPart(text="Hello, world!")
    print("✅ ADKContentPart created successfully")
except Exception as e:
    print(f"❌ ADKContentPart failed: {e}")

# Test 2: Message
try:
    message = ADKMessage.from_text("Hello", MessageRole.USER)
    print("✅ ADKMessage created successfully")
except Exception as e:
    print(f"❌ ADKMessage failed: {e}")

# Test 3: Request
try:
    request = ADKRunAgentRequest(
        app_name="test_agent",
        user_id="user123",
        session_id="session456",
        new_message=ADKMessage.from_text("Hello", MessageRole.USER)
    )
    print("✅ ADKRunAgentRequest created successfully")
except Exception as e:
    print(f"❌ ADKRunAgentRequest failed: {e}")

# Test 4: Streaming Chunk
try:
    chunk = StreamingChunk(
        content="Hello",
        done=False,
        message_id="msg123"
    )
    print("✅ StreamingChunk created successfully")
except Exception as e:
    print(f"❌ StreamingChunk failed: {e}")

print("All basic tests completed!")