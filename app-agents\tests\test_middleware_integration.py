"""
Integration tests for ADK error handling middleware with FastAPI application.

This module tests the middleware integration with the actual FastAPI app
to ensure proper error handling in real request/response scenarios.
"""

import pytest
import json
import asyncio
from unittest.mock import AsyncMock, patch, Mock
from datetime import datetime

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.testclient import <PERSON><PERSON><PERSON>
from fastapi.responses import StreamingResponse

from app.core.error_handling import (
    ADKIntegrationError,
    ADKServerUnavailableError,
    ADKAgentNotFoundError,
    ADKStreamingError,
    ErrorHandler
)
from app.core.middleware import (
    ADKErrorHandlingMiddleware,
    ADKHealthCheckMiddleware,
    create_adk_error_middleware,
    create_adk_health_middleware
)
from app.models.chat_models import StreamingChunk


# Test FastAPI app with middleware
def create_test_app():
    """Create test FastAPI app with error handling middleware"""
    app = FastAPI()
    
    # Add error handling middleware
    app.add_middleware(
        ADKErrorHandlingMiddleware,
        enable_fallback=True,
        fallback_service_url="http://localhost:8000"
    )
    
    # Add health check middleware
    app.add_middleware(
        ADKHealthCheckMiddleware,
        health_check_interval=30,
        failure_threshold=3,
        recovery_timeout=60
    )
    
    @app.get("/test/success")
    async def success_endpoint():
        return {"status": "success"}
    
    @app.get("/test/adk-error")
    async def adk_error_endpoint():
        raise ADKServerUnavailableError("Test ADK error")
    
    @app.get("/test/agent-not-found")
    async def agent_not_found_endpoint():
        raise ADKAgentNotFoundError(
            agent_name="missing_agent",
            available_agents=["agent1", "agent2"]
        )
    
    @app.get("/test/streaming-error")
    async def streaming_error_endpoint():
        async def error_stream():
            yield "data: initial\n\n"
            raise ADKStreamingError("Stream failed")
        
        return StreamingResponse(
            error_stream(),
            media_type="text/event-stream"
        )
    
    @app.get("/test/generic-error")
    async def generic_error_endpoint():
        raise ValueError("Generic error")
    
    @app.get("/test/adk-related-error")
    async def adk_related_error_endpoint():
        raise ConnectionError("ADK connection timeout")
    
    @app.get("/test/stream")
    async def stream_endpoint():
        async def test_stream():
            for i in range(3):
                chunk = StreamingChunk(
                    content=f"Chunk {i}",
                    done=(i == 2),
                    message_id=f"msg_{i}",
                    metadata={"chunk_number": i}
                )
                yield f"data: {chunk.model_dump_json()}\n\n"
        
        return StreamingResponse(
            test_stream(),
            media_type="text/event-stream"
        )
    
    return app


class TestMiddlewareIntegration:
    """Test middleware integration with FastAPI application"""
    
    @pytest.fixture
    def app(self):
        """Create test FastAPI app"""
        return create_test_app()
    
    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return TestClient(app)
    
    def test_successful_request_passes_through(self, client):
        """Test that successful requests pass through middleware unchanged"""
        response = client.get("/test/success")
        
        assert response.status_code == 200
        assert response.json() == {"status": "success"}
        
        # Check that middleware added headers
        assert "X-Request-ID" in response.headers
        assert "X-Processing-Time" in response.headers
    
    def test_adk_server_unavailable_error_handling(self, client):
        """Test ADK server unavailable error is handled properly"""
        response = client.get("/test/adk-error")
        
        assert response.status_code == 503
        assert response.headers["content-type"] == "application/json"
        
        data = response.json()
        assert data["error"] is True
        assert data["error_code"] == "ADK_SERVER_UNAVAILABLE"
        assert "temporarily unavailable" in data["message"].lower()
        assert "X-Request-ID" in response.headers
        assert "X-Error-Code" in response.headers
    
    def test_agent_not_found_error_handling(self, client):
        """Test agent not found error is handled properly"""
        response = client.get("/test/agent-not-found")
        
        assert response.status_code == 404
        assert response.headers["content-type"] == "application/json"
        
        data = response.json()
        assert data["error"] is True
        assert data["error_code"] == "ADK_AGENT_NOT_FOUND"
        assert "not available" in data["message"].lower()
        assert "agent1" in data["message"]
        assert "agent2" in data["message"]
    
    def test_streaming_error_handling(self, client):
        """Test streaming endpoint error handling"""
        response = client.get("/test/streaming-error")
        
        assert response.status_code == 200  # Streaming starts successfully
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
        
        # Read the stream content
        content = response.content.decode()
        lines = content.strip().split('\n')
        
        # Should have initial data and error data
        assert len(lines) >= 2
        assert "data: initial" in lines[0]
        
        # Find error chunk
        error_line = None
        for line in lines:
            if line.startswith("data: ") and "error" in line:
                error_line = line
                break
        
        assert error_line is not None
        error_data = json.loads(error_line[6:])  # Remove "data: "
        assert error_data["metadata"]["error"] is True
        assert error_data["metadata"]["error_code"] == "ADK_STREAMING_ERROR"
    
    def test_generic_error_not_wrapped(self, client):
        """Test that generic non-ADK errors are not wrapped"""
        with pytest.raises(Exception):
            # This should raise the original ValueError, not be caught by middleware
            response = client.get("/test/generic-error")
    
    def test_adk_related_generic_error_wrapped(self, client):
        """Test that ADK-related generic errors are wrapped"""
        response = client.get("/test/adk-related-error")
        
        assert response.status_code == 503
        data = response.json()
        assert data["error"] is True
        assert data["error_code"] == "ADK_SERVER_UNAVAILABLE"
    
    def test_successful_streaming_request(self, client):
        """Test that successful streaming requests work properly"""
        response = client.get("/test/stream")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
        
        content = response.content.decode()
        lines = [line for line in content.split('\n') if line.strip()]
        
        # Should have 3 data lines
        data_lines = [line for line in lines if line.startswith("data: ")]
        assert len(data_lines) == 3
        
        # Check each chunk
        for i, line in enumerate(data_lines):
            chunk_data = json.loads(line[6:])  # Remove "data: "
            assert chunk_data["content"] == f"Chunk {i}"
            assert chunk_data["message_id"] == f"msg_{i}"
            assert chunk_data["done"] == (i == 2)


class TestHealthCheckMiddleware:
    """Test health check middleware functionality"""
    
    @pytest.fixture
    def app_with_health_middleware(self):
        """Create app with health check middleware"""
        app = FastAPI()
        
        # Add health middleware with low thresholds for testing
        app.add_middleware(
            ADKHealthCheckMiddleware,
            health_check_interval=1,
            failure_threshold=2,
            recovery_timeout=5
        )
        
        @app.get("/api/chat/test")
        async def adk_endpoint():
            return {"status": "ok"}
        
        @app.get("/api/health")
        async def health_endpoint():
            return {"status": "healthy"}
        
        return app
    
    @pytest.fixture
    def health_client(self, app_with_health_middleware):
        """Create client for health middleware testing"""
        return TestClient(app_with_health_middleware)
    
    def test_health_middleware_allows_normal_requests(self, health_client):
        """Test health middleware allows normal requests"""
        response = health_client.get("/api/chat/test")
        assert response.status_code == 200
    
    def test_health_middleware_allows_non_adk_requests(self, health_client):
        """Test health middleware allows non-ADK requests"""
        response = health_client.get("/api/health")
        assert response.status_code == 200
    
    @patch('app.core.middleware.ADKHealthCheckMiddleware._perform_health_check')
    def test_health_check_is_called(self, mock_health_check, health_client):
        """Test that health check is performed periodically"""
        mock_health_check.return_value = None
        
        # Make request to trigger health check
        response = health_client.get("/api/chat/test")
        assert response.status_code == 200
        
        # Health check should be called (though timing may vary)
        # This is more of a smoke test for the health check mechanism


class TestMiddlewareFactories:
    """Test middleware factory functions"""
    
    def test_create_adk_error_middleware_factory(self):
        """Test ADK error middleware factory"""
        factory = create_adk_error_middleware(
            enable_fallback=True,
            fallback_service_url="http://localhost:8000"
        )
        
        # Factory should be callable
        assert callable(factory)
        
        # Create mock app
        mock_app = Mock()
        middleware = factory(mock_app)
        
        assert isinstance(middleware, ADKErrorHandlingMiddleware)
        assert middleware.enable_fallback is True
        assert middleware.fallback_service_url == "http://localhost:8000"
    
    def test_create_adk_health_middleware_factory(self):
        """Test ADK health middleware factory"""
        factory = create_adk_health_middleware(
            health_check_interval=60,
            failure_threshold=5,
            recovery_timeout=120
        )
        
        # Factory should be callable
        assert callable(factory)
        
        # Create mock app
        mock_app = Mock()
        middleware = factory(mock_app)
        
        assert isinstance(middleware, ADKHealthCheckMiddleware)
        assert middleware.health_check_interval == 60
        assert middleware.failure_threshold == 5
        assert middleware.recovery_timeout == 120


class TestErrorHandlerLogging:
    """Test error handler logging functionality"""
    
    @pytest.fixture
    def mock_logger(self):
        """Create mock logger"""
        return Mock()
    
    @pytest.fixture
    def error_handler_with_logger(self, mock_logger):
        """Create error handler with mock logger"""
        return ErrorHandler(mock_logger)
    
    def test_error_logging_by_severity(self, error_handler_with_logger, mock_logger):
        """Test that errors are logged with appropriate levels based on severity"""
        from app.core.error_handling import ErrorSeverity
        
        # Test critical error logging
        critical_error = ADKIntegrationError(
            message="Critical error",
            severity=ErrorSeverity.CRITICAL
        )
        error_handler_with_logger.log_error(critical_error)
        mock_logger.critical.assert_called_once()
        
        # Reset mock
        mock_logger.reset_mock()
        
        # Test high severity error logging
        high_error = ADKServerUnavailableError()  # Default is HIGH
        error_handler_with_logger.log_error(high_error)
        mock_logger.error.assert_called_once()
        
        # Reset mock
        mock_logger.reset_mock()
        
        # Test medium severity error logging
        medium_error = ADKAgentNotFoundError(agent_name="test")  # Default is MEDIUM
        error_handler_with_logger.log_error(medium_error)
        mock_logger.warning.assert_called_once()
        
        # Reset mock
        mock_logger.reset_mock()
        
        # Test low severity error logging
        low_error = ADKIntegrationError(
            message="Low priority error",
            severity=ErrorSeverity.LOW
        )
        error_handler_with_logger.log_error(low_error)
        mock_logger.info.assert_called_once()
    
    def test_error_logging_includes_context(self, error_handler_with_logger, mock_logger):
        """Test that error logging includes proper context"""
        error = ADKSessionError(
            message="Session failed",
            session_id="test_session",
            user_id="test_user",
            original_error=ValueError("Original error")
        )
        
        error_handler_with_logger.log_error(error)
        
        # Check that logger was called with extra context
        mock_logger.warning.assert_called_once()
        call_args = mock_logger.warning.call_args
        
        # Should have extra parameter with error details
        assert 'extra' in call_args.kwargs
        extra_data = call_args.kwargs['extra']
        
        assert extra_data['error_type'] == 'ADKSessionError'
        assert extra_data['message'] == 'Session failed'
        assert extra_data['error_code'] == 'ADK_SESSION_ERROR'
        assert extra_data['severity'] == 'medium'
        assert 'session_id' in extra_data['details']
        assert 'user_id' in extra_data['details']


class TestFallbackBehavior:
    """Test fallback behavior when ADK is unavailable"""
    
    @pytest.fixture
    def app_with_fallback(self):
        """Create app with fallback enabled"""
        app = FastAPI()
        
        app.add_middleware(
            ADKErrorHandlingMiddleware,
            enable_fallback=True,
            fallback_service_url="http://localhost:8000"
        )
        
        @app.get("/api/chat/send")
        async def chat_endpoint():
            raise ADKServerUnavailableError("ADK is down")
        
        @app.get("/api/chat/stream")
        async def stream_endpoint():
            async def failing_stream():
                yield "data: starting\n\n"
                raise ADKServerUnavailableError("ADK streaming failed")
            
            return StreamingResponse(
                failing_stream(),
                media_type="text/event-stream"
            )
        
        return app
    
    @pytest.fixture
    def fallback_client(self, app_with_fallback):
        """Create client for fallback testing"""
        return TestClient(app_with_fallback)
    
    def test_fallback_response_in_json_endpoint(self, fallback_client):
        """Test fallback response in JSON endpoint"""
        response = fallback_client.get("/api/chat/send")
        
        assert response.status_code == 503
        data = response.json()
        
        assert data["error"] is True
        assert data["error_code"] == "ADK_SERVER_UNAVAILABLE"
        # Should include fallback information if implemented
    
    def test_fallback_response_in_streaming_endpoint(self, fallback_client):
        """Test fallback response in streaming endpoint"""
        response = fallback_client.get("/api/chat/stream")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
        
        content = response.content.decode()
        lines = content.strip().split('\n')
        
        # Should have initial data and error/fallback data
        assert len(lines) >= 2
        assert "data: starting" in lines[0]
        
        # Should have error chunk
        error_chunks = [line for line in lines if line.startswith("data: ") and "error" in line]
        assert len(error_chunks) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])