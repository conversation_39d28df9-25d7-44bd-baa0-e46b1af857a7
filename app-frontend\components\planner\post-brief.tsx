"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Youtube,
  Instagram,
  Twitter,
  Clock,
  Hash,
  Target,
  Edit,
  MoreHorizontal,
  Copy
} from "lucide-react";

interface Post {
  id: string;
  date: Date;
  platform: string;
  title: string;
  hook: string;
  status: string;
  bestTime: string;
  hashtags: string[];
  outline?: string;
  cta?: string;
}

interface PostBriefProps {
  post: Post;
}

export function PostBrief({ post }: PostBriefProps) {
  const platformIcons = {
    youtube: Youtube,
    instagram: Instagram,
    twitter: Twitter,
  };

  const platformColors = {
    youtube: "text-red-500 bg-red-50 border-red-200",
    instagram: "text-pink-500 bg-pink-50 border-pink-200",
    twitter: "text-blue-500 bg-blue-50 border-blue-200",
  };

  const statusColors = {
    scheduled: "bg-green-100 text-green-700 border-green-200",
    draft: "bg-yellow-100 text-yellow-700 border-yellow-200",
    idea: "bg-blue-100 text-blue-700 border-blue-200",
    published: "bg-gray-100 text-gray-700 border-gray-200"
  };

  const Icon = platformIcons[post.platform as keyof typeof platformIcons];
  const platformColor = platformColors[post.platform as keyof typeof platformColors];
  const statusColor = statusColors[post.status as keyof typeof statusColors];

  const mockData = {
    outline: "Introduction → Main points → Practical examples → Call to action",
    cta: "Subscribe for more tutorials and hit the bell icon!",
    estimatedReach: "5.2K - 8.1K",
    bestPerformingTime: post.bestTime,
    engagementPrediction: "4.2%"
  };

  return (
    <Card className={`${platformColor} transition-all hover:shadow-md`}>
      <CardContent className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3 flex-1">
            <div className="w-10 h-10 rounded-lg bg-white/50 flex items-center justify-center">
              {Icon && <Icon className="w-5 h-5" />}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="outline" className="text-xs capitalize">
                  {post.platform}
                </Badge>
                <Badge className={`text-xs ${statusColor}`}>
                  {post.status}
                </Badge>
              </div>
              <h3 className="font-semibold text-sm leading-tight">{post.title}</h3>
            </div>
          </div>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>

        {/* Hook */}
        <div className="mb-3">
          <p className="text-sm text-muted-foreground mb-1">Hook</p>
          <p className="text-sm italic bg-white/50 p-2 rounded">"{post.hook}"</p>
        </div>

        {/* Outline */}
        {mockData.outline && (
          <div className="mb-3">
            <p className="text-sm text-muted-foreground mb-1">Outline</p>
            <p className="text-sm bg-white/50 p-2 rounded">{mockData.outline}</p>
          </div>
        )}

        {/* CTA */}
        {mockData.cta && (
          <div className="mb-3">
            <div className="flex items-center gap-1 mb-1">
              <Target className="w-3 h-3 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">Call to Action</p>
            </div>
            <p className="text-sm bg-white/50 p-2 rounded">{mockData.cta}</p>
          </div>
        )}

        {/* Hashtags */}
        {post.hashtags.length > 0 && (
          <div className="mb-3">
            <div className="flex items-center gap-1 mb-1">
              <Hash className="w-3 h-3 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">Hashtags</p>
            </div>
            <div className="flex flex-wrap gap-1">
              {post.hashtags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Timing and Predictions */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="bg-white/50 rounded p-2">
            <div className="flex items-center gap-1 mb-1">
              <Clock className="w-3 h-3 text-muted-foreground" />
              <p className="text-xs text-muted-foreground">Best Time</p>
            </div>
            <p className="text-sm font-medium">{post.bestTime}</p>
          </div>
          <div className="bg-white/50 rounded p-2">
            <p className="text-xs text-muted-foreground mb-1">Est. Reach</p>
            <p className="text-sm font-medium">{mockData.estimatedReach}</p>
          </div>
        </div>

        {/* Performance Prediction */}
        <div className="bg-white/50 rounded p-2 mb-3">
          <p className="text-xs text-muted-foreground mb-1">Engagement Prediction</p>
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium">{mockData.engagementPrediction}</p>
            <Badge variant="outline" className="text-xs">
              Above Average
            </Badge>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex-1">
            <Edit className="w-4 h-4 mr-1" />
            Edit
          </Button>
          <Button variant="outline" size="sm" className="flex-1">
            <Copy className="w-4 h-4 mr-1" />
            Duplicate
          </Button>
          {post.status === 'draft' && (
            <Button size="sm" className="flex-1">
              Schedule
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}