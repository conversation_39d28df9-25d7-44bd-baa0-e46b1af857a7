# Social Media Manager Agent

## Goal
Build a production-ready web app where creators connect their YouTube & Instagram (now) and chat with one manager agent that delegates to platform analyzers, does grounded web research, and returns insights + a content plan. Future: TikTok & X.

## Tech Stack

### Frontend
- **Framework**: Next.js (App Router) + React + TypeScript
- **Styling**: Tailwind CSS, shadcn/ui
- **Data Management**: TanStack Query
- **Forms**: React Hook Form + Zod

### Backend
- **API**: Python FastAPI with Google ADK (Python) agents

### Cloud Infrastructure
- **Compute**: Google Cloud Run (containers)
- **Database**: Firestore (NoSQL)
- **Analytics**: BigQuery
- **Security**: Secret Manager
- **Monitoring**: Cloud Logging/Trace
- **Optional**: Vertex AI Agent Engine/Builder for sessions/evals (preview)

### APIs
- YouTube Data API v3
- Instagram Graph API (Professional)
- TikTok Developer/Business API (stub now)
- X API v2 (stub now)

## Architecture

### Main Components
- **ADK Coordinator/Dispatcher**: Main agent that orchestrates all operations
- **Sub-agents**:
  - `YouTubeAnalyzer`
  - `InstagramAnalyzer`
  - `ResearchAgent` (uses Google Search Grounding)
  - `ContentPlanner`

### Data Flow
Each sub-agent exposes an `analyze()` method that returns a typed JSON schema (metrics, derived KPIs, suggestions). The Coordinator composes the final answer.

## UI Requirements

### Chat Page (Center)
- Streaming assistant replies
- System prompt: "You're talking to your social media manager."

### Sidebar (Left) - "Accounts"
- **Connect YouTube**: Accept channel handle/URL if read-only
- **Connect Instagram**: OAuth to IG Professional
- **Placeholders**: TikTok/X
- **Display**: Avatar, handle, last sync

### Profile Page
- Tiles per platform (KPIs & sparklines)
- Overall health score
- Month-over-month growth

### Planner Page
- Calendar view (2–4 weeks)
- Per-post brief (title, hook, outline, CTA, best time)
- Export to CSV/Sheets

## Backend Contracts

### Authentication & Connection
```http
POST /auth/callback/{platform}
# Completes OAuth; stores encrypted tokens

POST /connect/youtube
# Accepts channel URL/handle → resolves channelId for public read
```

### Core Features
```http
POST /chat
# Body: { userId, message }
# Returns: streaming response; server calls ADK Coordinator with session.state.accounts

GET /profile
# Returns: latest summaries (cached) per platform

POST /planner/generate
# Params: timeframe, goals
# Returns: plan JSON

POST /planner/save
# Saves generated plan
```

## Data Storage

### Firestore Collections
- `users`
- `connections`
- `snapshots`
- `insights`
- `plans`

### BigQuery Tables
- `metrics_daily`: Historical KPIs per platform

## Platform Tools (AgentTools)

### YouTube
- **APIs**: `channels.list`, `search.list`, `videos.list`
- **Inputs**: `channelId`
- **Outputs**: Derived KPIs (view velocity, topic clusters via tags/titles, posting cadence)

### Instagram
- **API**: IG Graph API for media/profile insights
- **Requirements**: IG Professional account
- **Notes**: Handle 2025 deprecations

### Research
- **Tool**: Google Search Grounding (`grounded_search`)
- **Purpose**: Returns top sources for trends/hashtags and fact checks

## Acceptance Criteria

### Core Functionality
1. **First Chat**: After connecting accounts, the first chat answer must reference live metrics (last 90 days) and produce at least 3 platform-specific actions

2. **Engagement Analysis**: "Why is my IG engagement down?" returns:
   - Metric deltas
   - Suspected causes (format/time/topic)
   - 3 experiments with posting windows

3. **Content Planning**: "Plan my next 2 weeks for a product drop" produces:
   - Calendar with 8–12 posts across platforms
   - Post briefs and best times
   - Working export functionality

4. **Cold Start**: (<2 accounts) returns public YouTube read-only analysis via channel URL/handle input

## Security & Observability

### Security
- All tokens stored in Secret Manager
- Firestore documents encrypted
- Refresh token rotation
- Platform-specific rate-limit backoffs

### Monitoring
- Structured logs of all AgentTool calls
- Error alerts on Cloud Monitoring

## Development Notes

### Infrastructure
- Containerize both applications
- CI/CD with GitHub Actions → Cloud Run

### Testing
- Unit tests for tool wrappers
- E2E happy-path chat tests

### Configuration
- Provide `.env.sample` with required keys and OAuth redirect URIs

## Stretch Goals

### Platform Expansion
- Add TikTok & X analyzers behind feature flags

### Advanced Features
- Add Agent Engine Sessions/Memory Bank to keep long-running strategy threads per user

## Deliverables

### Repositories
- `app-frontend`: Frontend application
- `app-agents`: Backend agents
- Both with README & setup scripts

### Demo Data
- Seed script that ingests 10 sample videos & 10 IG posts to demo charts without real tokens

### UI Screens
- Chat interface
- Profile dashboard
- Content planner
- Connections modal
- Empty-state views

## Key References

### Google Cloud & ADK
- [Google ADK docs & multi-agent patterns (Coordinator/Dispatcher)]()
- [Vertex AI Agent Builder/Engine overview + ADK quickstart (sessions, memory, tools, preview)]()

### Platform APIs
- [YouTube Data API v3 (channels/videos)]()
- [Instagram Graph API insights + 2025 changes/deprecations]()
- [TikTok developer/business docs]()
- [X (Twitter) API v2 & access tiers]()