"""
Social Media Tools - Core functionality for social media management
Provides essential functions used by the social media coordinator agent.
"""

from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

def get_user_connected_platforms(user_id: str) -> List[str]:
    """
    Get list of platforms connected for a user.
    
    Args:
        user_id (str): User identifier
        
    Returns:
        List[str]: Connected platforms
    """
    try:
        # In production, this would query the database
        # For now, returning mock data
        logger.info(f"Getting connected platforms for user: {user_id}")
        return ["instagram", "youtube"]
    except Exception as e:
        logger.error(f"Error getting connected platforms: {e}")
        return ["instagram", "youtube"]  # Fallback

def analyze_message_intent(message: str) -> Dict[str, Any]:
    """
    Analyze user message to determine intent and required actions.
    
    Args:
        message (str): User message text
        
    Returns:
        Dict[str, Any]: Intent analysis results
    """
    try:
        message_lower = message.lower()
        
        # Determine primary intent
        if any(word in message_lower for word in ["news", "latest", "trending", "current"]):
            intent = "news_research"
        elif any(word in message_lower for word in ["analyze", "performance", "metrics", "stats"]):
            intent = "analytics"
        elif any(word in message_lower for word in ["plan", "strategy", "calendar", "schedule"]):
            intent = "planning"
        elif any(word in message_lower for word in ["create", "write", "generate", "content"]):
            intent = "content_creation"
        else:
            intent = "general"
        
        # Detect platforms mentioned
        platforms = []
        if "instagram" in message_lower or "ig" in message_lower:
            platforms.append("instagram")
        if "youtube" in message_lower or "yt" in message_lower:
            platforms.append("youtube")
        if not platforms:
            platforms = ["instagram", "youtube"]  # Default to both
        
        return {
            "status": "success",
            "intent": intent,
            "platforms": platforms,
            "message": message,
            "confidence": 0.85
        }
        
    except Exception as e:
        logger.error(f"Error analyzing message intent: {e}")
        return {
            "status": "error",
            "intent": "general",
            "platforms": ["instagram", "youtube"],
            "message": message,
            "confidence": 0.5,
            "error": str(e)
        }

def get_trending_topics(platform: str = "all") -> Dict[str, Any]:
    """
    Get trending topics for specified platform(s).
    
    Args:
        platform (str): Target platform or "all"
        
    Returns:
        Dict[str, Any]: Trending topics and hashtags
    """
    try:
        trending_data = {
            "instagram": {
                "hashtags": ["#contentcreator", "#socialmedia", "#marketing", "#entrepreneur"],
                "topics": ["AI in content creation", "Authentic storytelling", "Behind-the-scenes content"],
                "trending_now": ["Reels optimization", "Story engagement", "Creator economy"]
            },
            "youtube": {
                "hashtags": ["#YouTube2025", "#ContentStrategy", "#VideoMarketing"],
                "topics": ["Long-form tutorials", "Community engagement", "Shorts optimization"],
                "trending_now": ["AI tools for creators", "Monetization strategies", "Audience retention"]
            },
            "general": {
                "hashtags": ["#DigitalMarketing", "#SocialMediaTips", "#ContentPlanning"],
                "topics": ["Cross-platform strategy", "Audience engagement", "Content repurposing"],
                "trending_now": ["Multi-platform content", "AI integration", "Creator tools"]
            }
        }
        
        if platform == "all":
            return {
                "status": "success",
                "platform": platform,
                "data": trending_data,
                "updated": "2025-08-30T23:59:59Z"
            }
        else:
            return {
                "status": "success", 
                "platform": platform,
                "data": trending_data.get(platform, trending_data["general"]),
                "updated": "2025-08-30T23:59:59Z"
            }
            
    except Exception as e:
        logger.error(f"Error getting trending topics: {e}")
        return {
            "status": "error",
            "platform": platform,
            "data": {},
            "error": str(e),
            "updated": "2025-08-30T23:59:59Z"
        }

# Additional utility functions for social media management
def format_content_for_platform(content: str, platform: str, max_length: int = None) -> Dict[str, Any]:
    """
    Format content according to platform specifications.
    
    Args:
        content (str): Original content
        platform (str): Target platform (instagram, youtube, etc.)
        max_length (int): Maximum character length
        
    Returns:
        Dict[str, Any]: Formatted content with metadata
    """
    try:
        platform_specs = {
            "instagram": {"max_caption": 2200, "max_hashtags": 30},
            "youtube": {"max_title": 100, "max_description": 5000},
            "twitter": {"max_length": 280},
            "linkedin": {"max_post": 3000}
        }
        
        spec = platform_specs.get(platform, {"max_length": 1000})
        
        if max_length:
            target_length = max_length
        elif platform == "instagram":
            target_length = spec["max_caption"]
        elif platform == "youtube":
            target_length = spec["max_description"]
        else:
            target_length = spec.get("max_length", 1000)
        
        # Truncate if necessary
        if len(content) > target_length:
            formatted_content = content[:target_length-3] + "..."
            truncated = True
        else:
            formatted_content = content
            truncated = False
        
        return {
            "status": "success",
            "platform": platform,
            "original_content": content,
            "formatted_content": formatted_content,
            "original_length": len(content),
            "formatted_length": len(formatted_content),
            "truncated": truncated,
            "target_length": target_length
        }
        
    except Exception as e:
        logger.error(f"Error formatting content: {e}")
        return {
            "status": "error",
            "error": str(e),
            "formatted_content": content
        }

def validate_hashtags(hashtags: List[str], platform: str = "instagram") -> Dict[str, Any]:
    """
    Validate hashtags for platform compliance.
    
    Args:
        hashtags (List[str]): List of hashtags to validate
        platform (str): Target platform
        
    Returns:
        Dict[str, Any]: Validation results
    """
    try:
        platform_limits = {
            "instagram": 30,
            "youtube": 15,
            "twitter": 2,
            "linkedin": 3
        }
        
        max_hashtags = platform_limits.get(platform, 10)
        
        # Clean and validate hashtags
        valid_hashtags = []
        invalid_hashtags = []
        
        for hashtag in hashtags:
            # Remove # if present and clean
            clean_tag = hashtag.lstrip('#').strip()
            
            # Basic validation
            if clean_tag and len(clean_tag) > 0 and len(clean_tag) <= 100:
                valid_hashtags.append(f"#{clean_tag}")
            else:
                invalid_hashtags.append(hashtag)
        
        # Limit to platform maximum
        if len(valid_hashtags) > max_hashtags:
            recommended_hashtags = valid_hashtags[:max_hashtags]
            excess_hashtags = valid_hashtags[max_hashtags:]
        else:
            recommended_hashtags = valid_hashtags
            excess_hashtags = []
        
        return {
            "status": "success",
            "platform": platform,
            "original_count": len(hashtags),
            "valid_count": len(valid_hashtags),
            "recommended_hashtags": recommended_hashtags,
            "excess_hashtags": excess_hashtags,
            "invalid_hashtags": invalid_hashtags,
            "max_allowed": max_hashtags,
            "within_limits": len(recommended_hashtags) <= max_hashtags
        }
        
    except Exception as e:
        logger.error(f"Error validating hashtags: {e}")
        return {
            "status": "error",
            "error": str(e),
            "recommended_hashtags": hashtags[:5]  # Safe fallback
        }