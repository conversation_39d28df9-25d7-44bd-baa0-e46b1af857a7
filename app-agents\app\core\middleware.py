"""
Middleware for ADK integration error handling and request/response processing.

This module provides middleware components that handle ADK communication failures,
add error context, and provide graceful fallback behavior.
"""

import time
import json
import logging
from typing import Callable, Dict, Any, Optional
from datetime import datetime
from fastapi import Request, Response, HTTPException
from fastapi.responses import StreamingResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.error_handling import (
    ADKIntegrationError,
    ADKServerUnavailableError,
    ADKTimeoutError,
    ErrorHandler,
    error_handler
)


class ADKErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle ADK integration errors and provide fallback behavior.
    
    This middleware:
    - Catches ADK-related exceptions
    - Provides graceful fallback responses
    - Logs errors with proper context
    - Adds error metadata to responses
    """
    
    def __init__(
        self, 
        app: ASGIApp,
        enable_fallback: bool = True,
        fallback_service_url: Optional[str] = None
    ):
        super().__init__(app)
        self.enable_fallback = enable_fallback
        self.fallback_service_url = fallback_service_url
        self.logger = logging.getLogger(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and handle ADK errors"""
        
        start_time = time.time()
        request_id = self._generate_request_id()
        
        # Add request context
        request.state.request_id = request_id
        request.state.start_time = start_time
        
        try:
            response = await call_next(request)
            
            # Add success metadata
            if hasattr(response, 'headers'):
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Processing-Time"] = str(time.time() - start_time)
            
            return response
            
        except ADKIntegrationError as adk_error:
            return await self._handle_adk_error(request, adk_error)
            
        except Exception as generic_error:
            # Wrap generic errors that might be ADK-related
            if self._is_adk_related_error(generic_error):
                adk_error = self._wrap_generic_error(generic_error)
                return await self._handle_adk_error(request, adk_error)
            else:
                # Re-raise non-ADK errors
                raise generic_error
    
    async def _handle_adk_error(
        self, 
        request: Request, 
        error: ADKIntegrationError
    ) -> Response:
        """Handle ADK integration errors with appropriate responses"""
        
        # Log the error
        error_handler.log_error(error)
        
        # Check if this is a streaming endpoint
        is_streaming = self._is_streaming_endpoint(request)
        
        if is_streaming:
            return await self._create_streaming_error_response(request, error)
        else:
            return await self._create_json_error_response(request, error)
    
    async def _create_streaming_error_response(
        self, 
        request: Request, 
        error: ADKIntegrationError
    ) -> StreamingResponse:
        """Create streaming error response for SSE endpoints"""
        
        async def error_stream():
            # Send error chunk
            error_chunk = error_handler.create_error_stream_chunk(
                error, 
                message_id=getattr(request.state, 'request_id', 'error')
            )
            yield f"data: {error_chunk.model_dump_json()}\n\n"
            
            # If fallback is enabled and this is a server unavailable error,
            # try to provide fallback response
            if (self.enable_fallback and 
                isinstance(error, ADKServerUnavailableError)):
                
                fallback_chunk = await self._get_fallback_streaming_response(request)
                if fallback_chunk:
                    yield f"data: {fallback_chunk.model_dump_json()}\n\n"
        
        return StreamingResponse(
            error_stream(),
            media_type="text/event-stream",
            headers={
                "X-Request-ID": getattr(request.state, 'request_id', ''),
                "X-Error-Code": error.error_code,
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
        )
    
    async def _create_json_error_response(
        self, 
        request: Request, 
        error: ADKIntegrationError
    ) -> Response:
        """Create JSON error response for regular endpoints"""
        
        error_response = error_handler.create_error_response(error)
        error_response["timestamp"] = datetime.utcnow().isoformat()
        error_response["request_id"] = getattr(request.state, 'request_id', '')
        
        # Determine appropriate status code
        status_code = 500
        if isinstance(error, ADKServerUnavailableError):
            status_code = 503
        elif isinstance(error, ADKTimeoutError):
            status_code = 408
        
        # If fallback is enabled, try to provide fallback response
        if (self.enable_fallback and 
            isinstance(error, ADKServerUnavailableError)):
            
            fallback_response = await self._get_fallback_json_response(request)
            if fallback_response:
                error_response["fallback_response"] = fallback_response
        
        return Response(
            content=json.dumps(error_response),
            status_code=status_code,
            media_type="application/json",
            headers={
                "X-Request-ID": getattr(request.state, 'request_id', ''),
                "X-Error-Code": error.error_code
            }
        )
    
    async def _get_fallback_streaming_response(
        self, 
        request: Request
    ) -> Optional[Dict[str, Any]]:
        """Get fallback streaming response when ADK is unavailable"""
        
        if not self.fallback_service_url:
            return None
        
        try:
            # This would integrate with existing chat service
            # For now, return a simple fallback message
            from app.models.chat_models import StreamingChunk
            
            fallback_chunk = StreamingChunk(
                content="I'm using a backup system while the main service is unavailable. How can I help you?",
                done=True,
                message_id=getattr(request.state, 'request_id', 'fallback'),
                metadata={
                    "fallback": True,
                    "source": "backup_service"
                }
            )
            
            return fallback_chunk.model_dump()
            
        except Exception as e:
            self.logger.error(f"Fallback service also failed: {e}")
            return None
    
    async def _get_fallback_json_response(
        self, 
        request: Request
    ) -> Optional[Dict[str, Any]]:
        """Get fallback JSON response when ADK is unavailable"""
        
        if not self.fallback_service_url:
            return None
        
        try:
            # This would integrate with existing chat service
            return {
                "message": "Using backup service",
                "source": "fallback",
                "available": True
            }
            
        except Exception as e:
            self.logger.error(f"Fallback service also failed: {e}")
            return None
    
    def _is_streaming_endpoint(self, request: Request) -> bool:
        """Check if the request is for a streaming endpoint"""
        path = request.url.path
        return (
            "stream" in path.lower() or
            request.headers.get("Accept") == "text/event-stream" or
            "sse" in path.lower()
        )
    
    def _is_adk_related_error(self, error: Exception) -> bool:
        """Check if a generic error is likely ADK-related"""
        error_str = str(error).lower()
        adk_indicators = [
            "adk", "connection", "timeout", "unavailable", 
            "agent", "session", "streaming", "sse"
        ]
        return any(indicator in error_str for indicator in adk_indicators)
    
    def _wrap_generic_error(self, error: Exception) -> ADKIntegrationError:
        """Wrap generic error as ADK integration error"""
        error_str = str(error).lower()
        
        if "timeout" in error_str:
            return ADKTimeoutError(
                message="Request timed out",
                original_error=error
            )
        elif "connection" in error_str or "unavailable" in error_str:
            return ADKServerUnavailableError(
                message="Service unavailable",
                original_error=error
            )
        else:
            return ADKIntegrationError(
                message=f"Unexpected error: {str(error)}",
                original_error=error
            )
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID"""
        import uuid
        return str(uuid.uuid4())[:8]


class ADKHealthCheckMiddleware(BaseHTTPMiddleware):
    """
    Middleware to monitor ADK server health and provide circuit breaker functionality.
    """
    
    def __init__(
        self, 
        app: ASGIApp,
        health_check_interval: int = 30,
        failure_threshold: int = 5,
        recovery_timeout: int = 60
    ):
        super().__init__(app)
        self.health_check_interval = health_check_interval
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_health_check = 0
        self.circuit_open = False
        self.logger = logging.getLogger(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Check ADK health and apply circuit breaker logic"""
        
        # Check if we should perform health check
        current_time = time.time()
        if current_time - self.last_health_check > self.health_check_interval:
            await self._perform_health_check()
            self.last_health_check = current_time
        
        # If circuit is open, return error immediately for ADK endpoints
        if self.circuit_open and self._is_adk_endpoint(request):
            error = ADKServerUnavailableError(
                message="ADK service is temporarily unavailable (circuit breaker open)",
                details={"circuit_breaker": True, "failure_count": self.failure_count}
            )
            return await self._create_circuit_breaker_response(request, error)
        
        try:
            response = await call_next(request)
            
            # Reset failure count on successful ADK request
            if self._is_adk_endpoint(request) and response.status_code < 500:
                self.failure_count = 0
                if self.circuit_open:
                    self.circuit_open = False
                    self.logger.info("Circuit breaker closed - ADK service recovered")
            
            return response
            
        except Exception as error:
            # Increment failure count for ADK endpoints
            if self._is_adk_endpoint(request):
                self.failure_count += 1
                
                if self.failure_count >= self.failure_threshold:
                    self.circuit_open = True
                    self.logger.warning(
                        f"Circuit breaker opened - ADK service failed {self.failure_count} times"
                    )
            
            raise error
    
    async def _perform_health_check(self):
        """Perform health check on ADK service"""
        try:
            # This would ping the ADK service health endpoint
            # For now, we'll simulate the check
            pass
        except Exception as e:
            self.logger.warning(f"ADK health check failed: {e}")
    
    def _is_adk_endpoint(self, request: Request) -> bool:
        """Check if request is for an ADK endpoint"""
        path = request.url.path
        adk_paths = ["/api/chat", "/api/agents", "/api/sessions"]
        return any(adk_path in path for adk_path in adk_paths)
    
    async def _create_circuit_breaker_response(
        self, 
        request: Request, 
        error: ADKIntegrationError
    ) -> Response:
        """Create response when circuit breaker is open"""
        
        if self._is_streaming_endpoint(request):
            async def error_stream():
                chunk = error_handler.create_error_stream_chunk(error)
                yield f"data: {chunk.model_dump_json()}\n\n"
            
            return StreamingResponse(
                error_stream(),
                media_type="text/event-stream",
                status_code=503
            )
        else:
            error_response = error_handler.create_error_response(error)
            return Response(
                content=json.dumps(error_response),
                status_code=503,
                media_type="application/json"
            )
    
    def _is_streaming_endpoint(self, request: Request) -> bool:
        """Check if the request is for a streaming endpoint"""
        path = request.url.path
        return (
            "stream" in path.lower() or
            request.headers.get("Accept") == "text/event-stream"
        )


# Middleware factory functions
def create_adk_error_middleware(
    enable_fallback: bool = True,
    fallback_service_url: Optional[str] = None
) -> ADKErrorHandlingMiddleware:
    """Factory function to create ADK error handling middleware"""
    
    def middleware_factory(app: ASGIApp) -> ADKErrorHandlingMiddleware:
        return ADKErrorHandlingMiddleware(
            app=app,
            enable_fallback=enable_fallback,
            fallback_service_url=fallback_service_url
        )
    
    return middleware_factory


def create_adk_health_middleware(
    health_check_interval: int = 30,
    failure_threshold: int = 5,
    recovery_timeout: int = 60
) -> ADKHealthCheckMiddleware:
    """Factory function to create ADK health check middleware"""
    
    def middleware_factory(app: ASGIApp) -> ADKHealthCheckMiddleware:
        return ADKHealthCheckMiddleware(
            app=app,
            health_check_interval=health_check_interval,
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout
        )
    
    return middleware_factory