"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  X,
  Sparkles,
  Youtube,
  Instagram,
  Twitter,
  Calendar,
  Target,
  Wand2
} from "lucide-react";

interface GeneratePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function GeneratePlanModal({ isOpen, onClose }: GeneratePlanModalProps) {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    timeframe: 14,
    platforms: [] as string[],
    goals: [] as string[],
    contentTypes: [] as string[],
    postingFrequency: 'regular'
  });
  const [isGenerating, setIsGenerating] = useState(false);

  if (!isOpen) return null;

  const platforms = [
    { id: 'youtube', name: 'YouTube', icon: Youtube, color: 'text-red-500' },
    { id: 'instagram', name: 'Instagram', icon: Instagram, color: 'text-pink-500' },
    { id: 'twitter', name: 'Twitter', icon: Twitter, color: 'text-blue-500' },
  ];

  const goals = [
    'Increase followers',
    'Boost engagement',
    'Drive website traffic',
    'Build brand awareness',
    'Generate leads',
    'Promote products/services'
  ];

  const contentTypes = [
    'Educational tutorials',
    'Behind-the-scenes',
    'Product showcases',
    'Industry insights',
    'User-generated content',
    'Live sessions',
    'Q&A content',
    'Trending topics'
  ];

  const toggleSelection = (array: string[], item: string) => {
    return array.includes(item) 
      ? array.filter(i => i !== item)
      : [...array, item];
  };

  const handleGenerate = async () => {
    setIsGenerating(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 3000));
    setIsGenerating(false);
    setStep(4); // Show results
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Generate Content Plan</h2>
                <p className="text-sm text-muted-foreground">
                  Create a personalized content strategy with AI
                </p>
              </div>
            </div>
            <Button variant="ghost" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        <div className="p-6">
          {/* Step 1: Timeframe & Platforms */}
          {step === 1 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">Select Timeframe & Platforms</h3>
                
                {/* Timeframe */}
                <div className="mb-6">
                  <label className="text-sm font-medium mb-3 block">Planning Timeframe</label>
                  <div className="grid grid-cols-3 gap-3">
                    {[7, 14, 30].map((days) => (
                      <button
                        key={days}
                        onClick={() => setFormData({...formData, timeframe: days})}
                        className={`p-3 border rounded-lg text-center transition-colors ${
                          formData.timeframe === days 
                            ? 'border-primary bg-primary/5' 
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        <Calendar className="w-5 h-5 mx-auto mb-1" />
                        <p className="font-medium">{days} Days</p>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Platforms */}
                <div>
                  <label className="text-sm font-medium mb-3 block">Select Platforms</label>
                  <div className="grid grid-cols-3 gap-3">
                    {platforms.map((platform) => {
                      const Icon = platform.icon;
                      const isSelected = formData.platforms.includes(platform.id);
                      return (
                        <button
                          key={platform.id}
                          onClick={() => setFormData({
                            ...formData, 
                            platforms: toggleSelection(formData.platforms, platform.id)
                          })}
                          className={`p-3 border rounded-lg text-center transition-colors ${
                            isSelected 
                              ? 'border-primary bg-primary/5' 
                              : 'border-border hover:border-primary/50'
                          }`}
                        >
                          <Icon className={`w-5 h-5 mx-auto mb-1 ${platform.color}`} />
                          <p className="font-medium text-sm">{platform.name}</p>
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={onClose}>Cancel</Button>
                <Button 
                  onClick={() => setStep(2)}
                  disabled={formData.platforms.length === 0}
                >
                  Next: Goals
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: Goals */}
          {step === 2 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">What are your goals?</h3>
                <div className="grid grid-cols-2 gap-3">
                  {goals.map((goal) => {
                    const isSelected = formData.goals.includes(goal);
                    return (
                      <button
                        key={goal}
                        onClick={() => setFormData({
                          ...formData, 
                          goals: toggleSelection(formData.goals, goal)
                        })}
                        className={`p-3 border rounded-lg text-left transition-colors ${
                          isSelected 
                            ? 'border-primary bg-primary/5' 
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        <Target className="w-4 h-4 mb-1" />
                        <p className="text-sm">{goal}</p>
                      </button>
                    );
                  })}
                </div>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>Back</Button>
                <Button 
                  onClick={() => setStep(3)}
                  disabled={formData.goals.length === 0}
                >
                  Next: Content Types
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: Content Types */}
          {step === 3 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">Content Preferences</h3>
                <div className="grid grid-cols-2 gap-3">
                  {contentTypes.map((type) => {
                    const isSelected = formData.contentTypes.includes(type);
                    return (
                      <button
                        key={type}
                        onClick={() => setFormData({
                          ...formData, 
                          contentTypes: toggleSelection(formData.contentTypes, type)
                        })}
                        className={`p-3 border rounded-lg text-left transition-colors ${
                          isSelected 
                            ? 'border-primary bg-primary/5' 
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        <p className="text-sm">{type}</p>
                      </button>
                    );
                  })}
                </div>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(2)}>Back</Button>
                <Button onClick={handleGenerate} disabled={isGenerating}>
                  {isGenerating ? (
                    <>
                      <Wand2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate Plan
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {/* Step 4: Results */}
          {step === 4 && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-lg font-medium mb-2">Content Plan Generated!</h3>
                <p className="text-muted-foreground mb-4">
                  Your {formData.timeframe}-day content plan is ready with {formData.platforms.length * Math.ceil(formData.timeframe / 7)} posts
                </p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Plan Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Duration</span>
                    <span className="font-medium">{formData.timeframe} days</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Platforms</span>
                    <div className="flex gap-1">
                      {formData.platforms.map(platform => (
                        <Badge key={platform} variant="secondary" className="text-xs">
                          {platform}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Goals</span>
                    <span className="font-medium">{formData.goals.length} selected</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Posts</span>
                    <span className="font-medium">{formData.platforms.length * Math.ceil(formData.timeframe / 7)}</span>
                  </div>
                </CardContent>
              </Card>

              <div className="flex gap-2">
                <Button variant="outline" className="flex-1" onClick={() => setStep(1)}>
                  Generate Another
                </Button>
                <Button className="flex-1" onClick={onClose}>
                  Add to Calendar
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}