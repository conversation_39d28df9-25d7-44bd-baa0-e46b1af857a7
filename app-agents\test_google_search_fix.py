#!/usr/bin/env python3
"""
Test Google Search Fix - Verify Correct Architecture
Tests that the Google Search agent is properly configured as a root agent
and can be used via AgentTool without the "Tool use with function calling is unsupported" error.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add agents directory to path
agents_dir = os.path.join(os.path.dirname(__file__), 'agents')
sys.path.insert(0, agents_dir)

async def test_google_search_root_agent():
    """Test the Google Search Root Agent (correct architecture)."""
    print("🔍 Testing Google Search Root Agent...")
    
    try:
        # Import the Google Search ROOT agent
        from google_search_root_agent.agent import root_agent as search_root_agent
        
        print(f"✅ Google Search Root Agent loaded: {search_root_agent.name}")
        print(f"   Model: {search_root_agent.model}")
        print(f"   Tools: {len(search_root_agent.tools)}")
        
        # Verify it has google_search tool
        has_google_search = any(
            str(tool).lower().find('google_search') != -1 or 
            getattr(tool, '__name__', '').lower().find('google_search') != -1
            for tool in search_root_agent.tools
        )
        
        if has_google_search:
            print("   ✅ google_search tool found")
        else:
            print("   ⚠️ google_search tool not detected")
        
        # Verify it's configured as root agent (no parent)
        if not hasattr(search_root_agent, 'parent_agent') or search_root_agent.parent_agent is None:
            print("   ✅ Configured as root agent (no parent)")
        else:
            print("   ⚠️ Has parent agent - should be root agent")
        
        # Verify it has only one tool (built-in tool limitation)
        if len(search_root_agent.tools) == 1:
            print("   ✅ Has exactly one tool (ADK built-in tool requirement)")
        else:
            print(f"   ⚠️ Has {len(search_root_agent.tools)} tools - built-in tools should be alone")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Google Search Root Agent: {e}")
        return False

async def test_news_content_agent_fix():
    """Test the News Content Agent with fixed architecture."""
    print("\n📰 Testing News Content Agent (Fixed Architecture)...")
    
    try:
        # Import the News Content agent
        from news_content_agent.agent import root_agent as news_agent
        
        print(f"✅ News Content Agent loaded: {news_agent.name}")
        print(f"   Model: {news_agent.model}")
        print(f"   Sub-agents: {len(news_agent.sub_agents) if news_agent.sub_agents else 0}")
        print(f"   Tools: {len(news_agent.tools)}")
        
        # Verify no built-in tools in sub-agents
        if news_agent.sub_agents:
            for sub_agent in news_agent.sub_agents:
                print(f"     • Sub-agent: {sub_agent.name}")
                # Check if sub-agent has google_search (should not)
                if hasattr(sub_agent, 'tools') and sub_agent.tools:
                    has_google_search = any(
                        str(tool).lower().find('google_search') != -1
                        for tool in sub_agent.tools
                    )
                    if has_google_search:
                        print(f"       ❌ Sub-agent {sub_agent.name} has google_search (not allowed)")
                        return False
                    else:
                        print(f"       ✅ Sub-agent {sub_agent.name} has no built-in tools")
        
        # Verify AgentTools are used for Google Search
        if news_agent.tools:
            agent_tools_count = sum(1 for tool in news_agent.tools if 'AgentTool' in str(type(tool)))
            print(f"   ✅ Has {agent_tools_count} AgentTools (correct pattern for built-in tools)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing News Content Agent: {e}")
        return False

async def test_main_coordinator_fix():
    """Test the main coordinator with fixed architecture."""
    print("\n🎯 Testing Main Coordinator (Fixed Architecture)...")
    
    try:
        # Import the main coordinator
        from agent import root_agent as coordinator
        
        print(f"✅ Main Coordinator loaded: {coordinator.name}")
        print(f"   Model: {coordinator.model}")
        print(f"   Sub-agents: {len(coordinator.sub_agents) if coordinator.sub_agents else 0}")
        print(f"   Tools: {len(coordinator.tools)}")
        
        # Verify no built-in tools in sub-agents
        if coordinator.sub_agents:
            print("   Sub-agents:")
            for sub_agent in coordinator.sub_agents:
                print(f"     • {sub_agent.name}")
                # Check if sub-agent has google_search (should not)
                if hasattr(sub_agent, 'tools') and sub_agent.tools:
                    has_google_search = any(
                        str(tool).lower().find('google_search') != -1
                        for tool in sub_agent.tools
                    )
                    if has_google_search:
                        print(f"       ❌ Sub-agent {sub_agent.name} has google_search (not allowed)")
                        return False
                    else:
                        print(f"       ✅ No built-in tools in {sub_agent.name}")
        
        # Verify AgentTools for Google Search
        if coordinator.tools:
            agent_tools = [tool for tool in coordinator.tools if 'AgentTool' in str(type(tool))]
            print(f"   ✅ Has {len(agent_tools)} AgentTools for delegation")
            
            # Check if Google Search Root Agent is available as AgentTool
            google_search_tool_found = False
            for tool in agent_tools:
                if hasattr(tool, 'agent') and hasattr(tool.agent, 'name'):
                    if 'google_search' in tool.agent.name.lower():
                        google_search_tool_found = True
                        print(f"     • Google Search available via AgentTool: {tool.agent.name}")
            
            if google_search_tool_found:
                print("   ✅ Google Search Root Agent available as AgentTool")
            else:
                print("   ⚠️ Google Search Root Agent not found as AgentTool")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Main Coordinator: {e}")
        return False

def check_architecture_compliance():
    """Check if the architecture follows ADK built-in tool rules."""
    print("\n📋 Checking ADK Built-in Tool Compliance...")
    
    compliance_rules = [
        "✅ Built-in tools (google_search) only in root agents",
        "✅ No built-in tools in sub-agents", 
        "✅ Only one built-in tool per agent",
        "✅ No mixing built-in tools with custom tools",
        "✅ Use AgentTool pattern for built-in tools in complex systems"
    ]
    
    for rule in compliance_rules:
        print(f"   {rule}")
    
    print("\n   📖 ADK Documentation Reference:")
    print("   https://google.github.io/adk-docs/tools/built-in-tools/#limitations")
    
    return True

async def main():
    """Main test function."""
    print("🔧 Google Search Fix Verification Test")
    print("=" * 50)
    
    # Check individual components
    search_ok = await test_google_search_root_agent()
    news_ok = await test_news_content_agent_fix()
    coordinator_ok = await test_main_coordinator_fix()
    compliance_ok = check_architecture_compliance()
    
    print("\n" + "=" * 50)
    print("📊 Fix Verification Results:")
    print(f"   Google Search Root Agent: {'✅ PASS' if search_ok else '❌ FAIL'}")
    print(f"   News Content Agent: {'✅ PASS' if news_ok else '❌ FAIL'}")
    print(f"   Main Coordinator: {'✅ PASS' if coordinator_ok else '❌ FAIL'}")
    print(f"   Architecture Compliance: {'✅ PASS' if compliance_ok else '❌ FAIL'}")
    
    if all([search_ok, news_ok, coordinator_ok, compliance_ok]):
        print("\n🎉 Fix Verification PASSED!")
        print("\n✅ The 'Tool use with function calling is unsupported' error should be resolved.")
        print("\n🚀 Your agents are now correctly configured for Google Search:")
        print("   • Google Search available via Google Search Root Agent (AgentTool)")
        print("   • No built-in tools in sub-agents (ADK compliance)")
        print("   • Proper delegation pattern implemented")
        print("   • Ready for real-time news research and content creation")
        
        print("\n💡 Test your agents with:")
        print("   • 'Give me the latest news on AI'")
        print("   • 'Research trending topics in social media marketing'")
        print("   • 'Latest developments in [your industry] and create content'")
        
    else:
        print("\n⚠️ Some verification checks failed.")
        print("   Please review the errors above and ensure:")
        print("   • Google Search Root Agent is properly configured")
        print("   • No built-in tools in sub-agents")
        print("   • AgentTool pattern is used correctly")

if __name__ == "__main__":
    asyncio.run(main())