"""
ADK Session Management Integration

This module provides the SessionManager class that uses ADK session endpoints
for creating, retrieving, and managing ADK sessions with caching and lifecycle management.

Requirements covered: 3.1, 3.2, 3.3
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import asyncio
from contextlib import asynccontextmanager

from app.services.adk_service import ADKService, ADKConnectionError, ADKAgentNotFoundError
from app.models.adk_models import (
    ADKSessionInfo,
    ADKSessionCreateResponse,
    ADKRequestValidator
)


logger = logging.getLogger(__name__)


class SessionManagerError(Exception):
    """Base exception for session manager errors"""
    pass


class SessionNotFoundError(SessionManagerError):
    """Raised when a session is not found"""
    pass


class SessionCreationError(SessionManagerError):
    """Raised when session creation fails"""
    pass


class SessionManager:
    """
    SessionManager class that uses ADK session endpoints for session lifecycle management.
    
    Provides high-level interface for:
    - Session creation via POST /apps/{app_name}/users/{user_id}/sessions
    - Session retrieval using GET /apps/{app_name}/users/{user_id}/sessions/{session_id}
    - Session caching and lifecycle management
    - Session cleanup and expiration handling
    """
    
    def __init__(
        self,
        adk_service: ADKService,
        cache_ttl: timedelta = timedelta(hours=1),
        max_cache_size: int = 1000,
        session_timeout: timedelta = timedelta(hours=24)
    ):
        """
        Initialize SessionManager.
        
        Args:
            adk_service: ADK service instance for API communication
            cache_ttl: Time-to-live for cached sessions
            max_cache_size: Maximum number of sessions to cache
            session_timeout: Session timeout duration
        """
        self.adk_service = adk_service
        self.cache_ttl = cache_ttl
        self.max_cache_size = max_cache_size
        self.session_timeout = session_timeout
        
        # Session cache: {session_key: (session_info, cache_time)}
        self._session_cache: Dict[str, tuple[ADKSessionInfo, datetime]] = {}
        
        # User session mapping: {user_id: {agent_name: session_id}}
        self._user_sessions: Dict[str, Dict[str, str]] = {}
        
        # Session creation locks to prevent duplicate sessions
        self._creation_locks: Dict[str, asyncio.Lock] = {}
    
    def _get_session_key(self, app_name: str, user_id: str, session_id: str) -> str:
        """Generate cache key for session"""
        return f"{app_name}:{user_id}:{session_id}"
    
    def _get_user_key(self, user_id: str, app_name: str) -> str:
        """Generate key for user session mapping"""
        return f"{user_id}:{app_name}"
    
    def _is_cache_valid(self, cache_time: datetime) -> bool:
        """Check if cached session is still valid"""
        return datetime.now() - cache_time < self.cache_ttl
    
    def _cleanup_cache(self) -> None:
        """Remove expired entries from cache"""
        now = datetime.now()
        expired_keys = []
        
        for key, (session_info, cache_time) in self._session_cache.items():
            if not self._is_cache_valid(cache_time):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._session_cache[key]
            logger.debug(f"Removed expired session from cache: {key}")
        
        # Enforce max cache size
        if len(self._session_cache) > self.max_cache_size:
            # Remove oldest entries
            sorted_items = sorted(
                self._session_cache.items(),
                key=lambda x: x[1][1]  # Sort by cache_time
            )
            
            excess_count = len(self._session_cache) - self.max_cache_size
            for i in range(excess_count):
                key = sorted_items[i][0]
                del self._session_cache[key]
                logger.debug(f"Removed old session from cache due to size limit: {key}")
    
    async def create_session(
        self,
        app_name: str,
        user_id: str,
        initial_state: Optional[Dict[str, Any]] = None,
        force_new: bool = False
    ) -> ADKSessionCreateResponse:
        """
        Create a new ADK session via POST /apps/{app_name}/users/{user_id}/sessions.
        
        Args:
            app_name: Name of the ADK agent
            user_id: User identifier
            initial_state: Optional initial session state
            force_new: Force creation of new session even if one exists
            
        Returns:
            Session creation response
            
        Raises:
            SessionCreationError: If session creation fails
            ADKAgentNotFoundError: If agent is not found
            ValueError: If input validation fails
        """
        # Validate inputs
        if not ADKRequestValidator.validate_app_name(app_name):
            raise ValueError(f"Invalid app name: {app_name}")
        if not ADKRequestValidator.validate_user_id(user_id):
            raise ValueError(f"Invalid user ID: {user_id}")
        
        user_key = self._get_user_key(user_id, app_name)
        
        # Use lock to prevent concurrent session creation for same user/agent
        if user_key not in self._creation_locks:
            self._creation_locks[user_key] = asyncio.Lock()
        
        async with self._creation_locks[user_key]:
            try:
                # Check if user already has an active session for this agent
                if not force_new and user_key in self._user_sessions:
                    existing_session_id = self._user_sessions[user_key]
                    existing_session = await self.get_session(app_name, user_id, existing_session_id)
                    
                    if existing_session:
                        logger.info(f"Returning existing session {existing_session_id} for user {user_id}")
                        return ADKSessionCreateResponse(
                            id=existing_session.id,
                            app_name=existing_session.app_name,
                            user_id=existing_session.user_id,
                            created_at=existing_session.created_at or datetime.now()
                        )
                
                logger.info(f"Creating new ADK session for user {user_id} with agent {app_name}")
                
                # Create session via ADK service
                session_response = await self.adk_service.create_session(
                    app_name=app_name,
                    user_id=user_id,
                    initial_state=initial_state
                )
                
                # Cache the session info
                session_info = ADKSessionInfo(
                    id=session_response.id,
                    app_name=session_response.app_name,
                    user_id=session_response.user_id,
                    created_at=session_response.created_at,
                    last_activity=datetime.now(),
                    event_count=0,
                    metadata={"initial_state": initial_state} if initial_state else {}
                )
                
                session_key = self._get_session_key(app_name, user_id, session_response.id)
                self._session_cache[session_key] = (session_info, datetime.now())
                
                # Update user session mapping
                self._user_sessions[user_key] = session_response.id
                
                # Cleanup cache
                self._cleanup_cache()
                
                logger.info(f"Successfully created ADK session: {session_response.id}")
                return session_response
                
            except ADKAgentNotFoundError:
                logger.error(f"Agent not found: {app_name}")
                raise
            except ADKConnectionError as e:
                logger.error(f"ADK connection error during session creation: {e}")
                raise SessionCreationError(f"Failed to create session: {e}")
            except Exception as e:
                logger.error(f"Unexpected error creating session: {e}")
                raise SessionCreationError(f"Session creation failed: {e}")
    
    async def get_session(
        self,
        app_name: str,
        user_id: str,
        session_id: str,
        use_cache: bool = True
    ) -> Optional[ADKSessionInfo]:
        """
        Retrieve ADK session using GET /apps/{app_name}/users/{user_id}/sessions/{session_id}.
        
        Args:
            app_name: Name of the ADK agent
            user_id: User identifier
            session_id: Session identifier
            use_cache: Whether to use cached session data
            
        Returns:
            Session information if found, None otherwise
            
        Raises:
            ValueError: If input validation fails
        """
        # Validate inputs
        if not ADKRequestValidator.validate_app_name(app_name):
            raise ValueError(f"Invalid app name: {app_name}")
        if not ADKRequestValidator.validate_user_id(user_id):
            raise ValueError(f"Invalid user ID: {user_id}")
        if not ADKRequestValidator.validate_session_id(session_id):
            raise ValueError(f"Invalid session ID: {session_id}")
        
        session_key = self._get_session_key(app_name, user_id, session_id)
        
        # Check cache first
        if use_cache and session_key in self._session_cache:
            session_info, cache_time = self._session_cache[session_key]
            if self._is_cache_valid(cache_time):
                logger.debug(f"Returning cached session: {session_id}")
                return session_info
            else:
                # Remove expired cache entry
                del self._session_cache[session_key]
        
        try:
            logger.debug(f"Retrieving session {session_id} from ADK server")
            
            # Retrieve from ADK service
            session_info = await self.adk_service.get_session(app_name, user_id, session_id)
            
            if session_info:
                # Update cache
                self._session_cache[session_key] = (session_info, datetime.now())
                
                # Update user session mapping
                user_key = self._get_user_key(user_id, app_name)
                self._user_sessions[user_key] = session_id
                
                logger.debug(f"Successfully retrieved session: {session_id}")
            else:
                logger.debug(f"Session not found: {session_id}")
            
            return session_info
            
        except Exception as e:
            logger.error(f"Error retrieving session {session_id}: {e}")
            return None
    
    async def get_or_create_session(
        self,
        app_name: str,
        user_id: str,
        session_id: Optional[str] = None,
        initial_state: Optional[Dict[str, Any]] = None
    ) -> ADKSessionCreateResponse:
        """
        Get existing session or create new one if not found.
        
        Args:
            app_name: Name of the ADK agent
            user_id: User identifier
            session_id: Optional existing session ID
            initial_state: Optional initial session state for new sessions
            
        Returns:
            Session creation response (existing or new)
            
        Raises:
            SessionCreationError: If session creation fails
            ValueError: If input validation fails
        """
        # If session_id provided, try to retrieve it first
        if session_id:
            existing_session = await self.get_session(app_name, user_id, session_id)
            if existing_session:
                logger.info(f"Found existing session: {session_id}")
                return ADKSessionCreateResponse(
                    id=existing_session.id,
                    app_name=existing_session.app_name,
                    user_id=existing_session.user_id,
                    created_at=existing_session.created_at or datetime.now()
                )
        
        # Create new session
        logger.info(f"Creating new session for user {user_id} with agent {app_name}")
        return await self.create_session(
            app_name=app_name,
            user_id=user_id,
            initial_state=initial_state
        )
    
    async def get_user_sessions(
        self,
        user_id: str,
        app_name: Optional[str] = None
    ) -> List[ADKSessionInfo]:
        """
        Get all sessions for a user, optionally filtered by agent.
        
        Args:
            user_id: User identifier
            app_name: Optional agent name filter
            
        Returns:
            List of user sessions
        """
        if not ADKRequestValidator.validate_user_id(user_id):
            raise ValueError(f"Invalid user ID: {user_id}")
        
        sessions = []
        
        # Search through cached sessions
        for session_key, (session_info, cache_time) in self._session_cache.items():
            if (session_info.user_id == user_id and 
                (app_name is None or session_info.app_name == app_name) and
                self._is_cache_valid(cache_time)):
                sessions.append(session_info)
        
        return sessions
    
    async def invalidate_session(
        self,
        app_name: str,
        user_id: str,
        session_id: str
    ) -> None:
        """
        Invalidate a session from cache and user mapping.
        
        Args:
            app_name: Name of the ADK agent
            user_id: User identifier
            session_id: Session identifier
        """
        session_key = self._get_session_key(app_name, user_id, session_id)
        user_key = self._get_user_key(user_id, app_name)
        
        # Remove from cache
        if session_key in self._session_cache:
            del self._session_cache[session_key]
            logger.debug(f"Invalidated session cache: {session_id}")
        
        # Remove from user mapping if it matches
        if (user_key in self._user_sessions and 
            self._user_sessions[user_key] == session_id):
            del self._user_sessions[user_key]
            logger.debug(f"Removed session from user mapping: {session_id}")
    
    async def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions from cache.
        
        Returns:
            Number of sessions cleaned up
        """
        initial_count = len(self._session_cache)
        self._cleanup_cache()
        cleaned_count = initial_count - len(self._session_cache)
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} expired sessions")
        
        return cleaned_count
    
    async def get_session_info(
        self,
        session_id: str,
        user_id: str
    ) -> Optional[ADKSessionInfo]:
        """
        Get session info by session ID and user ID.
        
        Args:
            session_id: Session identifier
            user_id: User identifier
            
        Returns:
            Session information if found, None otherwise
        """
        # Search through cached sessions first
        for session_key, (session_info, cache_time) in self._session_cache.items():
            if (session_info.id == session_id and 
                session_info.user_id == user_id and
                self._is_cache_valid(cache_time)):
                return session_info
        
        # Try to find session by checking all possible app names
        # This is a fallback when we don't know the app name
        try:
            available_agents = await self.adk_service.list_agents()
            for agent in available_agents:
                session_info = await self.get_session(agent.name, user_id, session_id)
                if session_info:
                    return session_info
        except Exception as e:
            logger.error(f"Error searching for session {session_id}: {e}")
        
        return None
    
    async def get_chat_history(
        self,
        user_id: str,
        session_id: str
    ) -> List[Any]:
        """
        Get chat history events for a session.
        
        Args:
            user_id: User identifier
            session_id: Session identifier
            
        Returns:
            List of ADK events for the session
        """
        # First get session info to determine app name
        session_info = await self.get_session_info(session_id, user_id)
        if not session_info:
            return []
        
        try:
            # Get events from ADK service
            events = await self.adk_service.get_session_events(
                app_name=session_info.app_name,
                user_id=user_id,
                session_id=session_id
            )
            return events
        except Exception as e:
            logger.error(f"Error getting chat history for session {session_id}: {e}")
            return []

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get session cache statistics.
        
        Returns:
            Cache statistics dictionary
        """
        now = datetime.now()
        valid_sessions = 0
        expired_sessions = 0
        
        for _, (_, cache_time) in self._session_cache.items():
            if self._is_cache_valid(cache_time):
                valid_sessions += 1
            else:
                expired_sessions += 1
        
        return {
            "total_cached_sessions": len(self._session_cache),
            "valid_sessions": valid_sessions,
            "expired_sessions": expired_sessions,
            "user_mappings": len(self._user_sessions),
            "cache_ttl_seconds": self.cache_ttl.total_seconds(),
            "max_cache_size": self.max_cache_size
        }


# Context manager for session lifecycle
@asynccontextmanager
async def session_manager_context(adk_service: Optional[ADKService] = None):
    """
    Context manager for SessionManager lifecycle.
    
    Args:
        adk_service: Optional ADK service instance
        
    Usage:
        async with session_manager_context() as session_mgr:
            session = await session_mgr.create_session("agent", "user123")
    """
    if adk_service is None:
        from app.services.adk_service import get_adk_service
        adk_service = await get_adk_service()
    
    session_manager = SessionManager(adk_service)
    try:
        yield session_manager
    finally:
        # Cleanup if needed
        await session_manager.cleanup_expired_sessions()


# Global session manager instance
_session_manager: Optional[SessionManager] = None


async def get_session_manager(adk_service: Optional[ADKService] = None) -> SessionManager:
    """
    Get or create global SessionManager instance.
    
    Args:
        adk_service: Optional ADK service instance
        
    Returns:
        SessionManager instance
    """
    global _session_manager
    
    if _session_manager is None:
        if adk_service is None:
            from app.services.adk_service import get_adk_service
            adk_service = await get_adk_service()
        
        _session_manager = SessionManager(adk_service)
    
    return _session_manager