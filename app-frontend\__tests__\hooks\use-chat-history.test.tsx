import React, { ReactNode } from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useChatHistory } from '@/hooks/use-chat-history';

// Mock fetch
global.fetch = jest.fn();

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        retryDelay: 0,
        staleTime: 0,
        cacheTime: 0,
      },
    },
    logger: {
      log: () => {},
      warn: () => {},
      error: () => {},
    },
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useChatHistory', () => {
  const mockOptions = {
    sessionId: 'test-session',
    userId: 'test-user',
    agentName: 'test-agent',
  };

  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should initialize with empty state', () => {
    const { result } = renderHook(() => useChatHistory(mockOptions), {
      wrapper: createWrapper(),
    });

    expect(result.current.messages).toEqual([]);
    expect(result.current.isLoading).toBe(true);
    expect(result.current.totalCount).toBe(0);
    expect(result.current.hasMore).toBe(false);
  });

  it('should fetch chat history successfully', async () => {
    const mockMessages = [
      {
        id: 'msg-1',
        session_id: 'test-session',
        role: 'user',
        content: 'Hello',
        user_id: 'test-user',
        timestamp: new Date().toISOString(),
        agent_name: 'test-agent',
      },
      {
        id: 'msg-2',
        session_id: 'test-session',
        role: 'model',
        content: 'Hi there!',
        user_id: 'test-user',
        timestamp: new Date().toISOString(),
        agent_name: 'test-agent',
      },
    ];

    const mockResponse = {
      messages: mockMessages,
      total_count: 2,
      has_more: false,
      session_info: {
        id: 'test-session',
        user_id: 'test-user',
        app_name: 'test-agent',
      },
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const { result } = renderHook(() => useChatHistory(mockOptions), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.messages).toEqual(mockMessages);
    expect(result.current.totalCount).toBe(2);
    expect(result.current.hasMore).toBe(false);
  });

  it('should handle ADK events transformation', async () => {
    const mockADKEvents = [
      {
        invocation_id: 'inv-1',
        content: {
          role: 'user',
          parts: [{ text: 'Hello from ADK' }],
        },
        interrupted: false,
        turn_complete: true,
      },
      {
        invocation_id: 'inv-2',
        content: {
          role: 'model',
          parts: [
            { text: 'Response from ADK' },
            { function_call: { name: 'search', arguments: { query: 'test' } } },
          ],
        },
        interrupted: false,
        turn_complete: true,
        author: 'test-agent',
      },
    ];

    const mockResponse = {
      adk_events: mockADKEvents,
      total_count: 2,
      has_more: false,
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const { result } = renderHook(() => useChatHistory(mockOptions), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.messages).toHaveLength(2);
    expect(result.current.messages[0].content).toBe('Hello from ADK');
    expect(result.current.messages[1].content).toBe('Response from ADK');
    expect(result.current.messages[1].function_calls).toEqual([
      { name: 'search', arguments: { query: 'test' } },
    ]);
  });

  it('should handle fetch errors', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useChatHistory(mockOptions), {
      wrapper: createWrapper(),
    });

    // Wait for the query to complete and enter error state
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    }, { timeout: 5000 });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    }, { timeout: 5000 });

    expect(result.current.error).toEqual(
      expect.objectContaining({
        error: expect.stringContaining('Network error'),
        error_code: 'HISTORY_FETCH_ERROR',
        session_id: 'test-session',
      })
    );
  });

  it('should handle empty session ID', () => {
    const optionsWithoutSession = {
      ...mockOptions,
      sessionId: undefined,
    };

    const { result } = renderHook(() => useChatHistory(optionsWithoutSession), {
      wrapper: createWrapper(),
    });

    expect(result.current.messages).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.totalCount).toBe(0);
  });

  it('should refresh history', async () => {
    const mockResponse = {
      messages: [],
      total_count: 0,
      has_more: false,
    };

    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockResponse,
    });

    const { result } = renderHook(() => useChatHistory(mockOptions), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Refresh should trigger another fetch
    await waitFor(async () => {
      await result.current.refreshHistory();
    });

    expect(fetch).toHaveBeenCalledTimes(2);
  });

  it('should clear history', () => {
    const { result } = renderHook(() => useChatHistory(mockOptions), {
      wrapper: createWrapper(),
    });

    result.current.clearHistory();

    // Should reset to initial state
    expect(result.current.messages).toEqual([]);
    expect(result.current.totalCount).toBe(0);
  });

  it('should construct correct API URL with parameters', async () => {
    const mockResponse = {
      messages: [],
      total_count: 0,
      has_more: false,
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const optionsWithParams = {
      ...mockOptions,
      limit: 25,
      offset: 10,
      includeMetadata: true,
    };

    renderHook(() => useChatHistory(optionsWithParams), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(fetch).toHaveBeenCalled();
    });

    const fetchCall = (fetch as jest.Mock).mock.calls[0];
    const url = fetchCall[0];

    expect(url).toContain('session_id=test-session');
    expect(url).toContain('user_id=test-user');
    expect(url).toContain('limit=25');
    expect(url).toContain('offset=10');
    expect(url).toContain('include_metadata=true');
  });
});