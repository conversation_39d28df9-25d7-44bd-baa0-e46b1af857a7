"""
Unit tests for ADK Service Foundation

Tests for ADK service initialization, agent discovery, session management,
error handling, and connection management.

Requirements covered: 1.1, 1.5, 4.1
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import httpx
import json

from app.services.adk_service import (
    ADKService,
    ADKConnectionError,
    ADKServerUnavailableError,
    ADKAgentNotFoundError,
    get_adk_service,
    adk_service_context
)
from app.models.adk_models import (
    ADKAgentInfo,
    ADKSessionInfo,
    ADKSessionCreateResponse,
    ADKHealthCheck
)


class TestADKService:
    """Test cases for ADK service functionality"""
    
    @pytest.fixture
    def adk_service(self):
        """Create ADK service instance for testing"""
        return ADKService(
            base_url="http://localhost:8001",
            agents_dir="test_agents",
            timeout=5.0,
            max_retries=2,
            retry_delay=0.1
        )
    
    @pytest.fixture
    def mock_httpx_client(self):
        """Mock httpx client for testing"""
        client = AsyncMock(spec=httpx.AsyncClient)
        return client
    
    @pytest.fixture
    def sample_agents_response(self):
        """Sample agents list response"""
        return [
            "content_planner",
            "research_agent",
            "social_media_agent"
        ]
    
    @pytest.fixture
    def sample_detailed_agents_response(self):
        """Sample detailed agents response"""
        return {
            "content_planner": {
                "description": "AI agent for content planning and strategy",
                "available": True,
                "capabilities": ["content_planning", "strategy"],
                "tools": ["google_search", "calendar"]
            },
            "research_agent": {
                "description": "AI agent for research and analysis",
                "available": True,
                "capabilities": ["research", "analysis"],
                "tools": ["google_search", "web_scraper"]
            }
        }
    
    @pytest.fixture
    def sample_session_response(self):
        """Sample session creation response"""
        return {
            "id": "session_123",
            "created_at": "2024-01-01T12:00:00Z",
            "app_name": "content_planner",
            "user_id": "user_456"
        }
    
    @pytest.fixture
    def sample_health_response(self):
        """Sample health check response"""
        return {
            "status": "healthy",
            "agents_loaded": 3,
            "available_agents": ["content_planner", "research_agent", "social_media_agent"],
            "server_version": "1.0.0",
            "uptime_seconds": 3600.0
        }
    
    def test_adk_service_initialization(self, adk_service):
        """Test ADK service initialization parameters"""
        assert adk_service.base_url == "http://localhost:8001"
        assert adk_service.agents_dir == "test_agents"
        assert adk_service.timeout == 5.0
        assert adk_service.max_retries == 2
        assert adk_service.retry_delay == 0.1
        assert not adk_service._is_connected
        assert adk_service._agents_cache is None
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, adk_service, mock_httpx_client, sample_health_response):
        """Test successful ADK service initialization"""
        # Mock successful health check
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_health_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            await adk_service.initialize()
            
            assert adk_service._is_connected
            mock_httpx_client.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_failure(self, adk_service, mock_httpx_client):
        """Test ADK service initialization failure"""
        # Mock connection failure
        mock_httpx_client.request.side_effect = httpx.ConnectError("Connection failed")
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(ADKConnectionError, match="Failed to connect to ADK server"):
                await adk_service.initialize()
            
            assert not adk_service._is_connected
    
    @pytest.mark.asyncio
    async def test_close(self, adk_service, mock_httpx_client):
        """Test ADK service cleanup"""
        adk_service._client = mock_httpx_client
        adk_service._is_connected = True
        adk_service._agents_cache = [ADKAgentInfo(name="test")]
        
        await adk_service.close()
        
        mock_httpx_client.aclose.assert_called_once()
        assert adk_service._client is None
        assert not adk_service._is_connected
        assert adk_service._agents_cache is None
    
    @pytest.mark.asyncio
    async def test_is_healthy_success(self, adk_service, mock_httpx_client, sample_health_response):
        """Test health check success"""
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_health_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            is_healthy = await adk_service.is_healthy()
            
            assert is_healthy
            assert adk_service._is_connected
    
    @pytest.mark.asyncio
    async def test_is_healthy_failure(self, adk_service, mock_httpx_client):
        """Test health check failure"""
        mock_httpx_client.request.side_effect = httpx.ConnectError("Connection failed")
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            is_healthy = await adk_service.is_healthy()
            
            assert not is_healthy
            assert not adk_service._is_connected
    
    @pytest.mark.asyncio
    async def test_list_agents_simple_format(self, adk_service, mock_httpx_client, sample_agents_response):
        """Test agent discovery with simple list format"""
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_agents_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            agents = await adk_service.list_agents()
            
            assert len(agents) == 3
            assert agents[0].name == "content_planner"
            assert agents[0].available
            assert agents[1].name == "research_agent"
            assert agents[2].name == "social_media_agent"
            
            # Verify caching
            assert adk_service._agents_cache == agents
            assert adk_service._agents_cache_expiry is not None
    
    @pytest.mark.asyncio
    async def test_list_agents_detailed_format(self, adk_service, mock_httpx_client, sample_detailed_agents_response):
        """Test agent discovery with detailed format"""
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_detailed_agents_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            agents = await adk_service.list_agents()
            
            assert len(agents) == 2
            
            content_planner = agents[0]
            assert content_planner.name == "content_planner"
            assert content_planner.description == "AI agent for content planning and strategy"
            assert content_planner.available
            assert "content_planning" in content_planner.capabilities
            assert "google_search" in content_planner.tools
            
            research_agent = agents[1]
            assert research_agent.name == "research_agent"
            assert research_agent.description == "AI agent for research and analysis"
    
    @pytest.mark.asyncio
    async def test_list_agents_cache_hit(self, adk_service, mock_httpx_client):
        """Test agent list cache functionality"""
        # Set up cache
        cached_agents = [ADKAgentInfo(name="cached_agent")]
        adk_service._agents_cache = cached_agents
        adk_service._agents_cache_expiry = datetime.now() + timedelta(minutes=5)
        
        agents = await adk_service.list_agents()
        
        # Should return cached results without making HTTP request
        assert agents == cached_agents
        mock_httpx_client.request.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_list_agents_force_refresh(self, adk_service, mock_httpx_client, sample_agents_response):
        """Test forced refresh of agent cache"""
        # Set up cache
        adk_service._agents_cache = [ADKAgentInfo(name="old_agent")]
        adk_service._agents_cache_expiry = datetime.now() + timedelta(minutes=5)
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_agents_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            agents = await adk_service.list_agents(force_refresh=True)
            
            # Should make HTTP request despite cache
            mock_httpx_client.request.assert_called_once()
            assert len(agents) == 3
            assert agents[0].name == "content_planner"
    
    @pytest.mark.asyncio
    async def test_list_agents_server_error(self, adk_service, mock_httpx_client):
        """Test agent discovery with server error"""
        mock_response = AsyncMock()
        mock_response.status_code = 500
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(ADKServerUnavailableError, match="Failed to list agents"):
                await adk_service.list_agents()
    
    @pytest.mark.asyncio
    async def test_list_agents_connection_error(self, adk_service, mock_httpx_client):
        """Test agent discovery with connection error"""
        mock_httpx_client.request.side_effect = httpx.ConnectError("Connection failed")
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(ADKConnectionError, match="Failed to connect to ADK server"):
                await adk_service.list_agents()
    
    @pytest.mark.asyncio
    async def test_get_agent_info_found(self, adk_service, mock_httpx_client, sample_agents_response):
        """Test getting specific agent information"""
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_agents_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            agent_info = await adk_service.get_agent_info("content_planner")
            
            assert agent_info is not None
            assert agent_info.name == "content_planner"
            assert agent_info.available
    
    @pytest.mark.asyncio
    async def test_get_agent_info_not_found(self, adk_service, mock_httpx_client, sample_agents_response):
        """Test getting non-existent agent information"""
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_agents_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            agent_info = await adk_service.get_agent_info("nonexistent_agent")
            
            assert agent_info is None
    
    @pytest.mark.asyncio
    async def test_get_agent_info_invalid_name(self, adk_service):
        """Test getting agent info with invalid name"""
        with pytest.raises(ValueError, match="Invalid agent name"):
            await adk_service.get_agent_info("")
        
        with pytest.raises(ValueError, match="Invalid agent name"):
            await adk_service.get_agent_info(None)
    
    @pytest.mark.asyncio
    async def test_create_session_success(self, adk_service, mock_httpx_client, sample_agents_response, sample_session_response):
        """Test successful session creation"""
        # Mock agent discovery
        mock_agents_response = AsyncMock()
        mock_agents_response.status_code = 200
        mock_agents_response.json.return_value = sample_agents_response
        
        # Mock session creation
        mock_session_response = AsyncMock()
        mock_session_response.status_code = 200
        mock_session_response.json.return_value = sample_session_response
        
        mock_httpx_client.request.side_effect = [mock_agents_response, mock_session_response]
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            session = await adk_service.create_session("content_planner", "user_456")
            
            assert isinstance(session, ADKSessionCreateResponse)
            assert session.id == "session_123"
            assert session.app_name == "content_planner"
            assert session.user_id == "user_456"
            
            # Verify both requests were made
            assert mock_httpx_client.request.call_count == 2
    
    @pytest.mark.asyncio
    async def test_create_session_agent_not_found(self, adk_service, mock_httpx_client, sample_agents_response):
        """Test session creation with non-existent agent"""
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_agents_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(ADKAgentNotFoundError, match="Agent not found: nonexistent_agent"):
                await adk_service.create_session("nonexistent_agent", "user_456")
    
    @pytest.mark.asyncio
    async def test_create_session_invalid_params(self, adk_service):
        """Test session creation with invalid parameters"""
        with pytest.raises(ValueError, match="Invalid app name"):
            await adk_service.create_session("", "user_456")
        
        with pytest.raises(ValueError, match="Invalid user ID"):
            await adk_service.create_session("content_planner", "")
    
    @pytest.mark.asyncio
    async def test_create_session_server_error(self, adk_service, mock_httpx_client, sample_agents_response):
        """Test session creation with server error"""
        # Mock agent discovery success
        mock_agents_response = AsyncMock()
        mock_agents_response.status_code = 200
        mock_agents_response.json.return_value = sample_agents_response
        
        # Mock session creation failure
        mock_session_response = AsyncMock()
        mock_session_response.status_code = 500
        mock_session_response.json.return_value = {"error": "Internal server error"}
        
        mock_httpx_client.request.side_effect = [mock_agents_response, mock_session_response]
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(ADKConnectionError, match="Failed to create session"):
                await adk_service.create_session("content_planner", "user_456")
    
    @pytest.mark.asyncio
    async def test_get_session_success(self, adk_service, mock_httpx_client):
        """Test successful session retrieval"""
        session_data = {
            "id": "session_123",
            "created_at": "2024-01-01T12:00:00Z",
            "last_activity": "2024-01-01T13:00:00Z",
            "event_count": 5,
            "metadata": {"test": "data"}
        }
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = session_data
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            session = await adk_service.get_session("content_planner", "user_456", "session_123")
            
            assert session is not None
            assert isinstance(session, ADKSessionInfo)
            assert session.id == "session_123"
            assert session.app_name == "content_planner"
            assert session.user_id == "user_456"
            assert session.event_count == 5
    
    @pytest.mark.asyncio
    async def test_get_session_not_found(self, adk_service, mock_httpx_client):
        """Test session retrieval when session doesn't exist"""
        mock_response = AsyncMock()
        mock_response.status_code = 404
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            session = await adk_service.get_session("content_planner", "user_456", "nonexistent_session")
            
            assert session is None
    
    @pytest.mark.asyncio
    async def test_get_session_invalid_params(self, adk_service):
        """Test session retrieval with invalid parameters"""
        with pytest.raises(ValueError, match="Invalid app name"):
            await adk_service.get_session("", "user_456", "session_123")
        
        with pytest.raises(ValueError, match="Invalid user ID"):
            await adk_service.get_session("content_planner", "", "session_123")
        
        with pytest.raises(ValueError, match="Invalid session ID"):
            await adk_service.get_session("content_planner", "user_456", "")
    
    @pytest.mark.asyncio
    async def test_make_request_retry_logic(self, adk_service, mock_httpx_client):
        """Test HTTP request retry logic"""
        # First two attempts fail, third succeeds
        mock_httpx_client.request.side_effect = [
            httpx.ConnectError("Connection failed"),
            httpx.ConnectError("Connection failed"),
            AsyncMock(status_code=200)
        ]
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            response = await adk_service._make_request("GET", "/test")
            
            assert response.status_code == 200
            assert mock_httpx_client.request.call_count == 3
    
    @pytest.mark.asyncio
    async def test_make_request_all_retries_fail(self, adk_service, mock_httpx_client):
        """Test HTTP request when all retries fail"""
        mock_httpx_client.request.side_effect = httpx.ConnectError("Connection failed")
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(httpx.ConnectError):
                await adk_service._make_request("GET", "/test")
            
            # Should try max_retries + 1 times
            assert mock_httpx_client.request.call_count == adk_service.max_retries + 1
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_httpx_client, sample_health_response):
        """Test ADK service as async context manager"""
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_health_response
        mock_httpx_client.request.return_value = mock_response
        
        with patch('app.services.adk_service.httpx.AsyncClient', return_value=mock_httpx_client):
            adk_service = ADKService()
            async with adk_service:
                assert adk_service._is_connected
                assert adk_service._client is not None
            
            # Should be closed after context exit
            mock_httpx_client.aclose.assert_called_once()


class TestADKServiceGlobals:
    """Test global ADK service functions"""
    
    @pytest.mark.asyncio
    async def test_get_adk_service_singleton(self):
        """Test global ADK service singleton behavior"""
        with patch('app.services.adk_service.ADKService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            # First call should create instance
            service1 = await get_adk_service()
            mock_service.initialize.assert_called_once()
            
            # Second call should return same instance
            service2 = await get_adk_service()
            assert service1 is service2
            
            # Initialize should only be called once
            assert mock_service.initialize.call_count == 1
    
    @pytest.mark.asyncio
    async def test_adk_service_context_manager(self):
        """Test ADK service context manager"""
        with patch('app.services.adk_service.ADKService') as mock_service_class:
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            
            async with adk_service_context() as adk:
                assert adk is mock_service
                mock_service.initialize.assert_called_once()
            
            mock_service.close.assert_called_once()


class TestADKServiceErrorHandling:
    """Test error handling scenarios"""
    
    @pytest.fixture
    def adk_service(self):
        return ADKService(max_retries=1, retry_delay=0.01)
    
    @pytest.mark.asyncio
    async def test_connection_error_handling(self, adk_service, mock_httpx_client):
        """Test handling of connection errors"""
        mock_httpx_client.request.side_effect = httpx.ConnectError("Connection refused")
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(ADKConnectionError):
                await adk_service.list_agents()
    
    @pytest.mark.asyncio
    async def test_timeout_error_handling(self, adk_service, mock_httpx_client):
        """Test handling of timeout errors"""
        mock_httpx_client.request.side_effect = httpx.TimeoutException("Request timeout")
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(httpx.TimeoutException):
                await adk_service._make_request("GET", "/test")
    
    @pytest.mark.asyncio
    async def test_server_unavailable_handling(self, adk_service, mock_httpx_client):
        """Test handling of server unavailable scenarios"""
        mock_response = AsyncMock()
        mock_response.status_code = 503
        mock_httpx_client.request.return_value = mock_response
        
        with patch.object(adk_service, 'client', mock_httpx_client):
            with pytest.raises(ADKServerUnavailableError):
                await adk_service.list_agents()


if __name__ == "__main__":
    pytest.main([__file__])