# Social Media Agents 2025 - ADK Configuration
# Copy this file to .env and configure with your actual values

# ================================
# GOOGLE ADK CONFIGURATION
# ================================

# Authentication Method 1: Google AI Studio (Recommended for Development)
GOOGLE_API_KEY=AIzaSyBohXKf6Ra7pisAHMus15sLoiFFprgQV1Y
USE_VERTEX_AI=false

# Authentication Method 2: Vertex AI (Recommended for Production)
# USE_VERTEX_AI=true
# GCP_PROJECT_ID=your-gcp-project-id
# GCP_LOCATION=us-central1

# ================================
# ADK SESSION MANAGEMENT
# ================================

# Session Service Configuration
# Options:
#   - Empty (InMemorySessionService - Development only)
#   - sqlite:///path/to/database.db (DatabaseSessionService)
#   - agentengine://reasoning_engine_id (VertexAiSessionService - Production)
ADK_SESSION_SERVICE_URI=

# Vertex AI Agent Engine Configuration (for production sessions)
ADK_AGENT_ENGINE_ID=
ADK_MEMORY_BANK_ID=

# Database Configuration (for DatabaseSessionService)
DATABASE_URL=sqlite:///./adk_sessions.db

# ================================
# GOOGLE CLOUD SERVICES
# ================================

# Google Cloud Project
GOOGLE_CLOUD_PROJECT=your-gcp-project-id

# Firestore
FIRESTORE_PROJECT_ID=${GOOGLE_CLOUD_PROJECT}

# BigQuery
BIGQUERY_PROJECT_ID=${GOOGLE_CLOUD_PROJECT}
BIGQUERY_DATASET_ID=social_media_analytics

# Secret Manager
SECRET_MANAGER_PROJECT_ID=${GOOGLE_CLOUD_PROJECT}

# ================================
# API AUTHENTICATION
# ================================

# YouTube API
YOUTUBE_API_KEY=your_youtube_api_key_here

# Instagram API
INSTAGRAM_APP_ID=your_instagram_app_id
INSTAGRAM_APP_SECRET=your_instagram_app_secret

# Google Search API
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id

# OAuth Configuration
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# ================================
# APPLICATION CONFIGURATION
# ================================

# Security
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://yourdomain.com

# Logging
LOG_LEVEL=INFO

# ================================
# REDIS CONFIGURATION
# ================================

# Redis for background tasks
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# ================================
# ADK ADVANCED CONFIGURATION
# ================================

# State Scoping Prefixes (Advanced - Don't change unless you know what you're doing)
ADK_STATE_APP_PREFIX=app:
ADK_STATE_USER_PREFIX=user:
ADK_STATE_TEMP_PREFIX=temp:

# OpenTelemetry Configuration
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=
OTEL_SERVICE_NAME=social-media-agents
OTEL_RESOURCE_ATTRIBUTES=service.name=social-media-agents,service.version=1.0.0

# ================================
# DEVELOPMENT CONFIGURATION
# ================================

# Development flags
DEBUG=false
ENVIRONMENT=development

# Hot reload for development
RELOAD_AGENTS=true

# ================================
# DEPLOYMENT CONFIGURATION
# ================================

# For production deployment
# PORT=8080
# HOST=0.0.0.0

# Cloud Run specific
# CLOUD_RUN_SERVICE_URL=
# CLOUD_RUN_REGION=us-central1

# Agent Engine specific
# AGENT_ENGINE_RESOURCE_NAME=projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine_id}