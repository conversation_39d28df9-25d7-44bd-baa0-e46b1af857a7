# Cloud Run Staging Environment Configuration

# This directory contains Cloud Run deployment configurations and scripts
# for the Social Media Manager application.

## Files Overview

### Service Configurations
- `cloud-run-backend.yaml` - Backend service configuration for Cloud Run
- `cloud-run-frontend.yaml` - Frontend service configuration for Cloud Run

### Deployment Scripts
- `deploy.sh` - Linux/macOS deployment script
- `deploy.ps1` - Windows PowerShell deployment script

### Environment Configurations
- `staging.env` - Staging environment variables
- `production.env` - Production environment variables (template)

## Deployment Architecture

### Backend Service
- **Service Name**: `social-media-backend`
- **Container Port**: 8000
- **Resources**: 2 CPU, 4GB RAM
- **Autoscaling**: 1-100 instances
- **Service Account**: `social-media-backend@PROJECT_ID.iam.gserviceaccount.com`

### Frontend Service
- **Service Name**: `social-media-frontend`
- **Container Port**: 3000
- **Resources**: 1 CPU, 2GB RAM
- **Autoscaling**: 1-50 instances
- **Public Access**: Allowed

## Prerequisites

### 1. Google Cloud Setup
```bash
# Install gcloud CLI
curl https://sdk.cloud.google.com | bash

# Authenticate
gcloud auth login
gcloud auth configure-docker

# Set project
gcloud config set project YOUR_PROJECT_ID
```

### 2. Required APIs
The deployment script will enable these APIs automatically:
- Cloud Run API
- Cloud Build API
- Container Registry API
- Secret Manager API
- Firestore API
- BigQuery API

### 3. Required Secrets
The following secrets need to be created in Secret Manager:
- `app-secret-key` - JWT secret key
- `google-client-id` - Google OAuth client ID
- `google-client-secret` - Google OAuth client secret
- `youtube-api-key` - YouTube Data API key
- `instagram-app-id` - Instagram app ID
- `instagram-app-secret` - Instagram app secret

## Deployment Environments

### Staging Deployment
```bash
# Linux/macOS
./deploy.sh --project your-staging-project --region us-central1

# Windows
.\deploy.ps1 -ProjectId your-staging-project -Region us-central1
```

### Production Deployment
```bash
# Linux/macOS
./deploy.sh --project your-production-project --region us-central1

# Windows
.\deploy.ps1 -ProjectId your-production-project -Region us-central1
```

## Configuration Variables

### Backend Environment Variables
- `GOOGLE_CLOUD_PROJECT` - GCP project ID
- `FIRESTORE_PROJECT_ID` - Firestore project ID
- `SECRET_KEY` - JWT secret (from Secret Manager)
- `GOOGLE_CLIENT_ID` - OAuth client ID (from Secret Manager)
- `GOOGLE_CLIENT_SECRET` - OAuth client secret (from Secret Manager)
- `YOUTUBE_API_KEY` - YouTube API key (from Secret Manager)
- `INSTAGRAM_APP_ID` - Instagram app ID (from Secret Manager)
- `INSTAGRAM_APP_SECRET` - Instagram app secret (from Secret Manager)

### Frontend Environment Variables
- `NEXT_PUBLIC_API_URL` - Backend API URL
- `NEXT_PUBLIC_GOOGLE_CLIENT_ID` - Google OAuth client ID (public)
- `NEXT_PUBLIC_INSTAGRAM_APP_ID` - Instagram app ID (public)

## Security Configuration

### IAM Roles
The backend service account requires these roles:
- `roles/secretmanager.secretAccessor` - Access secrets
- `roles/datastore.user` - Access Firestore
- `roles/bigquery.user` - Access BigQuery

### Network Security
- Backend: Private by default, accessible via frontend
- Frontend: Public access allowed
- All sensitive data stored in Secret Manager

## Monitoring and Logging

### Health Checks
Both services include health check endpoints:
- Backend: `/health` and `/health/ready`
- Frontend: `/api/health`

### Logging
- Application logs: Cloud Logging
- Error tracking: Automatic error reporting
- Performance: Cloud Trace integration

### Monitoring
- CPU and memory usage metrics
- Request latency and error rates
- Custom application metrics

## Scaling Configuration

### Backend Autoscaling
- **Min instances**: 1 (always warm)
- **Max instances**: 100
- **CPU allocation**: 2 vCPU
- **Memory**: 4GB
- **Container concurrency**: 80 requests

### Frontend Autoscaling
- **Min instances**: 1 (always warm)
- **Max instances**: 50
- **CPU allocation**: 1 vCPU
- **Memory**: 2GB
- **Container concurrency**: 100 requests

## Custom Domains

### Setup Custom Domains
1. Deploy services first
2. Run deployment script with domain setup
3. Configure DNS records:
   ```
   api.yourdomain.com CNAME ghs.googlehosted.com
   app.yourdomain.com CNAME ghs.googlehosted.com
   ```

### SSL Certificates
- Automatically provisioned by Google Cloud
- Managed certificates for custom domains
- HTTPS enforced for all traffic

## CI/CD Integration

### GitHub Actions
Integration with existing GitHub Actions workflow:
```yaml
- name: Deploy to Cloud Run
  run: |
    ./deployment/deploy.sh \
      --project ${{ secrets.GCP_PROJECT_ID }} \
      --region us-central1 \
      --skip-secrets
```

### Environment Promotion
- Deploy to staging automatically on merge to main
- Deploy to production manually with approval
- Blue-green deployments supported

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Check service account permissions
   gcloud projects get-iam-policy PROJECT_ID
   ```

2. **Build Failures**
   ```bash
   # Check Cloud Build logs
   gcloud builds list --project=PROJECT_ID
   ```

3. **Service Unavailable**
   ```bash
   # Check service logs
   gcloud run services logs read SERVICE_NAME --region=REGION
   ```

### Debug Commands
```bash
# Check service status
gcloud run services describe SERVICE_NAME --region=REGION

# View logs
gcloud run services logs tail SERVICE_NAME --region=REGION

# Test locally
docker run -p 8080:8000 gcr.io/PROJECT_ID/social-media-backend
```

## Cost Optimization

### Resource Optimization
- Use minimum required CPU/memory
- Configure appropriate concurrency levels
- Set minimum instances to 1 for production

### Request Optimization
- Enable request/response compression
- Use CDN for static assets
- Implement proper caching headers

### Monitoring Costs
- Set up billing alerts
- Monitor service usage metrics
- Regular cost analysis and optimization