# BigQuery SQL Queries for Social Media Analytics

This directory contains SQL queries for analyzing social media performance data stored in BigQuery.

## Table Structure

The analytics data warehouse consists of the following tables:

- `user_metrics_daily` - Daily aggregated metrics per user
- `account_metrics_daily` - Daily metrics per connected social media account
- `content_performance` - Individual post/content performance data
- `engagement_events` - Real-time engagement events (likes, comments, shares)
- `platform_insights` - Platform-specific insights and trends
- `ai_predictions` - AI model predictions and their accuracy
- `chat_analytics` - Chat session analytics and user interaction data

## Query Categories

### User Analytics
- User engagement trends over time
- User growth and retention metrics
- Content planning behavior analysis

### Account Performance
- Account growth tracking
- Cross-platform performance comparison
- Follower engagement analysis

### Content Analysis
- Top performing content identification
- Content type performance comparison
- Hashtag and timing optimization

### AI Insights
- Prediction accuracy tracking
- Model performance evaluation
- Recommendation effectiveness

## Usage

These queries can be used in:
- BigQuery Console for ad-hoc analysis
- Data visualization tools (Looker, Data Studio)
- Automated reporting pipelines
- API endpoints for dashboard data

## Query Execution

Run queries in BigQuery Console or use the BigQuery API:

```python
from google.cloud import bigquery

client = bigquery.Client(project="your-project-id")
query = open("queries/user_engagement_trends.sql").read()
results = client.query(query).result()
```