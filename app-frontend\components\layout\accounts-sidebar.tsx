"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Plus, Youtube, Instagram, Twitter, TrendingUp } from "lucide-react";
import { useConnectedAccounts } from "@/hooks/use-connected-accounts";
import { ConnectionModal } from "@/components/connections/connection-modal";

export function AccountsSidebar() {
  const [isConnectionModalOpen, setIsConnectionModalOpen] = useState(false);
  const { data: accounts, isLoading } = useConnectedAccounts();

  const platformIcons = {
    youtube: Youtube,
    instagram: Instagram,
    twitter: Twitter,
  };

  const platformColors = {
    youtube: "text-red-500",
    instagram: "text-pink-500",
    twitter: "text-blue-500",
  };

  return (
    <>
      <aside className="w-80 border-r bg-muted/30 p-6 overflow-y-auto">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Connected Accounts</h2>
            <Button
              size="sm"
              onClick={() => setIsConnectionModalOpen(true)}
              className="h-8 w-8 p-0"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {isLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 bg-muted rounded-full" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4" />
                        <div className="h-3 bg-muted rounded w-1/2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : accounts && accounts.length > 0 ? (
            <div className="space-y-3">
              {accounts.map((account) => {
                const Icon = platformIcons[account.platform as keyof typeof platformIcons];
                const colorClass = platformColors[account.platform as keyof typeof platformColors];
                
                return (
                  <Card key={account.id} className="hover:bg-muted/50 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={account.avatar} alt={account.handle} />
                          <AvatarFallback>
                            {Icon && <Icon className={`h-5 w-5 ${colorClass}`} />}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <p className="font-medium truncate">{account.handle}</p>
                            <Badge variant="secondary" className="text-xs">
                              {account.platform}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <TrendingUp className="h-3 w-3" />
                            <span>{account.metrics?.followers?.toLocaleString() || 0} followers</span>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Last sync: {new Date(account.lastSync).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <Card className="border-dashed">
              <CardContent className="p-6 text-center">
                <div className="space-y-3">
                  <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                    <Plus className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium">No accounts connected</h3>
                    <p className="text-sm text-muted-foreground">
                      Connect your social media accounts to get started
                    </p>
                  </div>
                  <Button
                    onClick={() => setIsConnectionModalOpen(true)}
                    className="w-full"
                  >
                    Connect Account
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {accounts && accounts.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Total Followers</span>
                  <span className="font-medium">
                    {accounts.reduce((sum, acc) => sum + (acc.metrics?.followers || 0), 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Avg. Engagement</span>
                  <span className="font-medium">
                    {(accounts.reduce((sum, acc) => sum + (acc.metrics?.engagement || 0), 0) / accounts.length || 0).toFixed(1)}%
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </aside>

      <ConnectionModal
        isOpen={isConnectionModalOpen}
        onClose={() => setIsConnectionModalOpen(false)}
      />
    </>
  );
}