#!/usr/bin/env python3
"""
Simple test to verify the Google Search fix works
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_google_search_root_agent():
    """Test loading the Google Search Root Agent directly."""
    print("🔍 Testing Google Search Root Agent...")
    
    try:
        # Add agents directory to path
        agents_dir = os.path.join(os.path.dirname(__file__), 'agents')
        sys.path.insert(0, agents_dir)
        
        # Import the Google Search ROOT agent
        from google_search_root_agent.agent import root_agent as search_agent
        
        print(f"✅ Google Search Root Agent loaded: {search_agent.name}")
        print(f"   Model: {search_agent.model}")
        print(f"   Tools: {len(search_agent.tools)}")
        
        # Check if it has google_search tool
        for tool in search_agent.tools:
            tool_name = str(tool)
            print(f"     • Tool: {tool_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_adk_imports():
    """Test basic ADK imports."""
    print("\n🔧 Testing ADK Imports...")
    
    try:
        from google.adk.agents import LlmAgent
        from google.adk.tools import google_search, AgentTool
        print("✅ ADK imports successful")
        return True
    except Exception as e:
        print(f"❌ ADK import error: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Simple Google Search Fix Test")
    print("=" * 40)
    
    adk_ok = test_adk_imports()
    search_ok = test_google_search_root_agent()
    
    print("\n" + "=" * 40)
    print("📊 Results:")
    print(f"   ADK Imports: {'✅ PASS' if adk_ok else '❌ FAIL'}")
    print(f"   Google Search Agent: {'✅ PASS' if search_ok else '❌ FAIL'}")
    
    if adk_ok and search_ok:
        print("\n🎉 Basic components are working!")
        print("   The Google Search Root Agent loads correctly.")
        print("   You can now test it with 'adk web' command.")
    else:
        print("\n⚠️ Some components failed to load.")
        print("   Check the errors above.")

if __name__ == "__main__":
    main()