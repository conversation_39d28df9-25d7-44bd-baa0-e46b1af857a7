"use client";

import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { EnhancedChatMessage, ADKSessionInfo, ChatError, ADKEvent } from '@/types/adk';

export interface ChatHistoryOptions {
  sessionId?: string;
  userId: string;
  agentName: string;
  limit?: number;
  offset?: number;
  includeMetadata?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface ChatHistoryResponse {
  messages: EnhancedChatMessage[];
  totalCount: number;
  sessionInfo?: ADKSessionInfo;
  hasMore: boolean;
}

export interface UseChatHistoryReturn {
  messages: EnhancedChatMessage[];
  isLoading: boolean;
  isError: boolean;
  error: ChatError | null;
  totalCount: number;
  hasMore: boolean;
  sessionInfo: ADKSessionInfo | null;
  refetch: () => Promise<void>;
  loadMore: () => Promise<void>;
  clearHistory: () => void;
  refreshHistory: () => Promise<void>;
}

const DEFAULT_LIMIT = 50;
const DEFAULT_REFRESH_INTERVAL = 30000; // 30 seconds

export function useChatHistory(options: ChatHistoryOptions): UseChatHistoryReturn {
  const {
    sessionId,
    userId,
    agentName,
    limit = DEFAULT_LIMIT,
    offset = 0,
    includeMetadata = true,
    autoRefresh = false,
    refreshInterval = DEFAULT_REFRESH_INTERVAL
  } = options;

  const queryClient = useQueryClient();
  const [currentOffset, setCurrentOffset] = useState(offset);

  const queryKey = ['chat', 'history', sessionId, userId, agentName];

  // Transform ADK Event to EnhancedChatMessage
  const transformADKEventToMessage = useCallback((
    event: ADKEvent, 
    sessionId: string, 
    userId: string, 
    agentName: string
  ): EnhancedChatMessage | null => {
    if (!event.content || !event.content.parts || event.content.parts.length === 0) {
      return null;
    }

    // Extract text content from parts
    let content = '';
    const functionCalls: Record<string, any>[] = [];

    event.content.parts.forEach(part => {
      if (part.text) {
        content += part.text;
      }
      if (part.function_call) {
        functionCalls.push(part.function_call);
      }
    });

    if (!content && functionCalls.length === 0) {
      return null;
    }

    return {
      id: event.invocation_id || `event_${Date.now()}_${Math.random()}`,
      session_id: sessionId,
      role: event.content.role,
      content,
      user_id: userId,
      timestamp: new Date().toISOString(), // ADK events don't have timestamps, use current time
      agent_name: event.author || agentName,
      adk_invocation_id: event.invocation_id,
      function_calls: functionCalls.length > 0 ? functionCalls : undefined,
      interrupted: event.interrupted,
      metadata: {
        ...event.metadata,
        turn_complete: event.turn_complete,
        long_running_tool_ids: event.long_running_tool_ids
      }
    };
  }, []);

  // Fetch chat history from backend
  const fetchChatHistory = useCallback(async (): Promise<ChatHistoryResponse> => {
    if (!sessionId) {
      return {
        messages: [],
        totalCount: 0,
        hasMore: false
      };
    }

    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
    const authToken = typeof window !== 'undefined' ? localStorage.getItem('access_token') || 'dev-token' : 'dev-token';

    try {
      const params = new URLSearchParams({
        session_id: sessionId,
        user_id: userId,
        limit: limit.toString(),
        offset: currentOffset.toString(),
        include_metadata: includeMetadata.toString()
      });

      const response = await fetch(`${baseUrl}/chat/history?${params}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response || !response.ok) {
        throw new Error(`HTTP ${response?.status || 'unknown'}: ${response?.statusText || 'Network error'}`);
      }

      const data = await response.json();

      // If the backend returns ADK events, transform them
      let messages: EnhancedChatMessage[] = [];
      
      if (data.adk_events) {
        // Transform ADK events to messages
        messages = data.adk_events
          .map((event: ADKEvent) => transformADKEventToMessage(event, sessionId, userId, agentName))
          .filter((msg: EnhancedChatMessage | null) => msg !== null);
      } else if (data.messages) {
        // Use messages directly if already in the correct format
        messages = data.messages;
      }

      return {
        messages,
        totalCount: data.total_count || messages.length,
        sessionInfo: data.session_info,
        hasMore: data.has_more || false
      };

    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      const chatError: ChatError = {
        error: error instanceof Error ? error.message : 'Failed to fetch chat history',
        error_code: 'HISTORY_FETCH_ERROR',
        session_id: sessionId
      };
      throw chatError;
    }
  }, [sessionId, userId, agentName, limit, currentOffset, includeMetadata, transformADKEventToMessage]);

  // React Query for chat history
  const {
    data: historyData,
    isLoading,
    isError,
    error,
    refetch: queryRefetch
  } = useQuery({
    queryKey: [...queryKey, currentOffset, limit],
    queryFn: fetchChatHistory,
    enabled: !!sessionId,
    staleTime: 10000, // 10 seconds
    refetchInterval: autoRefresh ? refreshInterval : false,
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error && typeof error === 'object' && 'error_code' in error) {
        const chatError = error as unknown as ChatError;
        if (chatError.error_code === 'AUTHENTICATION_ERROR') {
          return false;
        }
      }
      return failureCount < 1; // Reduce retries for testing
    },
    retryDelay: 100 // Faster retry for testing
  });

  const messages = historyData?.messages || [];
  const totalCount = historyData?.totalCount || 0;
  const hasMore = historyData?.hasMore || false;
  const sessionInfo = historyData?.sessionInfo || null;

  const refetch = useCallback(async () => {
    await queryRefetch();
  }, [queryRefetch]);

  const loadMore = useCallback(async () => {
    if (hasMore && !isLoading) {
      const newOffset = currentOffset + limit;
      setCurrentOffset(newOffset);
      
      // Fetch additional messages and append to existing ones
      const additionalData = await fetchChatHistory();
      
      queryClient.setQueryData([...queryKey, newOffset, limit], (old: ChatHistoryResponse | undefined) => {
        if (!old) return additionalData;
        
        return {
          ...additionalData,
          messages: [...old.messages, ...additionalData.messages]
        };
      });
    }
  }, [hasMore, isLoading, currentOffset, limit, fetchChatHistory, queryClient, queryKey]);

  const clearHistory = useCallback(() => {
    queryClient.removeQueries({ queryKey });
    setCurrentOffset(0);
  }, [queryClient, queryKey]);

  const refreshHistory = useCallback(async () => {
    if (currentOffset !== 0) {
      setCurrentOffset(0);
    }
    await queryRefetch();
  }, [queryRefetch, currentOffset]);

  // Update query cache when new messages are added (from real-time chat)
  const updateHistoryCache = useCallback((newMessage: EnhancedChatMessage) => {
    queryClient.setQueryData([...queryKey, 0, limit], (old: ChatHistoryResponse | undefined) => {
      if (!old) {
        return {
          messages: [newMessage],
          totalCount: 1,
          hasMore: false
        };
      }

      // Check if message already exists
      const existingMessage = old.messages.find(msg => msg.id === newMessage.id);
      if (existingMessage) {
        // Update existing message
        return {
          ...old,
          messages: old.messages.map(msg => 
            msg.id === newMessage.id ? newMessage : msg
          )
        };
      }

      // Add new message
      return {
        ...old,
        messages: [newMessage, ...old.messages],
        totalCount: old.totalCount + 1
      };
    });
  }, [queryClient, queryKey, limit]);

  // Expose cache update function for use by chat components
  useEffect(() => {
    // Store the update function in a way that other hooks can access it
    (window as any).__updateChatHistoryCache = updateHistoryCache;
    
    return () => {
      delete (window as any).__updateChatHistoryCache;
    };
  }, [updateHistoryCache]);

  return {
    messages,
    isLoading,
    isError,
    error: error as ChatError | null,
    totalCount,
    hasMore,
    sessionInfo,
    refetch,
    loadMore,
    clearHistory,
    refreshHistory
  };
}