"""
Enhanced Chat Models with ADK Integration

This module contains enhanced chat models that integrate with ADK while maintaining
backward compatibility with existing chat functionality.

Requirements covered: 1.2, 10.1, 10.3
"""

from pydantic import BaseModel, Field, validator, model_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum
from .adk_models import MessageRole, ADKEvent, StreamingChunk


class ChatSessionType(str, Enum):
    """Type of chat session"""
    LEGACY = "legacy"  # Existing chat system
    ADK = "adk"        # ADK-powered chat


class ChatSession(BaseModel):
    """Enhanced chat session with ADK integration"""
    id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    session_type: ChatSessionType = Field(default=ChatSessionType.LEGACY, description="Session type")
    agent_name: Optional[str] = Field(None, description="ADK agent name (for ADK sessions)")
    adk_session_id: Optional[str] = Field(None, description="ADK session identifier")
    created_at: datetime = Field(default_factory=datetime.now, description="Session creation time")
    last_activity: datetime = Field(default_factory=datetime.now, description="Last activity timestamp")
    message_count: int = Field(default=0, description="Number of messages in session")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Session metadata")
    
    @validator('agent_name')
    def validate_agent_name_for_adk(cls, v, values):
        """Validate agent name is provided for ADK sessions"""
        if values.get('session_type') == ChatSessionType.ADK and not v:
            raise ValueError("agent_name is required for ADK sessions")
        return v


class EnhancedChatMessage(BaseModel):
    """Enhanced chat message with ADK support"""
    id: str = Field(..., description="Message identifier")
    session_id: str = Field(..., description="Session identifier")
    role: MessageRole = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    user_id: str = Field(..., description="User identifier")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")
    
    # ADK-specific fields
    agent_name: Optional[str] = Field(None, description="ADK agent name")
    adk_invocation_id: Optional[str] = Field(None, description="ADK invocation ID")
    function_calls: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="Function calls")
    interrupted: bool = Field(default=False, description="Whether message was interrupted")
    
    # Metadata and processing info
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    
    class Config:
        """Pydantic configuration"""
        json_schema_extra = {
            "example": {
                "id": "msg_123",
                "session_id": "session_456",
                "role": "user",
                "content": "Help me create a content plan",
                "user_id": "user_789",
                "timestamp": "2024-01-01T12:00:00Z",
                "agent_name": "content_planner",
                "adk_invocation_id": "inv_123",
                "function_calls": [],
                "interrupted": False,
                "metadata": {},
                "processing_time_ms": 1500.0
            }
        }


class ChatMessageRequest(BaseModel):
    """Request to send a chat message"""
    message: str = Field(..., description="Message content", min_length=1)
    session_id: Optional[str] = Field(None, description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    agent_name: Optional[str] = Field(None, description="Preferred ADK agent")
    use_adk: bool = Field(default=True, description="Whether to use ADK integration")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


class ChatMessageResponse(BaseModel):
    """Response from sending a chat message"""
    message_id: str = Field(..., description="Created message identifier")
    session_id: str = Field(..., description="Session identifier")
    streaming_url: Optional[str] = Field(None, description="URL for streaming response")
    estimated_response_time: Optional[float] = Field(None, description="Estimated response time in seconds")


class StreamingResponse(BaseModel):
    """Streaming response wrapper"""
    chunk: StreamingChunk = Field(..., description="Streaming chunk data")
    session_id: str = Field(..., description="Session identifier")
    message_id: str = Field(..., description="Message identifier")


class ChatHistoryRequest(BaseModel):
    """Request for chat history"""
    session_id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    limit: int = Field(default=50, description="Maximum number of messages", ge=1, le=1000)
    offset: int = Field(default=0, description="Offset for pagination", ge=0)
    include_metadata: bool = Field(default=False, description="Include message metadata")


class ChatHistoryResponse(BaseModel):
    """Response containing chat history"""
    messages: List[EnhancedChatMessage] = Field(..., description="Chat messages")
    total_count: int = Field(..., description="Total number of messages in session")
    session_info: ChatSession = Field(..., description="Session information")
    has_more: bool = Field(..., description="Whether more messages are available")


class AgentSelectionRequest(BaseModel):
    """Request to select or switch agents"""
    user_id: str = Field(..., description="User identifier")
    agent_name: str = Field(..., description="Agent name to select")
    session_id: Optional[str] = Field(None, description="Current session ID")
    preserve_history: bool = Field(default=True, description="Whether to preserve chat history")


class AgentSelectionResponse(BaseModel):
    """Response from agent selection"""
    session_id: str = Field(..., description="Session identifier (new or existing)")
    agent_name: str = Field(..., description="Selected agent name")
    agent_info: Optional[Dict[str, Any]] = Field(None, description="Agent information")
    history_preserved: bool = Field(..., description="Whether history was preserved")


class ChatError(BaseModel):
    """Chat error response"""
    error: str = Field(..., description="Error message")
    error_code: str = Field(..., description="Error code")
    session_id: Optional[str] = Field(None, description="Session identifier")
    message_id: Optional[str] = Field(None, description="Message identifier")
    details: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Error details")
    retry_after: Optional[int] = Field(None, description="Retry after seconds")


class ChatStatus(BaseModel):
    """Chat system status"""
    adk_enabled: bool = Field(..., description="Whether ADK integration is enabled")
    available_agents: List[str] = Field(..., description="Available ADK agents")
    active_sessions: int = Field(..., description="Number of active sessions")
    system_health: str = Field(..., description="System health status")
    features: Dict[str, bool] = Field(..., description="Feature availability")


# Event transformation utilities - Available from services package
# from ..services.event_transformer import ADKEventTransformer


# Validation utilities
class ChatValidators:
    """Validation utilities for chat models"""
    
    @staticmethod
    def validate_message_content(content: str) -> bool:
        """Validate message content"""
        if not content or not isinstance(content, str):
            return False
        return len(content.strip()) > 0
    
    @staticmethod
    def validate_session_id(session_id: str) -> bool:
        """Validate session ID format"""
        if not session_id or not isinstance(session_id, str):
            return False
        return len(session_id.strip()) > 0
    
    @staticmethod
    def validate_user_id(user_id: str) -> bool:
        """Validate user ID format"""
        if not user_id or not isinstance(user_id, str):
            return False
        return len(user_id.strip()) > 0