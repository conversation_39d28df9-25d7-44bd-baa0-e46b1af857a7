/**
 * Tests for ADK chat integration hook
 * 
 * Requirements covered: 1.2, 2.2, 5.3
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode } from 'react';
import { useADKChat } from '@/hooks/use-adk-chat';
import { EnhancedChatMessage, StreamingChunk, ChatError } from '@/types/adk';

// Mock the streaming hook
jest.mock('@/hooks/use-adk-streaming', () => ({
  useADKStreaming: jest.fn()
}));

import { useADKStreaming } from '@/hooks/use-adk-streaming';

const mockUseADKStreaming = useADKStreaming as jest.MockedFunction<typeof useADKStreaming>;

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useADKChat', () => {
  let mockStreamingHook: {
    isConnected: boolean;
    isStreaming: boolean;
    isReconnecting: boolean;
    currentChunk: StreamingChunk | null;
    error: ChatError | null;
    connectionAttempts: number;
    startStreaming: jest.Mock;
    stopStreaming: jest.Mock;
    interruptStreaming: jest.Mock;
    reconnect: jest.Mock;
    clearError: jest.Mock;
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('test-token');

    mockStreamingHook = {
      isConnected: true,
      isStreaming: false,
      isReconnecting: false,
      currentChunk: null,
      error: null,
      connectionAttempts: 0,
      startStreaming: jest.fn(),
      stopStreaming: jest.fn(),
      interruptStreaming: jest.fn(),
      reconnect: jest.fn(),
      clearError: jest.fn()
    };

    mockUseADKStreaming.mockReturnValue(mockStreamingHook);
  });

  const defaultOptions = {
    userId: 'test-user',
    sessionId: 'test-session',
    agentName: 'test-agent',
    enableStreaming: true
  };

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    expect(result.current.messages).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isStreaming).toBe(false);
    expect(result.current.isReconnecting).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.currentAgent).toBe('test-agent');
    expect(result.current.streamingMessage).toBe(null);
  });

  it('should send message with streaming enabled', async () => {
    mockStreamingHook.startStreaming.mockResolvedValue(undefined);

    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    await act(async () => {
      await result.current.sendMessage('Hello, world!');
    });

    expect(mockStreamingHook.startStreaming).toHaveBeenCalledWith(
      expect.stringMatching(/^assistant_\d+$/),
      'Hello, world!'
    );

    // Check that user message was added to query cache
    expect(result.current.messages).toHaveLength(1);
    expect(result.current.messages[0]).toMatchObject({
      role: 'user',
      content: 'Hello, world!',
      user_id: 'test-user'
    });
  });

  it('should send message with streaming disabled', async () => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        content: 'Hello back!',
        metadata: { author: 'test-agent' }
      })
    });

    const { result } = renderHook(() => 
      useADKChat({
        ...defaultOptions,
        enableStreaming: false
      }), {
      wrapper: createWrapper()
    });

    await act(async () => {
      await result.current.sendMessage('Hello, world!');
    });

    expect(mockFetch).toHaveBeenCalledWith(
      'http://localhost:8000/api/chat/send-message',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }),
        body: JSON.stringify({
          message: 'Hello, world!',
          session_id: 'test-session',
          user_id: 'test-user',
          agent_name: 'test-agent'
        })
      })
    );

    // Should have both user and assistant messages
    expect(result.current.messages).toHaveLength(2);
    expect(result.current.messages[1]).toMatchObject({
      role: 'model',
      content: 'Hello back!',
      user_id: 'test-user'
    });
  });

  it('should handle streaming chunks', async () => {
    const onMessageReceived = jest.fn();
    
    const { result } = renderHook(() => 
      useADKChat({
        ...defaultOptions,
        onMessageReceived
      }), {
      wrapper: createWrapper()
    });

    // Simulate streaming chunks
    const chunk1: StreamingChunk = {
      content: 'Hello, ',
      done: false,
      message_id: 'test-message-id',
      metadata: { author: 'test-agent' }
    };

    const chunk2: StreamingChunk = {
      content: 'world!',
      done: true,
      message_id: 'test-message-id',
      metadata: { author: 'test-agent' }
    };

    // Get the onChunk callback from the streaming hook
    const streamingOptions = mockUseADKStreaming.mock.calls[0][0];
    
    act(() => {
      streamingOptions.onChunk?.(chunk1);
    });

    expect(result.current.streamingMessage).toMatchObject({
      id: 'test-message-id',
      content: 'Hello, ',
      role: 'model'
    });

    act(() => {
      streamingOptions.onChunk?.(chunk2);
    });

    expect(result.current.streamingMessage).toMatchObject({
      content: 'Hello, world!',
      role: 'model'
    });

    // Simulate completion
    act(() => {
      streamingOptions.onComplete?.('test-message-id');
    });

    expect(onMessageReceived).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'test-message-id',
        content: 'Hello, world!',
        role: 'model'
      })
    );
    expect(result.current.streamingMessage).toBe(null);
  });

  it('should handle streaming errors', async () => {
    const onError = jest.fn();
    
    const { result } = renderHook(() => 
      useADKChat({
        ...defaultOptions,
        onError
      }), {
      wrapper: createWrapper()
    });

    const error: ChatError = {
      error: 'Connection failed',
      error_code: 'STREAM_ERROR',
      session_id: 'test-session'
    };

    // Get the onError callback from the streaming hook
    const streamingOptions = mockUseADKStreaming.mock.calls[0][0];
    
    act(() => {
      streamingOptions.onError?.(error);
    });

    expect(onError).toHaveBeenCalledWith(error);
    expect(result.current.streamingMessage).toBe(null);
  });

  it('should handle send message errors', async () => {
    mockStreamingHook.startStreaming.mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    await act(async () => {
      try {
        await result.current.sendMessage('Hello, world!');
      } catch (error) {
        // Expected to throw
      }
    });

    // User message should be removed from cache on error
    expect(result.current.messages).toHaveLength(0);
  });

  it('should retry last message', async () => {
    mockStreamingHook.startStreaming.mockResolvedValue(undefined);

    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    // Send initial message
    await act(async () => {
      await result.current.sendMessage('Hello, world!');
    });

    // Clear the mock to verify retry call
    mockStreamingHook.startStreaming.mockClear();

    // Retry last message
    await act(async () => {
      await result.current.retryLastMessage();
    });

    expect(mockStreamingHook.startStreaming).toHaveBeenCalledWith(
      expect.stringMatching(/^assistant_\d+$/),
      'Hello, world!'
    );
  });

  it('should interrupt streaming', () => {
    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    act(() => {
      result.current.interruptStreaming();
    });

    expect(mockStreamingHook.interruptStreaming).toHaveBeenCalled();
  });

  it('should clear errors', () => {
    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    act(() => {
      result.current.clearError();
    });

    expect(mockStreamingHook.clearError).toHaveBeenCalled();
  });

  it('should handle HTTP errors in non-streaming mode', async () => {
    mockFetch.mockResolvedValue({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error'
    });

    const { result } = renderHook(() => 
      useADKChat({
        ...defaultOptions,
        enableStreaming: false
      }), {
      wrapper: createWrapper()
    });

    await act(async () => {
      try {
        await result.current.sendMessage('Hello, world!');
      } catch (error) {
        // Expected to throw
      }
    });

    // User message should be removed on error
    expect(result.current.messages).toHaveLength(0);
  });

  it('should update streaming message in query cache', async () => {
    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    // Add a user message first
    await act(async () => {
      await result.current.sendMessage('Hello');
    });

    // Simulate streaming chunk
    const chunk: StreamingChunk = {
      content: 'Hello back!',
      done: false,
      message_id: 'test-message-id',
      metadata: { author: 'test-agent' }
    };

    const streamingOptions = mockUseADKStreaming.mock.calls[0][0];
    
    act(() => {
      streamingOptions.onChunk?.(chunk);
    });

    // Should have user message and streaming assistant message
    expect(result.current.messages).toHaveLength(2);
    expect(result.current.messages[1]).toMatchObject({
      id: 'test-message-id',
      content: 'Hello back!',
      role: 'model'
    });
  });

  it('should handle function calls in streaming chunks', async () => {
    const { result } = renderHook(() => useADKChat(defaultOptions), {
      wrapper: createWrapper()
    });

    const chunk: StreamingChunk = {
      content: 'Using search tool...',
      done: false,
      message_id: 'test-message-id',
      metadata: {
        author: 'test-agent',
        function_calls: [
          {
            name: 'search',
            arguments: { query: 'test query' }
          }
        ]
      }
    };

    const streamingOptions = mockUseADKStreaming.mock.calls[0][0];
    
    act(() => {
      streamingOptions.onChunk?.(chunk);
    });

    expect(result.current.streamingMessage).toMatchObject({
      content: 'Using search tool...',
      function_calls: [
        {
          name: 'search',
          arguments: { query: 'test query' }
        }
      ]
    });
  });
});