apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: social-media-backend
  labels:
    app: social-media-manager
    component: backend
    env: production
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/cpu-throttling: "false"
spec:
  template:
    metadata:
      labels:
        app: social-media-manager
        component: backend
        env: production
      annotations:
        # Autoscaling
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "100"
        
        # Performance
        run.googleapis.com/cpu: "2"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/startup-cpu-boost: "true"
        
        # Timeout
        run.googleapis.com/timeout: "300s"
        
        # VPC Connector (if needed)
        # run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/CONNECTOR_NAME
        # run.googleapis.com/vpc-access-egress: private-ranges-only
        
        # Service Account
        run.googleapis.com/service-account: social-media-backend@PROJECT_ID.iam.gserviceaccount.com
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      containers:
      - name: backend
        image: gcr.io/PROJECT_ID/social-media-backend:latest
        ports:
        - name: http1
          containerPort: 8000
        env:
        # Application Configuration
        - name: ENV
          value: "production"
        - name: DEBUG
          value: "false"
        - name: PORT
          value: "8000"
        - name: HOST
          value: "0.0.0.0"
        
        # Google Cloud Configuration
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/etc/service-account/key.json"
        
        # Database Configuration
        - name: FIRESTORE_PROJECT_ID
          value: "PROJECT_ID"
        - name: BIGQUERY_PROJECT_ID
          value: "PROJECT_ID"
        - name: BIGQUERY_DATASET_ID
          value: "social_media_analytics"
        
        # API Configuration
        - name: FRONTEND_URL
          value: "https://your-frontend-domain.com"
        - name: BACKEND_URL
          value: "https://your-backend-domain.com"
        
        # Secret Manager Configuration
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: secret_key
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: oauth-secrets
              key: google_client_id
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: oauth-secrets
              key: google_client_secret
        - name: YOUTUBE_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: youtube_api_key
        - name: INSTAGRAM_APP_ID
          valueFrom:
            secretKeyRef:
              name: oauth-secrets
              key: instagram_app_id
        - name: INSTAGRAM_APP_SECRET
          valueFrom:
            secretKeyRef:
              name: oauth-secrets
              key: instagram_app_secret
        - name: GOOGLE_SEARCH_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: google_search_api_key
        - name: GOOGLE_SEARCH_ENGINE_ID
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: google_search_engine_id
        
        # Resource Configuration
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
          requests:
            cpu: "1"
            memory: "2Gi"
        
        # Health Checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
        
        # Volume Mounts (if using service account key file)
        volumeMounts:
        - name: service-account
          mountPath: /etc/service-account
          readOnly: true
      
      # Volumes (if using service account key file)
      volumes:
      - name: service-account
        secret:
          secretName: service-account-key
          defaultMode: 0400
  
  traffic:
  - percent: 100
    latestRevision: true