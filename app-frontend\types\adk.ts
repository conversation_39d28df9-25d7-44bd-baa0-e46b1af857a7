/**
 * ADK Integration Type Definitions
 * 
 * TypeScript interfaces for ADK integration with the frontend chat interface.
 * These types correspond to the backend Pydantic models and ADK API structures.
 * 
 * Requirements covered: 1.2, 10.1, 10.3
 */

// Core ADK Types
export type MessageRole = 'user' | 'model' | 'system';

export interface ADKContentPart {
  text?: string;
  function_call?: Record<string, any>;
  function_response?: Record<string, any>;
  inline_data?: Record<string, any>; // For binary data (audio, images)
}

export interface ADKEventContent {
  role: MessageRole;
  parts: ADKContentPart[];
}

export interface ADKEvent {
  author?: string;
  invocation_id?: string;
  content?: ADKEventContent;
  interrupted: boolean;
  turn_complete: boolean;
  long_running_tool_ids?: string[];
  metadata?: Record<string, any>;
}

export interface ADKMessage {
  role: MessageRole;
  parts: ADKContentPart[];
}

export interface ADKRunAgentRequest {
  app_name: string;
  user_id: string;
  session_id: string;
  new_message: ADKMessage;
  streaming?: boolean;
  state_delta?: Record<string, any>;
}

// Agent and Session Management
export interface ADKAgentInfo {
  name: string;
  description?: string;
  available: boolean;
  capabilities?: string[];
  tools?: string[];
}

export interface ADKSessionInfo {
  id: string;
  app_name: string;
  user_id: string;
  created_at?: string;
  last_activity?: string;
  event_count: number;
  metadata?: Record<string, any>;
}

export interface ADKSessionCreateRequest {
  user_id: string;
  app_name: string;
  initial_state?: Record<string, any>;
}

export interface ADKSessionCreateResponse {
  id: string;
  app_name: string;
  user_id: string;
  created_at: string;
}

// Error Handling
export interface ADKErrorResponse {
  error: string;
  error_code?: string;
  details?: Record<string, any>;
  invocation_id?: string;
}

export interface ADKHealthCheck {
  status: string;
  agents_loaded: number;
  available_agents: string[];
  server_version?: string;
  uptime_seconds?: number;
}

// Frontend-Specific Types
export interface StreamingChunk {
  content: string;
  done: boolean;
  message_id: string;
  metadata?: {
    author?: string;
    function_calls?: Record<string, any>[];
    interrupted?: boolean;
    long_running_tools?: string[];
    [key: string]: any;
  };
}

export type ChatSessionType = 'legacy' | 'adk';

export interface ChatSession {
  id: string;
  user_id: string;
  session_type: ChatSessionType;
  agent_name?: string;
  adk_session_id?: string;
  created_at: string;
  last_activity: string;
  message_count: number;
  metadata?: Record<string, any>;
}

export interface EnhancedChatMessage {
  id: string;
  session_id: string;
  role: MessageRole;
  content: string;
  user_id: string;
  timestamp: string;
  agent_name?: string;
  adk_invocation_id?: string;
  function_calls?: Record<string, any>[];
  interrupted?: boolean;
  metadata?: Record<string, any>;
  processing_time_ms?: number;
}

// API Request/Response Types
export interface ChatMessageRequest {
  message: string;
  session_id?: string;
  user_id: string;
  agent_name?: string;
  use_adk?: boolean;
  metadata?: Record<string, any>;
}

export interface ChatMessageResponse {
  message_id: string;
  session_id: string;
  streaming_url?: string;
  estimated_response_time?: number;
}

export interface StreamingResponse {
  chunk: StreamingChunk;
  session_id: string;
  message_id: string;
}

export interface ChatHistoryRequest {
  session_id: string;
  user_id: string;
  limit?: number;
  offset?: number;
  include_metadata?: boolean;
}

export interface ChatHistoryResponse {
  messages: EnhancedChatMessage[];
  total_count: number;
  session_info: ChatSession;
  has_more: boolean;
}

export interface AgentSelectionRequest {
  user_id: string;
  agent_name: string;
  session_id?: string;
  preserve_history?: boolean;
}

export interface AgentSelectionResponse {
  session_id: string;
  agent_name: string;
  agent_info?: Record<string, any>;
  history_preserved: boolean;
}

export interface ChatError {
  error: string;
  error_code: string;
  session_id?: string;
  message_id?: string;
  details?: Record<string, any>;
  retry_after?: number;
}

export interface ChatStatus {
  adk_enabled: boolean;
  available_agents: string[];
  active_sessions: number;
  system_health: string;
  features: Record<string, boolean>;
}

// Event Source and Streaming Types
export interface SSEEventData {
  data: string;
  event?: string;
  id?: string;
  retry?: number;
}

export interface StreamingConnection {
  eventSource: EventSource;
  sessionId: string;
  messageId: string;
  onChunk: (chunk: StreamingChunk) => void;
  onError: (error: ChatError) => void;
  onComplete: () => void;
}

// Utility Types
export interface ADKIntegrationConfig {
  enabled: boolean;
  baseUrl: string;
  defaultAgent: string;
  streamingEnabled: boolean;
  functionCallsEnabled: boolean;
  debugMode: boolean;
}

export interface FunctionCall {
  name: string;
  arguments: Record<string, any>;
  id?: string;
}

export interface FunctionResponse {
  name: string;
  content: string;
  id?: string;
  success: boolean;
  error?: string;
}

export interface ToolExecution {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  input?: Record<string, any>;
  output?: any;
  error?: string;
  duration_ms?: number;
}

// Component Props Types
export interface ADKChatProps {
  userId: string;
  sessionId?: string;
  agentName?: string;
  onMessageSent?: (message: EnhancedChatMessage) => void;
  onMessageReceived?: (message: EnhancedChatMessage) => void;
  onError?: (error: ChatError) => void;
  onAgentChange?: (agentName: string) => void;
  className?: string;
}

export interface AgentSelectorProps {
  availableAgents: ADKAgentInfo[];
  selectedAgent?: string;
  onAgentSelect: (agentName: string) => void;
  disabled?: boolean;
  className?: string;
}

export interface StreamingMessageProps {
  message: EnhancedChatMessage;
  isStreaming: boolean;
  onInterrupt?: () => void;
  showFunctionCalls?: boolean;
  className?: string;
}

export interface FunctionCallDisplayProps {
  functionCalls: Record<string, any>[];
  toolExecutions?: ToolExecution[];
  expanded?: boolean;
  onToggleExpanded?: () => void;
  className?: string;
}

// Hook Types
export interface UseADKChatOptions {
  userId: string;
  sessionId?: string;
  agentName?: string;
  autoConnect?: boolean;
  onError?: (error: ChatError) => void;
}

export interface UseADKChatReturn {
  messages: EnhancedChatMessage[];
  isLoading: boolean;
  isStreaming: boolean;
  error: ChatError | null;
  sessionId: string | null;
  currentAgent: string | null;
  sendMessage: (message: string) => Promise<void>;
  selectAgent: (agentName: string) => Promise<void>;
  clearHistory: () => void;
  interruptStreaming: () => void;
  reconnect: () => Promise<void>;
}

export interface UseAgentListReturn {
  agents: ADKAgentInfo[];
  isLoading: boolean;
  error: ChatError | null;
  refresh: () => Promise<void>;
}

export interface UseStreamingReturn {
  isConnected: boolean;
  isStreaming: boolean;
  currentChunk: StreamingChunk | null;
  error: ChatError | null;
  connect: (sessionId: string, messageId: string) => void;
  disconnect: () => void;
  interrupt: () => void;
}

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface ADKValidators {
  validateMessage: (message: string) => ValidationResult;
  validateSessionId: (sessionId: string) => ValidationResult;
  validateUserId: (userId: string) => ValidationResult;
  validateAgentName: (agentName: string) => ValidationResult;
}

// Constants
export const ADK_MESSAGE_ROLES = ['user', 'model', 'system'] as const;
export const CHAT_SESSION_TYPES = ['legacy', 'adk'] as const;
export const TOOL_EXECUTION_STATUSES = ['pending', 'running', 'completed', 'failed'] as const;

// Type Guards
export const isADKEvent = (obj: any): obj is ADKEvent => {
  return obj && typeof obj === 'object' && 
         typeof obj.interrupted === 'boolean' && 
         typeof obj.turn_complete === 'boolean';
};

export const isStreamingChunk = (obj: any): obj is StreamingChunk => {
  return obj && typeof obj === 'object' && 
         typeof obj.content === 'string' && 
         typeof obj.done === 'boolean' && 
         typeof obj.message_id === 'string';
};

export const isChatError = (obj: any): obj is ChatError => {
  return obj && typeof obj === 'object' && 
         typeof obj.error === 'string' && 
         typeof obj.error_code === 'string';
};

export const isEnhancedChatMessage = (obj: any): obj is EnhancedChatMessage => {
  return obj && typeof obj === 'object' && 
         typeof obj.id === 'string' && 
         typeof obj.content === 'string' && 
         typeof obj.role === 'string' && 
         ADK_MESSAGE_ROLES.includes(obj.role);
};