"""
Validation Schemas for ADK API Communication

This module contains validation schemas and utilities for ADK API communication,
ensuring data integrity and proper format validation for all ADK-related requests
and responses.

Requirements covered: 1.2, 10.1, 10.3
"""

from pydantic import BaseModel, Field, validator, model_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import re
import json
from enum import Enum

from .adk_models import (
    ADKRunAgentRequest, 
    ADKEvent, 
    ADKAgentInfo, 
    ADKSessionInfo,
    MessageRole,
    ADKContentPart,
    ADKMessage
)


class ValidationError(Exception):
    """Custom validation error for ADK operations"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        self.message = message
        self.field = field
        self.value = value
        super().__init__(message)


class ValidationSeverity(str, Enum):
    """Validation error severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationResult(BaseModel):
    """Result of validation operation"""
    is_valid: bool = Field(..., description="Whether validation passed")
    errors: List[str] = Field(default_factory=list, description="Validation error messages")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    field_errors: Dict[str, List[str]] = Field(default_factory=dict, description="Field-specific errors")


class ADKRequestValidator:
    """Validator for ADK API requests"""
    
    # Validation patterns
    AGENT_NAME_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
    USER_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
    SESSION_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
    
    # Limits
    MAX_MESSAGE_LENGTH = 10000
    MAX_AGENT_NAME_LENGTH = 100
    MAX_USER_ID_LENGTH = 100
    MAX_SESSION_ID_LENGTH = 100
    MAX_CONTENT_PARTS = 50
    
    @classmethod
    def validate_run_agent_request(cls, request: ADKRunAgentRequest) -> ValidationResult:
        """Validate ADK RunAgentRequest"""
        result = ValidationResult(is_valid=True)
        
        # Validate app_name
        app_name_validation = cls._validate_agent_name(request.app_name)
        if not app_name_validation.is_valid:
            result.is_valid = False
            result.errors.extend(app_name_validation.errors)
            result.field_errors['app_name'] = app_name_validation.errors
        
        # Validate user_id
        user_id_validation = cls._validate_user_id(request.user_id)
        if not user_id_validation.is_valid:
            result.is_valid = False
            result.errors.extend(user_id_validation.errors)
            result.field_errors['user_id'] = user_id_validation.errors
        
        # Validate session_id
        session_id_validation = cls._validate_session_id(request.session_id)
        if not session_id_validation.is_valid:
            result.is_valid = False
            result.errors.extend(session_id_validation.errors)
            result.field_errors['session_id'] = session_id_validation.errors
        
        # Validate new_message
        message_validation = cls._validate_adk_message(request.new_message)
        if not message_validation.is_valid:
            result.is_valid = False
            result.errors.extend(message_validation.errors)
            result.field_errors['new_message'] = message_validation.errors
        
        # Validate state_delta if present
        if request.state_delta is not None:
            state_validation = cls._validate_state_delta(request.state_delta)
            if not state_validation.is_valid:
                result.warnings.extend(state_validation.errors)
        
        return result
    
    @classmethod
    def _validate_agent_name(cls, agent_name: str) -> ValidationResult:
        """Validate agent name format"""
        result = ValidationResult(is_valid=True)
        
        if not agent_name:
            result.is_valid = False
            result.errors.append("Agent name cannot be empty")
            return result
        
        if not isinstance(agent_name, str):
            result.is_valid = False
            result.errors.append("Agent name must be a string")
            return result
        
        if len(agent_name) > cls.MAX_AGENT_NAME_LENGTH:
            result.is_valid = False
            result.errors.append(f"Agent name too long (max {cls.MAX_AGENT_NAME_LENGTH} characters)")
        
        if not cls.AGENT_NAME_PATTERN.match(agent_name):
            result.is_valid = False
            result.errors.append("Agent name can only contain letters, numbers, underscores, and hyphens")
        
        return result
    
    @classmethod
    def _validate_user_id(cls, user_id: str) -> ValidationResult:
        """Validate user ID format"""
        result = ValidationResult(is_valid=True)
        
        if not user_id:
            result.is_valid = False
            result.errors.append("User ID cannot be empty")
            return result
        
        if not isinstance(user_id, str):
            result.is_valid = False
            result.errors.append("User ID must be a string")
            return result
        
        if len(user_id) > cls.MAX_USER_ID_LENGTH:
            result.is_valid = False
            result.errors.append(f"User ID too long (max {cls.MAX_USER_ID_LENGTH} characters)")
        
        if not cls.USER_ID_PATTERN.match(user_id):
            result.is_valid = False
            result.errors.append("User ID can only contain letters, numbers, underscores, and hyphens")
        
        return result
    
    @classmethod
    def _validate_session_id(cls, session_id: str) -> ValidationResult:
        """Validate session ID format"""
        result = ValidationResult(is_valid=True)
        
        if not session_id:
            result.is_valid = False
            result.errors.append("Session ID cannot be empty")
            return result
        
        if not isinstance(session_id, str):
            result.is_valid = False
            result.errors.append("Session ID must be a string")
            return result
        
        if len(session_id) > cls.MAX_SESSION_ID_LENGTH:
            result.is_valid = False
            result.errors.append(f"Session ID too long (max {cls.MAX_SESSION_ID_LENGTH} characters)")
        
        if not cls.SESSION_ID_PATTERN.match(session_id):
            result.is_valid = False
            result.errors.append("Session ID can only contain letters, numbers, underscores, and hyphens")
        
        return result
    
    @classmethod
    def _validate_adk_message(cls, message: ADKMessage) -> ValidationResult:
        """Validate ADK message structure"""
        result = ValidationResult(is_valid=True)
        
        # Validate role
        if message.role not in [role.value for role in MessageRole]:
            result.is_valid = False
            result.errors.append(f"Invalid message role: {message.role}")
        
        # Validate parts
        if not message.parts:
            result.is_valid = False
            result.errors.append("Message must have at least one content part")
            return result
        
        if len(message.parts) > cls.MAX_CONTENT_PARTS:
            result.is_valid = False
            result.errors.append(f"Too many content parts (max {cls.MAX_CONTENT_PARTS})")
        
        # Validate each part
        for i, part in enumerate(message.parts):
            part_validation = cls._validate_content_part(part)
            if not part_validation.is_valid:
                result.is_valid = False
                for error in part_validation.errors:
                    result.errors.append(f"Part {i}: {error}")
        
        return result
    
    @classmethod
    def _validate_content_part(cls, part: ADKContentPart) -> ValidationResult:
        """Validate individual content part"""
        result = ValidationResult(is_valid=True)
        
        # Check that at least one field is present
        has_content = any([
            part.text is not None,
            part.function_call is not None,
            part.function_response is not None,
            part.inline_data is not None
        ])
        
        if not has_content:
            result.is_valid = False
            result.errors.append("Content part must have at least one field")
            return result
        
        # Validate text content
        if part.text is not None:
            if not isinstance(part.text, str):
                result.is_valid = False
                result.errors.append("Text content must be a string")
            elif len(part.text) > cls.MAX_MESSAGE_LENGTH:
                result.is_valid = False
                result.errors.append(f"Text content too long (max {cls.MAX_MESSAGE_LENGTH} characters)")
        
        # Validate function_call
        if part.function_call is not None:
            if not isinstance(part.function_call, dict):
                result.is_valid = False
                result.errors.append("Function call must be a dictionary")
            else:
                func_validation = cls._validate_function_call(part.function_call)
                if not func_validation.is_valid:
                    result.errors.extend(func_validation.errors)
        
        # Validate function_response
        if part.function_response is not None:
            if not isinstance(part.function_response, dict):
                result.is_valid = False
                result.errors.append("Function response must be a dictionary")
        
        # Validate inline_data
        if part.inline_data is not None:
            if not isinstance(part.inline_data, dict):
                result.is_valid = False
                result.errors.append("Inline data must be a dictionary")
        
        return result
    
    @classmethod
    def _validate_function_call(cls, function_call: Dict[str, Any]) -> ValidationResult:
        """Validate function call structure"""
        result = ValidationResult(is_valid=True)
        
        # Check required fields
        if 'name' not in function_call:
            result.is_valid = False
            result.errors.append("Function call must have 'name' field")
        elif not isinstance(function_call['name'], str):
            result.is_valid = False
            result.errors.append("Function name must be a string")
        elif not function_call['name'].strip():
            result.is_valid = False
            result.errors.append("Function name cannot be empty")
        
        # Validate arguments if present
        if 'arguments' in function_call:
            if not isinstance(function_call['arguments'], dict):
                result.is_valid = False
                result.errors.append("Function arguments must be a dictionary")
        
        return result
    
    @classmethod
    def _validate_state_delta(cls, state_delta: Dict[str, Any]) -> ValidationResult:
        """Validate state delta structure"""
        result = ValidationResult(is_valid=True)
        
        if not isinstance(state_delta, dict):
            result.is_valid = False
            result.errors.append("State delta must be a dictionary")
            return result
        
        # Check for reasonable size
        try:
            json_str = json.dumps(state_delta)
            if len(json_str) > 10000:  # 10KB limit
                result.warnings.append("State delta is very large and may impact performance")
        except (TypeError, ValueError):
            result.errors.append("State delta contains non-serializable data")
            result.is_valid = False
        
        return result


class ADKResponseValidator:
    """Validator for ADK API responses"""
    
    @classmethod
    def validate_adk_event(cls, event: ADKEvent) -> ValidationResult:
        """Validate ADK Event structure"""
        result = ValidationResult(is_valid=True)
        
        # Validate invocation_id if present
        if event.invocation_id is not None:
            if not isinstance(event.invocation_id, str):
                result.is_valid = False
                result.errors.append("Invocation ID must be a string")
            elif not event.invocation_id.strip():
                result.warnings.append("Invocation ID is empty")
        
        # Validate author if present
        if event.author is not None:
            if not isinstance(event.author, str):
                result.is_valid = False
                result.errors.append("Author must be a string")
        
        # Validate content if present
        if event.content is not None:
            content_validation = cls._validate_event_content(event.content)
            if not content_validation.is_valid:
                result.is_valid = False
                result.errors.extend(content_validation.errors)
        
        # Validate boolean fields
        if not isinstance(event.interrupted, bool):
            result.is_valid = False
            result.errors.append("Interrupted field must be boolean")
        
        if not isinstance(event.turn_complete, bool):
            result.is_valid = False
            result.errors.append("Turn complete field must be boolean")
        
        # Validate long_running_tool_ids if present
        if event.long_running_tool_ids is not None:
            if not isinstance(event.long_running_tool_ids, list):
                result.is_valid = False
                result.errors.append("Long running tool IDs must be a list")
            else:
                for tool_id in event.long_running_tool_ids:
                    if not isinstance(tool_id, str):
                        result.is_valid = False
                        result.errors.append("Tool IDs must be strings")
                        break
        
        return result
    
    @classmethod
    def _validate_event_content(cls, content) -> ValidationResult:
        """Validate event content structure"""
        result = ValidationResult(is_valid=True)
        
        # Validate role
        if not hasattr(content, 'role') or content.role not in [role.value for role in MessageRole]:
            result.is_valid = False
            result.errors.append(f"Invalid content role: {getattr(content, 'role', 'missing')}")
        
        # Validate parts
        if not hasattr(content, 'parts') or not content.parts:
            result.is_valid = False
            result.errors.append("Content must have parts")
        elif not isinstance(content.parts, list):
            result.is_valid = False
            result.errors.append("Content parts must be a list")
        
        return result


class ADKSessionValidator:
    """Validator for ADK session operations"""
    
    @classmethod
    def validate_session_create_request(cls, user_id: str, app_name: str) -> ValidationResult:
        """Validate session creation request"""
        result = ValidationResult(is_valid=True)
        
        # Validate user_id
        user_validation = ADKRequestValidator._validate_user_id(user_id)
        if not user_validation.is_valid:
            result.is_valid = False
            result.errors.extend(user_validation.errors)
            result.field_errors['user_id'] = user_validation.errors
        
        # Validate app_name
        app_validation = ADKRequestValidator._validate_agent_name(app_name)
        if not app_validation.is_valid:
            result.is_valid = False
            result.errors.extend(app_validation.errors)
            result.field_errors['app_name'] = app_validation.errors
        
        return result
    
    @classmethod
    def validate_session_info(cls, session_info: ADKSessionInfo) -> ValidationResult:
        """Validate session info structure"""
        result = ValidationResult(is_valid=True)
        
        # Validate required fields
        if not session_info.id:
            result.is_valid = False
            result.errors.append("Session ID is required")
        
        if not session_info.user_id:
            result.is_valid = False
            result.errors.append("User ID is required")
        
        if not session_info.app_name:
            result.is_valid = False
            result.errors.append("App name is required")
        
        # Validate event count
        if session_info.event_count < 0:
            result.is_valid = False
            result.errors.append("Event count cannot be negative")
        
        return result


# Utility functions for validation
def validate_json_serializable(data: Any) -> ValidationResult:
    """Validate that data is JSON serializable"""
    result = ValidationResult(is_valid=True)
    
    try:
        json.dumps(data)
    except (TypeError, ValueError) as e:
        result.is_valid = False
        result.errors.append(f"Data is not JSON serializable: {str(e)}")
    
    return result


def validate_content_safety(text: str) -> ValidationResult:
    """Basic content safety validation"""
    result = ValidationResult(is_valid=True)
    
    if not isinstance(text, str):
        result.is_valid = False
        result.errors.append("Content must be a string")
        return result
    
    # Check for extremely long content
    if len(text) > 50000:  # 50KB limit
        result.warnings.append("Content is very long and may impact performance")
    
    # Check for potential security issues (basic checks)
    suspicious_patterns = [
        r'<script[^>]*>',
        r'javascript:',
        r'data:text/html',
        r'vbscript:',
    ]
    
    for pattern in suspicious_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            result.warnings.append("Content contains potentially unsafe patterns")
            break
    
    return result


# Validation decorators
def validate_adk_request(func):
    """Decorator to validate ADK requests"""
    def wrapper(*args, **kwargs):
        # Extract request from arguments
        request = None
        for arg in args:
            if isinstance(arg, ADKRunAgentRequest):
                request = arg
                break
        
        if request:
            validation_result = ADKRequestValidator.validate_run_agent_request(request)
            if not validation_result.is_valid:
                raise ValidationError(
                    f"Invalid ADK request: {', '.join(validation_result.errors)}"
                )
        
        return func(*args, **kwargs)
    return wrapper


def validate_adk_response(func):
    """Decorator to validate ADK responses"""
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        
        if isinstance(result, ADKEvent):
            validation_result = ADKResponseValidator.validate_adk_event(result)
            if not validation_result.is_valid:
                raise ValidationError(
                    f"Invalid ADK response: {', '.join(validation_result.errors)}"
                )
        
        return result
    return wrapper