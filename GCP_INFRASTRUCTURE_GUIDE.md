# Google Cloud Platform Infrastructure Setup Guide

This guide provides comprehensive instructions for setting up the complete Google Cloud Platform infrastructure for the Social Media Manager application.

## 📋 Overview

The Social Media Manager application requires the following GCP services:

- **Cloud Run**: Containerized application hosting
- **Firestore**: NoSQL database for application data
- **BigQuery**: Analytics data warehouse
- **Secret Manager**: Secure storage for API keys and secrets
- **Cloud Build**: CI/CD and container building
- **Container Registry**: Docker image storage
- **Cloud Logging**: Application logs and monitoring
- **Cloud Monitoring**: Performance metrics and alerting

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

**Linux/macOS:**
```bash
cd scripts
chmod +x setup_gcp.sh
./setup_gcp.sh
```

**Windows:**
```powershell
cd scripts
.\setup_gcp.ps1
```

### Option 2: Manual Setup

Follow the detailed manual setup instructions below.

## 📋 Prerequisites

### 1. Google Cloud Account
- Active Google Cloud account with billing enabled
- Owner or Editor role on the target project

### 2. Required Tools
- [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
- [Docker](https://docs.docker.com/get-docker/) (for deployment)
- [Python 3.11+](https://www.python.org/downloads/) (for BigQuery setup)

### 3. Authentication
```bash
# Authenticate with Google Cloud
gcloud auth login

# Set application default credentials
gcloud auth application-default login
```

## 🏗️ Manual Infrastructure Setup

### Step 1: Project Configuration

```bash
# Set your project ID
export PROJECT_ID="your-project-id"
gcloud config set project $PROJECT_ID

# Set default region and zone
gcloud config set compute/region us-central1
gcloud config set compute/zone us-central1-a
```

### Step 2: Enable Required APIs

```bash
# Core services
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable artifactregistry.googleapis.com

# Data services
gcloud services enable secretmanager.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable bigquery.googleapis.com

# Monitoring services
gcloud services enable logging.googleapis.com
gcloud services enable monitoring.googleapis.com
gcloud services enable cloudtrace.googleapis.com

# Authentication services
gcloud services enable iam.googleapis.com
gcloud services enable oauth2.googleapis.com

# Platform APIs
gcloud services enable youtube.googleapis.com
```

### Step 3: Create Service Accounts

#### Backend Service Account
```bash
# Create service account
gcloud iam service-accounts create social-media-backend \
    --display-name="Social Media Backend Service" \
    --description="Service account for Social Media Manager backend"

# Assign roles
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/datastore.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/bigquery.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/monitoring.metricWriter"
```

#### Analytics Service Account
```bash
# Create analytics service account
gcloud iam service-accounts create social-media-analytics \
    --display-name="Social Media Analytics Service" \
    --description="Service account for analytics and data processing"

# Assign roles
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-analytics@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/bigquery.admin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-analytics@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/datastore.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-analytics@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"
```

### Step 4: Setup Firestore Database

```bash
# Create Firestore database
gcloud firestore databases create \
    --database="(default)" \
    --location=us-central1

# Verify creation
gcloud firestore databases describe --database="(default)"
```

### Step 5: Setup BigQuery Dataset

```bash
# Create dataset
bq mk \
    --dataset \
    --location=us-central1 \
    --description="Social Media Manager Analytics Data Warehouse" \
    --label=app:social-media-manager \
    --label=env:production \
    ${PROJECT_ID}:social_media_analytics

# Verify creation
bq ls -d --project_id=$PROJECT_ID
```

### Step 6: Setup Secret Manager

Create placeholder secrets (update with actual values later):

```bash
# Application secrets
echo "your-secret-key" | gcloud secrets create app-secret-key --data-file=-
echo "your-google-client-id" | gcloud secrets create google-client-id --data-file=-
echo "your-google-client-secret" | gcloud secrets create google-client-secret --data-file=-

# API keys
echo "your-youtube-api-key" | gcloud secrets create youtube-api-key --data-file=-
echo "your-instagram-app-id" | gcloud secrets create instagram-app-id --data-file=-
echo "your-instagram-app-secret" | gcloud secrets create instagram-app-secret --data-file=-

# Google Search API
echo "your-google-search-api-key" | gcloud secrets create google-search-api-key --data-file=-
echo "your-google-search-engine-id" | gcloud secrets create google-search-engine-id --data-file=-
```

### Step 7: Configure Cloud Build

```bash
# Get project number
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")

# Grant Cloud Run Admin role to Cloud Build
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:${PROJECT_NUMBER}@cloudbuild.gserviceaccount.com" \
    --role="roles/run.admin"

# Grant Service Account User role
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:${PROJECT_NUMBER}@cloudbuild.gserviceaccount.com" \
    --role="roles/iam.serviceAccountUser"
```

### Step 8: Configure Container Registry

```bash
# Configure Docker authentication
gcloud auth configure-docker
```

### Step 9: Setup Monitoring and Logging

```bash
# Create log sink for application logs
gcloud logging sinks create social-media-app-logs \
    bigquery.googleapis.com/projects/${PROJECT_ID}/datasets/social_media_analytics \
    --log-filter='resource.type="cloud_run_revision" AND labels."service-name"=("social-media-backend" OR "social-media-frontend")'
```

## 🏗️ Initialize BigQuery Tables

After the infrastructure setup, initialize the BigQuery analytics tables:

```bash
cd scripts
python setup_bigquery.py
```

This will create all the necessary tables for analytics data storage.

## 🔑 API Keys and OAuth Setup

### 1. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
2. Create OAuth 2.0 Client ID for web application
3. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://your-domain.com/api/auth/callback/google` (production)
4. Update secrets:
   ```bash
   echo "your-actual-client-id" | gcloud secrets versions add google-client-id --data-file=-
   echo "your-actual-client-secret" | gcloud secrets versions add google-client-secret --data-file=-
   ```

### 2. YouTube Data API

1. Enable YouTube Data API v3 in Google Cloud Console
2. Create API key with YouTube Data API restrictions
3. Update secret:
   ```bash
   echo "your-youtube-api-key" | gcloud secrets versions add youtube-api-key --data-file=-
   ```

### 3. Instagram Graph API

1. Create Facebook App at [Facebook Developers](https://developers.facebook.com)
2. Add Instagram Graph API product
3. Configure Instagram Display API
4. Update secrets:
   ```bash
   echo "your-instagram-app-id" | gcloud secrets versions add instagram-app-id --data-file=-
   echo "your-instagram-app-secret" | gcloud secrets versions add instagram-app-secret --data-file=-
   ```

### 4. Google Search API (Optional)

1. Enable Custom Search API in Google Cloud Console
2. Create Custom Search Engine at [Google CSE](https://cse.google.com)
3. Update secrets:
   ```bash
   echo "your-search-api-key" | gcloud secrets versions add google-search-api-key --data-file=-
   echo "your-search-engine-id" | gcloud secrets versions add google-search-engine-id --data-file=-
   ```

## 🚀 Deploy Applications

After infrastructure setup, deploy the applications:

```bash
cd deployment
./deploy.sh  # Linux/macOS
# or
.\deploy.ps1  # Windows
```

## 📊 Monitoring and Alerting

### Cloud Monitoring Setup

1. Go to [Cloud Monitoring](https://console.cloud.google.com/monitoring)
2. Create alerting policies for:
   - Cloud Run service errors
   - High response latency
   - Memory usage
   - Secret Manager access failures

### Log Analysis

Key log queries for troubleshooting:

```sql
-- Application errors
resource.type="cloud_run_revision"
labels."service-name"="social-media-backend"
severity>=ERROR

-- Authentication failures
resource.type="cloud_run_revision"
jsonPayload.message:"authentication failed"

-- API rate limiting
resource.type="cloud_run_revision"
jsonPayload.message:"rate limit"
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Permission Denied Errors
```bash
# Check service account permissions
gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --filter="bindings.members:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com"
```

#### 2. Firestore Access Issues
```bash
# Verify Firestore database exists
gcloud firestore databases describe --database="(default)"

# Test Firestore access
gcloud firestore documents create users/test --data='{"test": "value"}'
gcloud firestore documents delete users/test
```

#### 3. BigQuery Connection Issues
```bash
# Test BigQuery access
bq query --use_legacy_sql=false 'SELECT 1 as test'

# Check dataset permissions
bq show ${PROJECT_ID}:social_media_analytics
```

#### 4. Secret Manager Access Issues
```bash
# Test secret access
gcloud secrets versions access latest --secret="app-secret-key"

# Check secret permissions
gcloud secrets get-iam-policy app-secret-key
```

## 🔄 Maintenance

### Regular Tasks

1. **Update secrets** when API keys rotate
2. **Monitor costs** and adjust quotas
3. **Review logs** for errors and performance issues
4. **Update IAM policies** as needed
5. **Backup Firestore data** regularly

### Cost Optimization

1. **Cloud Run**: Use minimum instances and right-size memory
2. **BigQuery**: Partition tables and use slots efficiently
3. **Firestore**: Optimize queries and use caching
4. **Monitoring**: Set up billing alerts

## 📚 Additional Resources

- [Google Cloud Documentation](https://cloud.google.com/docs)
- [Cloud Run Best Practices](https://cloud.google.com/run/docs/best-practices)
- [Firestore Best Practices](https://cloud.google.com/firestore/docs/best-practices)
- [BigQuery Best Practices](https://cloud.google.com/bigquery/docs/best-practices-performance-overview)
- [Secret Manager Best Practices](https://cloud.google.com/secret-manager/docs/best-practices)

## 🆘 Support

For infrastructure issues:

1. Check the troubleshooting section above
2. Review Google Cloud Status at [status.cloud.google.com](https://status.cloud.google.com)
3. Check application logs in Cloud Logging
4. Use `gcloud` commands to verify service status

## 🔒 Security Considerations

1. **Principle of Least Privilege**: Service accounts have minimal required permissions
2. **Secret Rotation**: Regularly rotate API keys and secrets
3. **Network Security**: Use VPC for sensitive workloads
4. **Audit Logging**: Enable Cloud Audit Logs for compliance
5. **Access Control**: Use IAM conditions for fine-grained access