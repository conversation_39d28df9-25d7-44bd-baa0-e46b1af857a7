"""
Chat service for managing ADK agent interactions and session state.
"""
import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional, AsyncGenerator
from contextlib import asynccontextmanager

from google.adk.runners import InMemoryRunner
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.adk.sessions.in_memory_session_service import InMemorySessionService
from google.genai.types import Content, Part

from app.models.chat import ChatMessage, MessageRole, StreamingChunk, SessionInfo
from app.core.observability import observability


class ChatService:
    """Service for managing chat interactions with ADK agents."""
    
    def __init__(self):
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.session_service = InMemorySessionService()
        self._agent = None
        
    async def _get_agent(self):
        """Lazy load the agent to avoid import issues at startup."""
        if self._agent is None:
            try:
                # Import the main agent
                import sys
                import os
                agents_path = os.path.join(os.path.dirname(__file__), '../../agents')
                sys.path.insert(0, agents_path)
                
                from agent import root_agent
                self._agent = root_agent
                observability.logger.info("Successfully loaded ADK agent")
            except Exception as e:
                observability.logger.warning(f"Failed to load ADK agent: {e}")
                try:
                    # Try to create a fallback agent with ADK
                    from google.adk.agents import LlmAgent
                    self._agent = LlmAgent(
                        name="fallback_agent",
                        model="gemini-2.0-flash",
                        description="Fallback social media assistant",
                        instruction="You are a helpful social media assistant. Help users with content creation and analysis."
                    )
                    observability.logger.info("Created fallback ADK agent")
                except Exception as fallback_error:
                    observability.logger.warning(f"Failed to create fallback ADK agent: {fallback_error}")
                    # Create a mock agent for testing
                    class MockAgent:
                        def __init__(self):
                            self.name = "mock_agent"
                            self.model = "mock-model"
                            self.description = "Mock agent for testing"
                    
                    self._agent = MockAgent()
                    observability.logger.info("Created mock agent for testing")
        return self._agent
    
    async def create_session(self, user_id: str, session_id: Optional[str] = None) -> SessionInfo:
        """Create a new chat session."""
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        try:
            agent = await self._get_agent()
            
            # Create ADK runner
            runner = InMemoryRunner(
                app_name="social_media_manager",
                agent=agent,
                session_service=self.session_service
            )
            
            # Create ADK session
            session = await runner.session_service.create_session(
                app_name="social_media_manager",
                user_id=user_id
            )
            
            # Store session info
            session_info = SessionInfo(
                session_id=session_id,
                user_id=user_id,
                created_at=datetime.now(),
                last_activity=datetime.now(),
                message_count=0
            )
            
            self.active_sessions[session_id] = {
                "runner": runner,
                "session": session,
                "info": session_info,
                "messages": []
            }
            
            observability.logger.info(f"Created chat session {session_id} for user {user_id}")
            return session_info
            
        except Exception as e:
            observability.logger.error(f"Failed to create session: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[SessionInfo]:
        """Get session information."""
        session_data = self.active_sessions.get(session_id)
        if session_data:
            return session_data["info"]
        return None
    
    async def send_message_streaming(
        self, 
        session_id: str, 
        message: str, 
        user_id: str
    ) -> AsyncGenerator[StreamingChunk, None]:
        """Send a message and stream the response."""
        try:
            # Get or create session
            session_data = self.active_sessions.get(session_id)
            if not session_data:
                await self.create_session(user_id, session_id)
                session_data = self.active_sessions[session_id]
            
            runner = session_data["runner"]
            session = session_data["session"]
            
            # Create user message
            user_message = ChatMessage(
                id=str(uuid.uuid4()),
                role=MessageRole.USER,
                content=message,
                session_id=session_id,
                user_id=user_id
            )
            
            # Store user message
            session_data["messages"].append(user_message)
            session_data["info"].message_count += 1
            session_data["info"].last_activity = datetime.now()
            
            # Create ADK content
            content = Content(
                role="user",
                parts=[Part.from_text(text=message)]
            )
            
            # Configure streaming
            run_config = RunConfig(
                streaming_mode=StreamingMode.SSE,
                max_llm_calls=50
            )
            
            # Stream response
            assistant_message_id = str(uuid.uuid4())
            assistant_content = ""
            
            observability.logger.info(f"Starting streaming response for session {session_id}")
            
            async for event in runner.run_async(
                session=session,
                new_message=content,
                run_config=run_config
            ):
                try:
                    # Handle different event types
                    if hasattr(event, 'content') and event.content and event.content.parts:
                        part = event.content.parts[0]
                        if hasattr(part, 'text') and part.text:
                            chunk_text = part.text
                            assistant_content += chunk_text
                            
                            yield StreamingChunk(
                                content=chunk_text,
                                done=False,
                                message_id=assistant_message_id
                            )
                    
                    # Check if turn is complete
                    if hasattr(event, 'turn_complete') and event.turn_complete:
                        # Store assistant message
                        assistant_message = ChatMessage(
                            id=assistant_message_id,
                            role=MessageRole.ASSISTANT,
                            content=assistant_content,
                            session_id=session_id,
                            user_id=user_id
                        )
                        
                        session_data["messages"].append(assistant_message)
                        session_data["info"].message_count += 1
                        session_data["info"].last_activity = datetime.now()
                        
                        yield StreamingChunk(
                            content="",
                            done=True,
                            message_id=assistant_message_id
                        )
                        break
                        
                except Exception as e:
                    observability.logger.error(f"Error processing event: {e}")
                    continue
            
            observability.logger.info(f"Completed streaming response for session {session_id}")
            
        except Exception as e:
            observability.logger.error(f"Error in streaming chat: {e}")
            yield StreamingChunk(
                content=f"I apologize, but I encountered an error: {str(e)}",
                done=True,
                message_id=str(uuid.uuid4())
            )
    
    async def get_chat_history(self, session_id: str) -> List[ChatMessage]:
        """Get chat history for a session."""
        session_data = self.active_sessions.get(session_id)
        if session_data:
            return session_data["messages"]
        return []
    
    async def clear_chat_history(self, session_id: str) -> bool:
        """Clear chat history for a session."""
        try:
            session_data = self.active_sessions.get(session_id)
            if session_data:
                session_data["messages"] = []
                session_data["info"].message_count = 0
                observability.logger.info(f"Cleared chat history for session {session_id}")
                return True
            return False
        except Exception as e:
            observability.logger.error(f"Error clearing chat history: {e}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a chat session."""
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                observability.logger.info(f"Deleted session {session_id}")
                return True
            return False
        except Exception as e:
            observability.logger.error(f"Error deleting session: {e}")
            return False


# Global chat service instance
chat_service = ChatService()