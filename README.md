# Social Media Manager Agent

A production-ready web application that enables content creators to connect their YouTube and Instagram accounts and interact with an intelligent social media manager agent. The system provides comprehensive analytics, insights, and content planning through AI-powered analysis and grounded web research.

## 🚀 Features

- **Multi-Platform Analytics**: Connect YouTube and Instagram accounts for unified insights
- **AI-Powered Chat Interface**: Interact with your social media manager through natural conversation
- **Real-time Streaming Responses**: Get instant feedback and analysis
- **Content Planning**: Generate optimized content calendars and posting strategies
- **Trend Research**: Stay updated with market trends and competitor analysis
- **Performance Insights**: Deep analytics across all connected platforms

## 🏗️ Architecture

### Frontend Stack
- **Framework**: Next.js 14+ (App Router) with React 18+ and TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query (React Query) for server state
- **Forms**: React Hook Form with Zod validation

### Backend Stack
- **API Framework**: Python FastAPI with async/await support
- **Agent Framework**: Google ADK (Agent Development Kit) for Python
- **Authentication**: OAuth 2.0 flows for platform integrations

### Cloud Infrastructure
- **Compute**: Google Cloud Run (containerized deployment)
- **Database**: Firestore (NoSQL document store)
- **Analytics Warehouse**: BigQuery for historical data
- **Secrets**: Google Secret Manager for API keys and tokens
- **Monitoring**: Cloud Logging and Cloud Trace

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- Python 3.11+
- Docker and Docker Compose
- Google Cloud Project (for production deployment)

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd social-media-agents-2025-aug
   ```

2. **Environment Configuration**
   ```bash
   # Frontend
   cd app-frontend
   cp .env.example .env.local
   # Edit .env.local with your configuration
   
   # Backend
   cd ../app-agents
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Install Dependencies**
   ```bash
   # Frontend
   cd app-frontend
   npm install
   
   # Backend
   cd ../app-agents
   pip install -r requirements.txt
   ```

4. **Start Development Servers**
   
   **Option A: Using Docker Compose (Recommended)**
   ```bash
   docker-compose up --build
   ```
   
   **Option B: Manual Start**
   ```bash
   # Terminal 1 - Backend
   cd app-agents
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   
   # Terminal 2 - Frontend
   cd app-frontend
   npm run dev
   ```

5. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## 🔧 Configuration

### Required API Keys

Create accounts and obtain API keys for:

1. **Google Cloud Platform**
   - Enable Firestore, BigQuery, Secret Manager
   - Create service account with appropriate permissions

2. **YouTube Data API v3**
   - Enable YouTube Data API v3 in Google Cloud Console
   - Generate API key for public data access

3. **Instagram Graph API**
   - Create Facebook App for Instagram Business API
   - Configure OAuth redirect URIs

4. **Google Search API** (for Research Agent)
   - Create Custom Search Engine
   - Generate Search API key

### Environment Variables

See `.env.example` files in both `app-frontend` and `app-agents` directories for complete configuration options.

## 📱 Usage

### Connecting Accounts

1. **YouTube**: 
   - OAuth flow for full access
   - Read-only mode via channel URL/handle

2. **Instagram**: 
   - OAuth flow for Instagram Business accounts
   - Requires Business/Creator account type

### Chat Interface

Ask your AI social media manager questions like:
- "How is my YouTube performance this month?"
- "What content should I post next week?"
- "Analyze my Instagram engagement trends"
- "Create a content plan for my product launch"

### Content Planning

Generate detailed content calendars with:
- Optimized posting times
- Platform-specific content suggestions
- Trending hashtags and topics
- Performance predictions

## 🧪 Testing

### Backend Tests
```bash
cd app-agents
pytest tests/
```

### Frontend Tests
```bash
cd app-frontend
npm test
npm run test:e2e
```

## 🚀 Deployment

### Google Cloud Run Deployment

1. **Build and push Docker images**
   ```bash
   # Build images
   docker build -t gcr.io/YOUR_PROJECT/social-media-frontend ./app-frontend
   docker build -t gcr.io/YOUR_PROJECT/social-media-backend ./app-agents
   
   # Push to Container Registry
   docker push gcr.io/YOUR_PROJECT/social-media-frontend
   docker push gcr.io/YOUR_PROJECT/social-media-backend
   ```

2. **Deploy to Cloud Run**
   ```bash
   # Deploy backend
   gcloud run deploy social-media-backend \
     --image gcr.io/YOUR_PROJECT/social-media-backend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   
   # Deploy frontend
   gcloud run deploy social-media-frontend \
     --image gcr.io/YOUR_PROJECT/social-media-frontend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

### CI/CD Pipeline

GitHub Actions workflows are configured for:
- Automated testing on pull requests
- Building and deploying to staging/production
- Security scanning and dependency updates

## 📊 Monitoring

- **Application Logs**: Google Cloud Logging
- **Performance Monitoring**: Google Cloud Trace
- **Error Tracking**: Integrated error reporting
- **Health Checks**: Built-in health endpoints

## 🔒 Security

- OAuth 2.0 authentication flows
- Encrypted token storage in Secret Manager
- Rate limiting and request validation
- CORS configuration for secure API access
- Regular security dependency updates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Use GitHub Issues for bug reports and feature requests
- **Discussions**: Use GitHub Discussions for questions and community support

## 🗺️ Roadmap

### Current (MVP)
- ✅ YouTube and Instagram integration
- ✅ AI chat interface with streaming
- ✅ Content planning and analytics
- ✅ Docker deployment setup

### Phase 2
- 
- 🔄 TikTok integration
- 🔄 X (Twitter) integration
- 🔄 Advanced analytics dashboard
- 🔄 Team collaboration features

### Phase 3
- 📋 Multi-user workspace support
- 📋 Advanced AI content generation
- 📋 Automated posting capabilities
- 📋 Enterprise features and SSO

---

**Built with ❤️ for content creators and social media professionals**