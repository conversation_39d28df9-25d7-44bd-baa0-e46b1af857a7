"use client";

import { useState } from 'react';
import { FunctionCallDisplay } from '@/components/chat/function-call-display';
import { ToolExecutionDetail } from '@/components/chat/tool-execution-detail';
import { Button } from '@/components/ui/button';
import { FunctionCall, FunctionResponse, ToolExecution } from '@/types/adk';

export default function ToolExecutionDemo() {
  const [showDetail, setShowDetail] = useState(false);

  const mockFunctionCalls: FunctionCall[] = [
    {
      id: 'call_1',
      name: 'search_web',
      arguments: {
        query: 'test search',
        limit: 10
      }
    },
    {
      id: 'call_2',
      name: 'analyze_data',
      arguments: {
        data: [1, 2, 3, 4, 5]
      }
    }
  ];

  const mockFunctionResponses: FunctionResponse[] = [
    {
      id: 'call_1',
      name: 'search_web',
      content: 'Found 5 results for test search',
      success: true
    },
    {
      id: 'call_2',
      name: 'analyze_data',
      content: 'Analysis complete',
      success: false,
      error: 'Invalid data format'
    }
  ];

  const mockToolExecutions: ToolExecution[] = [
    {
      id: 'call_1',
      name: 'search_web',
      status: 'completed',
      input: { query: 'test search', limit: 10 },
      output: 'Found 5 results for test search',
      duration_ms: 1500
    },
    {
      id: 'call_2',
      name: 'analyze_data',
      status: 'failed',
      input: { data: [1, 2, 3, 4, 5] },
      error: 'Invalid data format',
      duration_ms: 500
    }
  ];

  return (
    <div className="container mx-auto p-8 space-y-8">
      <h1 className="text-3xl font-bold">Tool Execution Demo</h1>
      
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Function Call Display</h2>
        <div className="border rounded-lg p-4">
          <FunctionCallDisplay
            functionCalls={mockFunctionCalls}
            functionResponses={mockFunctionResponses}
            toolExecutions={mockToolExecutions}
            expanded={true}
            onCopy={(content, type) => {
              console.log('Copied:', type, content);
            }}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Tool Execution Detail</h2>
        <Button onClick={() => setShowDetail(true)}>
          Show Tool Execution Detail
        </Button>
        
        <ToolExecutionDetail
          execution={mockToolExecutions[0]}
          functionCall={mockFunctionCalls[0]}
          functionResponse={mockFunctionResponses[0]}
          isOpen={showDetail}
          onClose={() => setShowDetail(false)}
          onCopy={(content, type) => {
            console.log('Copied:', type, content);
          }}
          onExport={(execution) => {
            console.log('Export:', execution);
          }}
        />
      </div>
    </div>
  );
}