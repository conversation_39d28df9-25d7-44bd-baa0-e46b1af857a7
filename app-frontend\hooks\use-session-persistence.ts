"use client";

import { useState, useEffect, useCallback } from 'react';
import { EnhancedChatMessage, ADKSessionInfo, ChatError } from '@/types/adk';

export interface SessionStorageData {
  sessionId: string;
  userId: string;
  agentName: string;
  lastActivity: string;
  messageCount: number;
  metadata?: Record<string, any>;
}

export interface UseSessionPersistenceOptions {
  userId: string;
  agentName: string;
  storageType?: 'localStorage' | 'sessionStorage';
  autoSave?: boolean;
  onSessionRecovered?: (sessionData: SessionStorageData) => void;
  onSessionCleared?: () => void;
}

export interface UseSessionPersistenceReturn {
  sessionId: string | null;
  isRecovering: boolean;
  hasStoredSession: boolean;
  sessionData: SessionStorageData | null;
  saveSession: (sessionId: string, metadata?: Record<string, any>) => void;
  clearSession: () => void;
  recoverSession: () => Promise<SessionStorageData | null>;
  updateActivity: () => void;
  getStoredSessions: () => SessionStorageData[];
  removeStoredSession: (sessionId: string) => void;
  clearAllSessions: () => void;
}

const STORAGE_KEY_PREFIX = 'adk_session';
const STORAGE_SESSIONS_KEY = 'adk_sessions_list';
const MAX_STORED_SESSIONS = 10;
const SESSION_EXPIRY_HOURS = 24;

export function useSessionPersistence(
  options: UseSessionPersistenceOptions
): UseSessionPersistenceReturn {
  const {
    userId,
    agentName,
    storageType = 'localStorage',
    autoSave = true,
    onSessionRecovered,
    onSessionCleared
  } = options;

  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);
  const [sessionData, setSessionData] = useState<SessionStorageData | null>(null);

  const storage = typeof window !== 'undefined' 
    ? (storageType === 'localStorage' ? localStorage : sessionStorage)
    : null;

  const getStorageKey = useCallback((sessionId: string) => {
    return `${STORAGE_KEY_PREFIX}_${userId}_${agentName}_${sessionId}`;
  }, [userId, agentName]);

  const getSessionsListKey = useCallback(() => {
    return `${STORAGE_SESSIONS_KEY}_${userId}`;
  }, [userId]);

  const isSessionExpired = useCallback((lastActivity: string): boolean => {
    const lastActivityDate = new Date(lastActivity);
    const expiryDate = new Date(lastActivityDate.getTime() + (SESSION_EXPIRY_HOURS * 60 * 60 * 1000));
    return new Date() > expiryDate;
  }, []);

  const saveSession = useCallback((
    newSessionId: string, 
    metadata?: Record<string, any>
  ) => {
    if (!storage) return;

    const sessionData: SessionStorageData = {
      sessionId: newSessionId,
      userId,
      agentName,
      lastActivity: new Date().toISOString(),
      messageCount: 0,
      metadata
    };

    try {
      // Save individual session data
      const storageKey = getStorageKey(newSessionId);
      storage.setItem(storageKey, JSON.stringify(sessionData));

      // Update sessions list
      const sessionsListKey = getSessionsListKey();
      const existingSessions = getStoredSessions();
      
      // Remove existing session with same ID if it exists
      const filteredSessions = existingSessions.filter(s => s.sessionId !== newSessionId);
      
      // Add new session at the beginning
      const updatedSessions = [sessionData, ...filteredSessions];
      
      // Keep only the most recent sessions
      const trimmedSessions = updatedSessions.slice(0, MAX_STORED_SESSIONS);
      
      storage.setItem(sessionsListKey, JSON.stringify(trimmedSessions));

      setSessionId(newSessionId);
      setSessionData(sessionData);

      console.log(`Session ${newSessionId} saved to ${storageType}`);
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }, [storage, userId, agentName, getStorageKey, getSessionsListKey, storageType]);

  const clearSession = useCallback(() => {
    if (!storage || !sessionId) return;

    try {
      // Remove individual session data
      const storageKey = getStorageKey(sessionId);
      storage.removeItem(storageKey);

      // Update sessions list
      const sessionsListKey = getSessionsListKey();
      const existingSessions = getStoredSessions();
      const filteredSessions = existingSessions.filter(s => s.sessionId !== sessionId);
      storage.setItem(sessionsListKey, JSON.stringify(filteredSessions));

      setSessionId(null);
      setSessionData(null);
      onSessionCleared?.();

      console.log(`Session ${sessionId} cleared from ${storageType}`);
    } catch (error) {
      console.error('Failed to clear session:', error);
    }
  }, [storage, sessionId, getStorageKey, getSessionsListKey, onSessionCleared, storageType]);

  const recoverSession = useCallback(async (): Promise<SessionStorageData | null> => {
    if (!storage) return null;

    setIsRecovering(true);

    try {
      // Get the most recent session for this user/agent combination
      const storedSessions = getStoredSessions();
      const matchingSession = storedSessions.find(s => 
        s.userId === userId && 
        s.agentName === agentName &&
        !isSessionExpired(s.lastActivity)
      );

      if (matchingSession) {
        const storageKey = getStorageKey(matchingSession.sessionId);
        const storedData = storage.getItem(storageKey);
        
        if (storedData) {
          const parsedData: SessionStorageData = JSON.parse(storedData);
          
          // Check if session is not expired
          if (!isSessionExpired(parsedData.lastActivity)) {
            setSessionId(parsedData.sessionId);
            setSessionData(parsedData);
            onSessionRecovered?.(parsedData);
            
            console.log(`Session ${parsedData.sessionId} recovered from ${storageType}`);
            return parsedData;
          } else {
            // Remove expired session
            removeStoredSession(parsedData.sessionId);
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Failed to recover session:', error);
      return null;
    } finally {
      setIsRecovering(false);
    }
  }, [storage, userId, agentName, getStorageKey, isSessionExpired, onSessionRecovered, storageType]);

  const updateActivity = useCallback(() => {
    if (!storage || !sessionId || !sessionData) return;

    try {
      const updatedData: SessionStorageData = {
        ...sessionData,
        lastActivity: new Date().toISOString(),
        messageCount: sessionData.messageCount + 1
      };

      const storageKey = getStorageKey(sessionId);
      storage.setItem(storageKey, JSON.stringify(updatedData));

      // Update sessions list
      const sessionsListKey = getSessionsListKey();
      const existingSessions = getStoredSessions();
      const updatedSessions = existingSessions.map(s => 
        s.sessionId === sessionId ? updatedData : s
      );
      storage.setItem(sessionsListKey, JSON.stringify(updatedSessions));

      setSessionData(updatedData);
    } catch (error) {
      console.error('Failed to update session activity:', error);
    }
  }, [storage, sessionId, sessionData, getStorageKey, getSessionsListKey]);

  const getStoredSessions = useCallback((): SessionStorageData[] => {
    if (!storage) return [];

    try {
      const sessionsListKey = getSessionsListKey();
      const storedList = storage.getItem(sessionsListKey);
      
      if (storedList) {
        const sessions: SessionStorageData[] = JSON.parse(storedList);
        // Filter out expired sessions
        const validSessions = sessions.filter(s => !isSessionExpired(s.lastActivity));
        
        // Update storage if we filtered out expired sessions
        if (validSessions.length !== sessions.length) {
          storage.setItem(sessionsListKey, JSON.stringify(validSessions));
        }
        
        return validSessions;
      }
    } catch (error) {
      console.error('Failed to get stored sessions:', error);
    }

    return [];
  }, [storage, getSessionsListKey, isSessionExpired]);

  const removeStoredSession = useCallback((targetSessionId: string) => {
    if (!storage) return;

    try {
      // Remove individual session data
      const storageKey = getStorageKey(targetSessionId);
      storage.removeItem(storageKey);

      // Update sessions list
      const sessionsListKey = getSessionsListKey();
      const existingSessions = getStoredSessions();
      const filteredSessions = existingSessions.filter(s => s.sessionId !== targetSessionId);
      storage.setItem(sessionsListKey, JSON.stringify(filteredSessions));

      // If this was the current session, clear it
      if (sessionId === targetSessionId) {
        setSessionId(null);
        setSessionData(null);
      }

      console.log(`Session ${targetSessionId} removed from ${storageType}`);
    } catch (error) {
      console.error('Failed to remove stored session:', error);
    }
  }, [storage, getStorageKey, getSessionsListKey, sessionId, storageType]);

  const clearAllSessions = useCallback(() => {
    if (!storage) return;

    try {
      const storedSessions = getStoredSessions();
      
      // Remove all individual session data
      storedSessions.forEach(session => {
        const storageKey = getStorageKey(session.sessionId);
        storage.removeItem(storageKey);
      });

      // Clear sessions list
      const sessionsListKey = getSessionsListKey();
      storage.removeItem(sessionsListKey);

      setSessionId(null);
      setSessionData(null);
      onSessionCleared?.();

      console.log(`All sessions cleared from ${storageType}`);
    } catch (error) {
      console.error('Failed to clear all sessions:', error);
    }
  }, [storage, getStoredSessions, getStorageKey, getSessionsListKey, onSessionCleared, storageType]);

  // Auto-recover session on mount
  useEffect(() => {
    if (storage && !sessionId) {
      recoverSession();
    }
  }, [storage, sessionId, recoverSession]);

  // Auto-save session activity
  useEffect(() => {
    if (autoSave && sessionId && sessionData) {
      const interval = setInterval(() => {
        updateActivity();
      }, 30000); // Update every 30 seconds

      return () => clearInterval(interval);
    }
  }, [autoSave, sessionId, sessionData, updateActivity]);

  const hasStoredSession = sessionData !== null;

  return {
    sessionId,
    isRecovering,
    hasStoredSession,
    sessionData,
    saveSession,
    clearSession,
    recoverSession,
    updateActivity,
    getStoredSessions,
    removeStoredSession,
    clearAllSessions
  };
}