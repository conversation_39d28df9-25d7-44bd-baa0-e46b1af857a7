from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="Test Social Media API")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Social Media Manager API Test", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.get("/api/health")
async def api_health():
    return {"status": "healthy", "service": "social-media-api"}

if __name__ == "__main__":
    print("Starting minimal FastAPI server...")
    uvicorn.run(
        "minimal_backend:app",
        host="127.0.0.1",
        port=8000,
        reload=False,
        log_level="info"
    )