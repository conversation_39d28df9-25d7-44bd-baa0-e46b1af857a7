"""
Enhanced Chat Router with ADK Integration

This router extends the existing chat functionality to support ADK agent communication
while maintaining backward compatibility and providing fallback mechanisms.

Requirements covered: 1.2, 4.2, 5.1
"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import StreamingResponse
from fastapi.security import HTTPBearer
import httpx

from app.models.chat_models import (
    ChatMessageRequest, ChatMessageResponse, ChatHistoryRequest, 
    ChatHistoryResponse, AgentSelectionRequest, AgentSelectionResponse,
    ChatError, ChatStatus, EnhancedChatMessage, ChatSession, ChatSessionType
)
from app.models.adk_models import (
    ADKRunAgentRequest, ADKMessage, ADKContentPart, MessageRole,
    StreamingChunk, ADKAgentInfo, ADKErrorResponse, ADKSessionCreateResponse,
    ADKSessionInfo, ADKEvent
)
from app.services.adk_service import ADKService
from app.services.session_manager import SessionManager
from app.services.event_transformer import ADKEventTransformer
from app.services.chat_service import chat_service
from app.core.observability import observability
from app.core.feature_flags import (
    is_adk_enabled, is_streaming_enabled, is_agent_enabled,
    is_debug_mode_enabled, get_feature_flag_manager
)
from app.core.error_handling import (
    ADKIntegrationError,
    ADKServerUnavailableError,
    ADKAgentNotFoundError,
    ADKStreamingError,
    ADKTimeoutError,
    ErrorHandler,
    create_error_stream
)

# Initialize router
router = APIRouter()
security = HTTPBearer(auto_error=False)

# Initialize services
adk_service = ADKService()
event_transformer = ADKEventTransformer()
session_manager = SessionManager(adk_service)
error_handler = ErrorHandler(observability.logger)


async def get_current_user_id(token: Optional[str] = Depends(security)) -> str:
    """
    Extract user ID from authentication token.
    For now, returns a default user ID. In production, this would validate the token.
    """
    # TODO: Implement proper authentication
    return "default_user"


@router.post("/adk-stream")
async def adk_stream_endpoint(
    request: ADKRunAgentRequest,
    user_id: str = Depends(get_current_user_id)
) -> StreamingResponse:
    """
    Direct SSE streaming endpoint that proxies ADK /run_sse responses.
    
    This endpoint provides a direct proxy to ADK's streaming functionality,
    parsing Event objects from the SSE data stream and transforming them
    to frontend-compatible StreamingChunk format.
    
    Requirements: 2.1, 2.3, 2.4
    """
    try:
        observability.logger.info(
            f"Direct ADK streaming request: user={user_id}, "
            f"agent={request.app_name}, session={request.session_id}"
        )
        
        # Validate ADK availability
        if not await _is_adk_available():
            error = ADKServerUnavailableError()
            raise error_handler.create_http_exception(error)
        
        # Validate agent exists
        try:
            available_agents = await adk_service.list_agents()
            agent_names = [agent.name for agent in available_agents]
            if request.app_name not in agent_names:
                error = ADKAgentNotFoundError(
                    agent_name=request.app_name,
                    available_agents=agent_names
                )
                raise error_handler.create_http_exception(error)
        except ADKIntegrationError:
            raise
        except Exception as e:
            error = ADKServerUnavailableError(
                message="Failed to validate agent availability",
                original_error=e
            )
            raise error_handler.create_http_exception(error)
        
        async def adk_sse_stream():
            """
            Pure ADK SSE stream proxy with comprehensive Event object handling.
            
            This generator:
            1. Proxies ADK /run_sse responses directly
            2. Parses ADK Event objects from SSE data stream
            3. Transforms Events to StreamingChunk format
            4. Handles turn_complete and interrupted fields properly
            """
            event_count = 0
            last_invocation_id = None
            
            try:
                # Send stream start indicator
                start_chunk = StreamingChunk(
                    content="",
                    done=False,
                    message_id=f"stream_start_{request.session_id}",
                    metadata={
                        "event_type": "stream_start",
                        "app_name": request.app_name,
                        "session_id": request.session_id,
                        "user_id": request.user_id,
                        "timestamp": datetime.now().isoformat()
                    }
                )
                yield f"data: {start_chunk.model_dump_json()}\n\n"
                
                # Stream from ADK with detailed event processing
                async for chunk in adk_service.stream_message(request):
                    event_count += 1
                    last_invocation_id = chunk.message_id
                    
                    # Enhanced logging for event processing
                    observability.logger.debug(
                        f"Processing ADK Event {event_count}: "
                        f"invocation_id={chunk.message_id}, "
                        f"content_length={len(chunk.content)}, "
                        f"turn_complete={chunk.metadata.get('turn_complete', False)}, "
                        f"interrupted={chunk.metadata.get('interrupted', False)}, "
                        f"has_function_calls={chunk.metadata.get('has_function_calls', False)}, "
                        f"has_long_running_tools={chunk.metadata.get('has_long_running_tools', False)}"
                    )
                    
                    # Add stream-specific metadata
                    chunk.metadata.update({
                        "event_number": event_count,
                        "stream_type": "adk_sse_proxy",
                        "processed_at": datetime.now().isoformat()
                    })
                    
                    # Yield the transformed chunk
                    yield f"data: {chunk.model_dump_json()}\n\n"
                    
                    # Handle turn completion
                    if chunk.metadata.get("turn_complete", False):
                        observability.logger.info(
                            f"ADK turn completed for session {request.session_id}: "
                            f"events_processed={event_count}, "
                            f"final_invocation_id={last_invocation_id}"
                        )
                        break
                    
                    # Handle interruption
                    if chunk.metadata.get("interrupted", False):
                        observability.logger.warning(
                            f"ADK stream interrupted for session {request.session_id}: "
                            f"events_processed={event_count}, "
                            f"interruption_invocation_id={last_invocation_id}"
                        )
                        break
                
                # Send final stream completion event
                end_chunk = StreamingChunk(
                    content="",
                    done=True,
                    message_id=f"stream_end_{request.session_id}",
                    metadata={
                        "event_type": "stream_end",
                        "total_events": event_count,
                        "final_invocation_id": last_invocation_id,
                        "session_id": request.session_id,
                        "timestamp": datetime.now().isoformat()
                    }
                )
                yield f"data: {end_chunk.model_dump_json()}\n\n"
                
            except ADKIntegrationError as e:
                observability.logger.error(f"ADK integration error in direct streaming: {e}")
                
                error_chunk = error_handler.create_error_stream_chunk(
                    error=e,
                    message_id=f"adk_error_{request.session_id}"
                )
                error_chunk.metadata.update({
                    "events_processed": event_count,
                    "last_invocation_id": last_invocation_id,
                    "timestamp": datetime.now().isoformat()
                })
                yield f"data: {error_chunk.model_dump_json()}\n\n"
                
            except Exception as e:
                observability.logger.error(f"Unexpected error in ADK SSE proxy: {e}")
                
                # Wrap generic error
                adk_error = ADKStreamingError(
                    message="Unexpected error in streaming proxy",
                    original_error=e
                )
                error_chunk = error_handler.create_error_stream_chunk(
                    error=adk_error,
                    message_id=f"proxy_error_{request.session_id}"
                )
                error_chunk.metadata.update({
                    "events_processed": event_count,
                    "last_invocation_id": last_invocation_id,
                    "timestamp": datetime.now().isoformat()
                })
                yield f"data: {error_chunk.model_dump_json()}\n\n"
        
        return StreamingResponse(
            adk_sse_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
                "X-ADK-App-Name": request.app_name,
                "X-ADK-Session-ID": request.session_id,
                "X-ADK-User-ID": request.user_id,
                "X-Stream-Type": "adk_sse_proxy"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        observability.logger.error(f"Error setting up ADK SSE proxy: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to setup ADK SSE proxy: {str(e)}"
        )


@router.post("/send-message-stream")
async def send_message_stream(
    request: ChatMessageRequest,
    user_id: str = Depends(get_current_user_id)
) -> StreamingResponse:
    """
    Send a message and stream the response using ADK agents.
    
    This endpoint formats RunAgentRequest objects and handles streaming responses
    with fallback to legacy chat service when ADK is unavailable.
    """
    try:
        observability.logger.info(
            f"Received streaming message request from user {user_id}, "
            f"agent: {request.agent_name}, use_adk: {request.use_adk}"
        )
        
        # Validate request
        if not request.message.strip():
            raise HTTPException(status_code=400, detail="Message cannot be empty")
        
        # Check feature flags first
        adk_enabled = is_adk_enabled(user_id)
        streaming_enabled = is_streaming_enabled(user_id)
        
        # Determine if we should use ADK
        use_adk = (request.use_adk and 
                  adk_enabled and 
                  streaming_enabled and 
                  await _is_adk_available())
        
        # Check agent-specific flags
        if use_adk and request.agent_name:
            agent_enabled = is_agent_enabled(request.agent_name, user_id)
            if not agent_enabled:
                observability.logger.info(f"Agent {request.agent_name} is disabled by feature flag")
                use_adk = False
        
        if use_adk and request.agent_name:
            # Use ADK streaming
            return await _stream_adk_response(request, user_id)
        else:
            # Fallback to legacy chat service
            fallback_reason = "disabled by feature flags" if not adk_enabled else "ADK unavailable"
            observability.logger.info(f"Falling back to legacy chat service: {fallback_reason}")
            return await _stream_legacy_response(request, user_id)
            
    except ADKIntegrationError as e:
        observability.logger.error(f"ADK integration error in send_message_stream: {e}")
        return await _create_adk_error_stream(e)
    except Exception as e:
        observability.logger.error(f"Unexpected error in send_message_stream: {e}")
        adk_error = ADKIntegrationError(
            message="Unexpected error in message streaming",
            original_error=e
        )
        return await _create_adk_error_stream(adk_error)


async def _stream_adk_response(request: ChatMessageRequest, user_id: str) -> StreamingResponse:
    """
    Stream response using ADK agents with comprehensive SSE handling.
    
    This function creates an SSE streaming endpoint that proxies ADK /run_sse responses,
    parsing ADK Event objects and transforming them to frontend-compatible format.
    
    Requirements: 2.1, 2.3, 2.4
    """
    try:
        # Get or create session
        session_id = await session_manager.get_or_create_session(
            user_id=user_id,
            agent_name=request.agent_name,
            session_id=request.session_id
        )
        
        # Format ADK request
        adk_request = ADKRunAgentRequest(
            app_name=request.agent_name,
            user_id=user_id,
            session_id=session_id,
            new_message=ADKMessage.from_text(request.message),
            streaming=True
        )
        
        observability.logger.info(
            f"Starting SSE stream for user {user_id}, agent {request.agent_name}, "
            f"session {session_id}"
        )
        
        async def event_stream():
            """
            SSE event stream generator that proxies ADK /run_sse responses.
            
            Handles:
            - ADK Event object parsing from SSE data stream
            - Event transformation to StreamingChunk format
            - turn_complete and interrupted field processing
            - Error handling and recovery
            """
            stream_started = False
            chunks_sent = 0
            
            try:
                # Send initial connection event
                connection_chunk = StreamingChunk(
                    content="",
                    done=False,
                    message_id=f"connection_{session_id}",
                    metadata={
                        "event_type": "connection_established",
                        "session_id": session_id,
                        "agent_name": request.agent_name,
                        "timestamp": datetime.now().isoformat()
                    }
                )
                yield f"data: {connection_chunk.model_dump_json()}\n\n"
                stream_started = True
                
                # Stream from ADK with enhanced error handling
                async for chunk in adk_service.stream_message(adk_request):
                    chunks_sent += 1
                    
                    # Log chunk details for debugging
                    observability.logger.debug(
                        f"Streaming chunk {chunks_sent}: "
                        f"content_length={len(chunk.content)}, "
                        f"done={chunk.done}, "
                        f"interrupted={chunk.metadata.get('interrupted', False)}, "
                        f"turn_complete={chunk.metadata.get('turn_complete', False)}"
                    )
                    
                    # Enhance chunk metadata with streaming info
                    chunk.metadata.update({
                        "chunk_number": chunks_sent,
                        "session_id": session_id,
                        "agent_name": request.agent_name,
                        "stream_timestamp": datetime.now().isoformat()
                    })
                    
                    # Send chunk as SSE data
                    yield f"data: {chunk.model_dump_json()}\n\n"
                    
                    # Check for completion or interruption
                    if chunk.done:
                        observability.logger.info(
                            f"Stream completed for session {session_id}: "
                            f"chunks_sent={chunks_sent}, "
                            f"interrupted={chunk.metadata.get('interrupted', False)}"
                        )
                        break
                    
                    # Handle interruption
                    if chunk.metadata.get("interrupted", False):
                        observability.logger.warning(
                            f"Stream interrupted for session {session_id} at chunk {chunks_sent}"
                        )
                        break
                
                # Send completion event if stream ended normally
                if chunks_sent > 0:
                    completion_chunk = StreamingChunk(
                        content="",
                        done=True,
                        message_id=f"completion_{session_id}",
                        metadata={
                            "event_type": "stream_completed",
                            "total_chunks": chunks_sent,
                            "session_id": session_id,
                            "timestamp": datetime.now().isoformat()
                        }
                    )
                    yield f"data: {completion_chunk.model_dump_json()}\n\n"
                    
            except ADKIntegrationError as e:
                observability.logger.error(f"ADK integration error in streaming: {e}")
                
                # Send detailed error chunk using error handler
                error_chunk = error_handler.create_error_stream_chunk(
                    error=e,
                    message_id=f"error_{session_id}_{datetime.now().timestamp()}"
                )
                error_chunk.metadata.update({
                    "session_id": session_id,
                    "chunks_sent": chunks_sent,
                    "stream_started": stream_started,
                    "timestamp": datetime.now().isoformat(),
                    "recoverable": True
                })
                yield f"data: {error_chunk.model_dump_json()}\n\n"
                
            except Exception as e:
                observability.logger.error(f"Unexpected error in SSE streaming: {e}")
                
                # Wrap and send generic error chunk
                adk_error = ADKStreamingError(
                    message="Unexpected error in streaming",
                    original_error=e
                )
                error_chunk = error_handler.create_error_stream_chunk(
                    error=adk_error,
                    message_id=f"error_{session_id}_{datetime.now().timestamp()}"
                )
                error_chunk.metadata.update({
                    "session_id": session_id,
                    "chunks_sent": chunks_sent,
                    "stream_started": stream_started,
                    "timestamp": datetime.now().isoformat(),
                    "recoverable": False
                })
                yield f"data: {error_chunk.model_dump_json()}\n\n"
            
            finally:
                # Log final stream statistics
                observability.logger.info(
                    f"SSE stream ended for session {session_id}: "
                    f"chunks_sent={chunks_sent}, stream_started={stream_started}"
                )
        
        # Create streaming response with comprehensive headers
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
                "X-Session-ID": session_id,
                "X-Agent-Name": request.agent_name,
                "X-Stream-Type": "adk_sse",
                "X-User-ID": user_id
            }
        )
        
    except Exception as e:
        observability.logger.error(f"Error setting up ADK SSE streaming: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to setup SSE streaming: {str(e)}"
        )


async def _stream_legacy_response(request: ChatMessageRequest, user_id: str) -> StreamingResponse:
    """Stream response using legacy chat service"""
    try:
        # Use existing chat service
        session_id = request.session_id or str(uuid.uuid4())
        
        async def event_stream():
            try:
                async for chunk in chat_service.send_message_streaming(
                    session_id=session_id,
                    message=request.message,
                    user_id=user_id
                ):
                    yield f"data: {chunk.model_dump_json()}\n\n"
            except Exception as e:
                observability.logger.error(f"Legacy chat service error: {e}")
                error_chunk = StreamingChunk(
                    content=f"I apologize, but I encountered an issue: {str(e)}",
                    done=True,
                    message_id=str(uuid.uuid4()),
                    metadata={"error": True, "fallback": True}
                )
                yield f"data: {error_chunk.model_dump_json()}\n\n"
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Session-ID": session_id,
                "X-Fallback": "true"
            }
        )
        
    except Exception as e:
        observability.logger.error(f"Error in legacy streaming: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to setup legacy streaming: {str(e)}")


@router.get("/agents")
async def list_available_agents(
    user_id: str = Depends(get_current_user_id)
) -> List[ADKAgentInfo]:
    """
    List all available ADK agents with their capabilities.
    
    This endpoint uses the /list-apps endpoint to discover agents.
    """
    try:
        observability.logger.info(f"Listing available agents for user {user_id}")
        
        # Check feature flags
        if not is_adk_enabled(user_id):
            observability.logger.info("ADK disabled by feature flag, returning fallback agents")
            return [
                ADKAgentInfo(
                    name="fallback_agent",
                    description="Fallback social media assistant",
                    available=True,
                    capabilities=["content_creation", "general_assistance"],
                    tools=[]
                )
            ]
        
        if await _is_adk_available():
            agents = await adk_service.list_agents()
            
            # Filter agents based on feature flags
            enabled_agents = []
            for agent in agents:
                if is_agent_enabled(agent.name, user_id):
                    enabled_agents.append(agent)
                else:
                    observability.logger.debug(f"Agent {agent.name} filtered out by feature flag")
            
            return enabled_agents
        else:
            # Return fallback agent info
            return [
                ADKAgentInfo(
                    name="fallback_agent",
                    description="Fallback social media assistant",
                    available=True,
                    capabilities=["content_creation", "general_assistance"],
                    tools=[]
                )
            ]
            
    except Exception as e:
        observability.logger.error(f"Error listing agents: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list agents: {str(e)}")


@router.post("/session")
async def create_session(
    request: Dict[str, Any],
    user_id: str = Depends(get_current_user_id)
) -> Dict[str, str]:
    """
    Create a new ADK session for the specified agent.
    
    This endpoint creates a new session and returns the session ID.
    """
    try:
        agent_name = request.get("agent_name", "content_planner")
        
        observability.logger.info(
            f"Creating session for user {user_id}, agent: {agent_name}"
        )
        
        # Check feature flags
        if not is_adk_enabled(user_id):
            raise HTTPException(
                status_code=503,
                detail="ADK integration is currently disabled"
            )
        
        if not is_agent_enabled(agent_name, user_id):
            raise HTTPException(
                status_code=403,
                detail=f"Agent '{agent_name}' is not available for your account"
            )
        
        # Create session
        session_id = await session_manager.get_or_create_session(
            user_id=user_id,
            agent_name=agent_name,
            session_id=None  # Force new session
        )
        
        return {"session_id": session_id}
        
    except HTTPException:
        raise
    except Exception as e:
        observability.logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.post("/select-agent")
async def select_agent(
    request: AgentSelectionRequest,
    user_id: str = Depends(get_current_user_id)
) -> AgentSelectionResponse:
    """
    Select or switch to a different ADK agent.
    
    This endpoint handles agent selection logic and session management.
    """
    try:
        observability.logger.info(
            f"Agent selection request from user {user_id}: {request.agent_name}"
        )
        
        # Check feature flags first
        if not is_adk_enabled(user_id):
            raise HTTPException(
                status_code=503,
                detail="ADK integration is currently disabled"
            )
        
        if not is_agent_enabled(request.agent_name, user_id):
            raise HTTPException(
                status_code=403,
                detail=f"Agent '{request.agent_name}' is not available for your account"
            )
        
        # Validate agent availability
        if await _is_adk_available():
            available_agents = await adk_service.list_agents()
            agent_names = [agent.name for agent in available_agents]
            
            if request.agent_name not in agent_names:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Agent '{request.agent_name}' not found. Available: {agent_names}"
                )
        
        # Get or create session for the selected agent
        session_id = await session_manager.get_or_create_session(
            user_id=user_id,
            agent_name=request.agent_name,
            session_id=request.session_id
        )
        
        # Get agent info
        agent_info = None
        if await _is_adk_available():
            try:
                agents = await adk_service.list_agents()
                agent_info = next(
                    (agent.model_dump() for agent in agents if agent.name == request.agent_name),
                    None
                )
            except Exception as e:
                observability.logger.warning(f"Failed to get agent info: {e}")
        
        return AgentSelectionResponse(
            session_id=session_id,
            agent_name=request.agent_name,
            agent_info=agent_info,
            history_preserved=request.preserve_history
        )
        
    except HTTPException:
        raise
    except Exception as e:
        observability.logger.error(f"Error in agent selection: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to select agent: {str(e)}")


@router.get("/history/{session_id}")
async def get_chat_history(
    session_id: str,
    user_id: str = Depends(get_current_user_id),
    limit: int = Query(default=50, ge=1, le=1000),
    offset: int = Query(default=0, ge=0),
    include_metadata: bool = Query(default=False)
) -> ChatHistoryResponse:
    """
    Get chat history for a session.
    
    This endpoint retrieves conversation history from ADK sessions or legacy storage.
    """
    try:
        observability.logger.info(f"Getting chat history for session {session_id}")
        
        # Try to get ADK session history first
        try:
            if await _is_adk_available():
                history = await session_manager.get_chat_history(
                    user_id=user_id,
                    session_id=session_id
                )
                
                # Convert to enhanced chat messages
                messages = []
                for event in history[offset:offset + limit]:
                    if hasattr(event, 'content') and event.content:
                        message = _convert_adk_event_to_message(event, session_id, user_id)
                        if message:
                            messages.append(message)
                
                # Create session info
                session_info = ChatSession(
                    id=session_id,
                    user_id=user_id,
                    session_type=ChatSessionType.ADK,
                    created_at=datetime.now(),
                    last_activity=datetime.now(),
                    message_count=len(history)
                )
                
                return ChatHistoryResponse(
                    messages=messages,
                    total_count=len(history),
                    session_info=session_info,
                    has_more=(offset + limit) < len(history)
                )
        except Exception as e:
            observability.logger.warning(f"Failed to get ADK history: {e}")
        
        # Fallback to legacy chat service
        legacy_messages = await chat_service.get_chat_history(session_id)
        
        # Convert legacy messages to enhanced format
        enhanced_messages = []
        for msg in legacy_messages[offset:offset + limit]:
            enhanced_msg = EnhancedChatMessage(
                id=msg.id,
                session_id=msg.session_id,
                role=MessageRole(msg.role.value),
                content=msg.content,
                user_id=msg.user_id,
                timestamp=msg.timestamp,
                metadata={"source": "legacy"}
            )
            enhanced_messages.append(enhanced_msg)
        
        session_info = ChatSession(
            id=session_id,
            user_id=user_id,
            session_type=ChatSessionType.LEGACY,
            created_at=datetime.now(),
            last_activity=datetime.now(),
            message_count=len(legacy_messages)
        )
        
        return ChatHistoryResponse(
            messages=enhanced_messages,
            total_count=len(legacy_messages),
            session_info=session_info,
            has_more=(offset + limit) < len(legacy_messages)
        )
        
    except Exception as e:
        observability.logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get chat history: {str(e)}")


@router.get("/status")
async def get_chat_status(
    user_id: str = Depends(get_current_user_id)
) -> ChatStatus:
    """
    Get current chat system status including ADK availability and features.
    """
    try:
        # Check feature flags
        adk_feature_enabled = is_adk_enabled(user_id)
        streaming_feature_enabled = is_streaming_enabled(user_id)
        
        adk_available = adk_feature_enabled and await _is_adk_available()
        available_agents = []
        
        if adk_available:
            try:
                agents = await adk_service.list_agents()
                # Filter by feature flags
                available_agents = [
                    agent.name for agent in agents 
                    if is_agent_enabled(agent.name, user_id)
                ]
            except Exception as e:
                observability.logger.warning(f"Failed to get agent list: {e}")
                adk_available = False
        
        # Count active sessions (simplified)
        active_sessions = len(chat_service.active_sessions)
        
        # Get feature flag status
        feature_flags = get_feature_flag_manager()
        
        return ChatStatus(
            adk_enabled=adk_available,
            available_agents=available_agents,
            active_sessions=active_sessions,
            system_health="healthy" if adk_available else ("disabled" if not adk_feature_enabled else "degraded"),
            features={
                "adk_integration": adk_feature_enabled,
                "adk_server_available": await _is_adk_available(),
                "streaming": streaming_feature_enabled,
                "agent_selection": adk_feature_enabled,
                "session_persistence": feature_flags.is_enabled("session_persistence_enabled"),
                "fallback_mode": feature_flags.is_enabled("adk_fallback_enabled"),
                "debug_mode": is_debug_mode_enabled(),
                "function_calls": feature_flags.is_enabled("function_calls_enabled"),
                "rollout_percentage": feature_flags.get_flag_value("rollout_percentage", 100)
            }
        )
        
    except Exception as e:
        observability.logger.error(f"Error getting chat status: {e}")
        return ChatStatus(
            adk_enabled=False,
            available_agents=[],
            active_sessions=0,
            system_health="error",
            features={
                "adk_integration": False,
                "streaming": True,
                "agent_selection": False,
                "session_persistence": True,
                "fallback_mode": True
            }
        )


@router.get("/feature-flags")
async def get_feature_flags(
    user_id: str = Depends(get_current_user_id)
) -> Dict[str, Any]:
    """
    Get current feature flag status for the user.
    
    This endpoint returns the feature flags that affect the user's experience.
    """
    try:
        feature_flags = get_feature_flag_manager()
        
        # Get user-specific feature status
        user_features = {
            "adk_enabled": is_adk_enabled(user_id),
            "streaming_enabled": is_streaming_enabled(user_id),
            "debug_mode": is_debug_mode_enabled(),
            "rollout_percentage": feature_flags.get_flag_value("rollout_percentage"),
            "is_beta_user": feature_flags.is_beta_user(user_id),
            "enabled_features": {
                "agent_selection": feature_flags.is_enabled("agent_selection_enabled", user_id),
                "function_calls": feature_flags.is_enabled("function_calls_enabled", user_id),
                "session_persistence": feature_flags.is_enabled("session_persistence_enabled", user_id),
                "session_recovery": feature_flags.is_enabled("session_recovery_enabled", user_id),
                "connection_pooling": feature_flags.is_enabled("connection_pooling_enabled", user_id),
                "request_caching": feature_flags.is_enabled("request_caching_enabled", user_id),
                "metrics_collection": feature_flags.is_enabled("metrics_collection_enabled", user_id)
            },
            "disabled_agents": feature_flags.get_flag_value("disabled_agents", []),
            "beta_agents": feature_flags.get_flag_value("beta_agents", [])
        }
        
        return user_features
        
    except Exception as e:
        observability.logger.error(f"Error getting feature flags: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get feature flags: {str(e)}")


@router.post("/feature-flags/reload")
async def reload_feature_flags(
    user_id: str = Depends(get_current_user_id)
) -> Dict[str, str]:
    """
    Reload feature flags from configuration.
    
    This endpoint allows runtime reloading of feature flag configuration.
    Note: This might require admin privileges in production.
    """
    try:
        # TODO: Add admin privilege check in production
        feature_flags = get_feature_flag_manager()
        feature_flags.reload_config()
        
        observability.logger.info(f"Feature flags reloaded by user {user_id}")
        
        return {
            "status": "success",
            "message": "Feature flags reloaded successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        observability.logger.error(f"Error reloading feature flags: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reload feature flags: {str(e)}")


# Helper functions

async def _create_error_stream(error_message: str) -> StreamingResponse:
    """Create an error streaming response"""
    async def error_stream():
        error_chunk = StreamingChunk(
            content=error_message,
            done=True,
            message_id=str(uuid.uuid4()),
            metadata={"error": True}
        )
        yield f"data: {error_chunk.model_dump_json()}\n\n"
    
    return StreamingResponse(
        error_stream(),
        media_type="text/event-stream",
        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
    )





# Error handlers


# Helper functions for error handling

async def _create_adk_error_stream(error: ADKIntegrationError) -> StreamingResponse:
    """Create error streaming response for ADK integration errors"""
    
    async def error_stream():
        error_chunk = error_handler.create_error_stream_chunk(
            error=error,
            message_id=f"error_{datetime.now().timestamp()}"
        )
        yield f"data: {error_chunk.model_dump_json()}\n\n"
    
    return StreamingResponse(
        error_stream(),
        media_type="text/event-stream",
        status_code=503 if isinstance(error, ADKServerUnavailableError) else 500,
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Error-Code": error.error_code
        }
    )


async def _is_adk_available() -> bool:
    """Check if ADK service is available and healthy"""
    try:
        return await adk_service.is_healthy()
    except Exception as e:
        observability.logger.warning(f"ADK health check failed: {e}")
        return False





# Updated error handlers using new error handling system

@router.exception_handler(ADKIntegrationError)
async def adk_integration_error_handler(request, exc: ADKIntegrationError):
    """Handle ADK integration errors with proper error responses"""
    error_handler.log_error(exc)
    
    # Check if this is a streaming request
    if request.headers.get("Accept") == "text/event-stream":
        return await _create_adk_error_stream(exc)
    else:
        raise error_handler.create_http_exception(exc)


@router.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions and wrap them as ADK errors if appropriate"""
    observability.logger.error(f"Unhandled exception in chat router: {exc}")
    
    # Check if this might be an ADK-related error
    error_str = str(exc).lower()
    if any(keyword in error_str for keyword in ["adk", "agent", "session", "streaming"]):
        adk_error = ADKIntegrationError(
            message=f"Unexpected error: {str(exc)}",
            original_error=exc
        )
        return await adk_integration_error_handler(request, adk_error)
    else:
        # Let FastAPI handle non-ADK errors normally
        raise exc


@router.post("/session")
async def create_session(
    request: Dict[str, Any],
    user_id: str = Depends(get_current_user_id)
) -> ADKSessionCreateResponse:
    """
    Create a new ADK session for the user and agent.
    
    This endpoint creates a new session via ADK's session management endpoints
    and returns the session information for frontend persistence.
    
    Requirements: 3.1, 3.2
    """
    try:
        agent_name = request.get("agent_name", "content_planner")
        initial_state = request.get("initial_state")
        
        observability.logger.info(
            f"Creating new session for user {user_id} with agent {agent_name}"
        )
        
        # Check feature flags
        if not is_adk_enabled(user_id):
            raise HTTPException(
                status_code=503,
                detail="ADK integration is currently disabled"
            )
        
        if not is_agent_enabled(agent_name, user_id):
            raise HTTPException(
                status_code=403,
                detail=f"Agent '{agent_name}' is not available for your account"
            )
        
        # Validate ADK availability
        if not await _is_adk_available():
            raise HTTPException(
                status_code=503,
                detail="ADK server is currently unavailable"
            )
        
        # Create session via session manager
        session_response = await session_manager.create_session(
            app_name=agent_name,
            user_id=user_id,
            initial_state=initial_state
        )
        
        observability.logger.info(
            f"Successfully created session {session_response.id} for user {user_id}"
        )
        
        return session_response
        
    except HTTPException:
        raise
    except Exception as e:
        observability.logger.error(f"Error creating session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create session: {str(e)}"
        )


@router.get("/history")
async def get_chat_history_query(
    session_id: str = Query(..., description="Session ID"),
    user_id: str = Depends(get_current_user_id),
    limit: int = Query(default=50, ge=1, le=1000, description="Maximum number of messages"),
    offset: int = Query(default=0, ge=0, description="Number of messages to skip"),
    include_metadata: bool = Query(default=True, description="Include message metadata")
) -> Dict[str, Any]:
    """
    Get chat history for a session with ADK event support.
    
    This endpoint retrieves conversation history from ADK sessions,
    transforming ADK events to frontend-compatible message format.
    
    Requirements: 3.3, 3.4, 3.5
    """
    try:
        observability.logger.info(
            f"Getting chat history for session {session_id}, user {user_id}, "
            f"limit={limit}, offset={offset}"
        )
        
        # Check feature flags
        if not is_adk_enabled(user_id):
            # Return empty history if ADK is disabled
            return {
                "messages": [],
                "total_count": 0,
                "has_more": False,
                "session_info": None,
                "adk_events": []
            }
        
        # Try to get ADK session and history
        try:
            if await _is_adk_available():
                # Get session info first
                session_info = await session_manager.get_session_info(
                    session_id=session_id,
                    user_id=user_id
                )
                
                if not session_info:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Session {session_id} not found"
                    )
                
                # Get session events from ADK
                session_events = await adk_service.get_session_events(
                    app_name=session_info.app_name,
                    user_id=user_id,
                    session_id=session_id
                )
                
                # Apply pagination to events
                total_events = len(session_events)
                paginated_events = session_events[offset:offset + limit]
                
                # Transform ADK events to messages
                messages = []
                for event in paginated_events:
                    message = _convert_adk_event_to_message(
                        event, session_id, user_id, session_info.app_name
                    )
                    if message:
                        messages.append(message)
                
                # Create response with both messages and raw events
                response = {
                    "messages": [msg.model_dump() for msg in messages],
                    "total_count": total_events,
                    "has_more": (offset + limit) < total_events,
                    "session_info": {
                        "id": session_info.id,
                        "app_name": session_info.app_name,
                        "user_id": session_info.user_id,
                        "created_at": session_info.created_at.isoformat() if session_info.created_at else None,
                        "last_activity": session_info.last_activity.isoformat() if session_info.last_activity else None,
                        "event_count": session_info.event_count
                    },
                    "adk_events": [event.model_dump() for event in paginated_events] if include_metadata else []
                }
                
                observability.logger.info(
                    f"Retrieved {len(messages)} messages from {total_events} events "
                    f"for session {session_id}"
                )
                
                return response
                
        except Exception as e:
            observability.logger.warning(f"Failed to get ADK session history: {e}")
        
        # Fallback: return empty history
        return {
            "messages": [],
            "total_count": 0,
            "has_more": False,
            "session_info": None,
            "adk_events": []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        observability.logger.error(f"Error getting chat history: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get chat history: {str(e)}"
        )


def _convert_adk_event_to_message(
    event: ADKEvent, 
    session_id: str, 
    user_id: str, 
    agent_name: str = None
) -> Optional[EnhancedChatMessage]:
    """
    Convert ADK Event object to EnhancedChatMessage.
    
    This function handles the transformation of ADK Event objects
    to frontend-compatible message format, extracting text content
    and function call information.
    """
    try:
        if not event.content or not event.content.parts:
            return None
        
        # Extract text content from parts
        content = ""
        function_calls = []
        
        for part in event.content.parts:
            if hasattr(part, 'text') and part.text:
                content += part.text
            elif hasattr(part, 'function_call') and part.function_call:
                function_calls.append(part.function_call)
        
        # Skip events with no meaningful content
        if not content and not function_calls:
            return None
        
        # Create enhanced message
        message = EnhancedChatMessage(
            id=event.invocation_id or f"event_{int(datetime.now().timestamp())}",
            session_id=session_id,
            role=event.content.role,
            content=content,
            user_id=user_id,
            timestamp=datetime.now().isoformat(),  # ADK events don't have timestamps
            agent_name=event.author or agent_name,
            adk_invocation_id=event.invocation_id,
            function_calls=function_calls if function_calls else None,
            interrupted=event.interrupted,
            metadata={
                "turn_complete": event.turn_complete,
                "long_running_tool_ids": event.long_running_tool_ids,
                "adk_event": True
            }
        )
        
        return message
        
    except Exception as e:
        observability.logger.error(f"Error converting ADK event to message: {e}")
        return None




