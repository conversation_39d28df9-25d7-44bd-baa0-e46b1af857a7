# Google Cloud Platform Setup Script for Social Media Manager (PowerShell)
# This script configures the complete GCP infrastructure

param(
    [string]$ProjectId = "",
    [string]$Region = "us-central1",
    [string]$Zone = "us-central1-a",
    [switch]$Help
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host ""
    Write-Host "============================================" -ForegroundColor Blue
    Write-Host $Message -ForegroundColor Blue
    Write-Host "============================================" -ForegroundColor Blue
    Write-Host ""
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-Status "Checking prerequisites..."
    
    # Check if gcloud is installed
    try {
        $null = Get-Command gcloud -ErrorAction Stop
    }
    catch {
        Write-Error "gcloud CLI is not installed. Please install it first."
        Write-Host "Download from: https://cloud.google.com/sdk/docs/install"
        exit 1
    }
    
    # Check if user is authenticated
    $authAccount = gcloud auth list --filter=status:ACTIVE --format="value(account)" | Select-Object -First 1
    if ([string]::IsNullOrEmpty($authAccount)) {
        Write-Error "Not authenticated with gcloud. Please run 'gcloud auth login' first."
        exit 1
    }
    
    Write-Success "Prerequisites check completed"
}

# Function to get or set project configuration
function Set-ProjectConfig {
    Write-Header "PROJECT CONFIGURATION"
    
    # Get current project if set
    $currentProject = gcloud config get-value project 2>$null
    
    if ([string]::IsNullOrEmpty($script:ProjectId)) {
        if ([string]::IsNullOrEmpty($currentProject)) {
            Write-Host "No project currently configured."
            $script:ProjectId = Read-Host "Enter your Google Cloud Project ID"
        } else {
            Write-Host "Current project: $currentProject"
            $useCurrentInput = Read-Host "Use current project? (y/N)"
            if ($useCurrentInput -match "^[Yy]$") {
                $script:ProjectId = $currentProject
            } else {
                $script:ProjectId = Read-Host "Enter your Google Cloud Project ID"
            }
        }
    }
    
    # Set project configuration
    gcloud config set project $script:ProjectId
    
    # Set region and zone
    $regionInput = Read-Host "Enter region [$script:Region]"
    if (![string]::IsNullOrEmpty($regionInput)) {
        $script:Region = $regionInput
    }
    
    $zoneInput = Read-Host "Enter zone [$script:Zone]"
    if (![string]::IsNullOrEmpty($zoneInput)) {
        $script:Zone = $zoneInput
    }
    
    gcloud config set compute/region $script:Region
    gcloud config set compute/zone $script:Zone
    
    Write-Success "Project configuration completed"
    Write-Host "  Project: $($script:ProjectId)"
    Write-Host "  Region: $($script:Region)"
    Write-Host "  Zone: $($script:Zone)"
}

# Function to enable required APIs
function Enable-APIs {
    Write-Header "ENABLING GOOGLE CLOUD APIS"
    
    $apis = @(
        "run.googleapis.com",
        "cloudbuild.googleapis.com",
        "containerregistry.googleapis.com",
        "artifactregistry.googleapis.com",
        "secretmanager.googleapis.com",
        "firestore.googleapis.com",
        "bigquery.googleapis.com",
        "logging.googleapis.com",
        "monitoring.googleapis.com",
        "cloudtrace.googleapis.com",
        "iam.googleapis.com",
        "youtube.googleapis.com",
        "oauth2.googleapis.com"
    )
    
    foreach ($api in $apis) {
        Write-Status "Enabling $api..."
        gcloud services enable $api --project=$script:ProjectId
    }
    
    Write-Success "All APIs enabled successfully"
}

# Function to create service accounts
function New-ServiceAccounts {
    Write-Header "CREATING SERVICE ACCOUNTS"
    
    # Backend service account
    Write-Status "Creating backend service account..."
    try {
        gcloud iam service-accounts describe "social-media-backend@$($script:ProjectId).iam.gserviceaccount.com" --project=$script:ProjectId 2>$null
        Write-Warning "Backend service account already exists"
    }
    catch {
        gcloud iam service-accounts create social-media-backend `
            --display-name="Social Media Backend Service" `
            --description="Service account for Social Media Manager backend" `
            --project=$script:ProjectId
        Write-Success "Backend service account created"
    }
    
    # Assign roles to backend service account
    $backendRoles = @(
        "roles/secretmanager.secretAccessor",
        "roles/datastore.user",
        "roles/bigquery.user",
        "roles/logging.logWriter",
        "roles/monitoring.metricWriter",
        "roles/cloudtrace.agent"
    )
    
    foreach ($role in $backendRoles) {
        Write-Status "Assigning role: $role"
        gcloud projects add-iam-policy-binding $script:ProjectId `
            --member="serviceAccount:social-media-backend@$($script:ProjectId).iam.gserviceaccount.com" `
            --role="$role"
    }
    
    # Analytics service account
    Write-Status "Creating analytics service account..."
    try {
        gcloud iam service-accounts describe "social-media-analytics@$($script:ProjectId).iam.gserviceaccount.com" --project=$script:ProjectId 2>$null
        Write-Warning "Analytics service account already exists"
    }
    catch {
        gcloud iam service-accounts create social-media-analytics `
            --display-name="Social Media Analytics Service" `
            --description="Service account for analytics and data processing" `
            --project=$script:ProjectId
        Write-Success "Analytics service account created"
    }
    
    # Assign roles to analytics service account
    $analyticsRoles = @(
        "roles/bigquery.admin",
        "roles/datastore.user",
        "roles/secretmanager.secretAccessor"
    )
    
    foreach ($role in $analyticsRoles) {
        Write-Status "Assigning role: $role"
        gcloud projects add-iam-policy-binding $script:ProjectId `
            --member="serviceAccount:social-media-analytics@$($script:ProjectId).iam.gserviceaccount.com" `
            --role="$role"
    }
    
    Write-Success "Service accounts setup completed"
}

# Function to setup Firestore
function Set-Firestore {
    Write-Header "CONFIGURING FIRESTORE"
    
    Write-Status "Checking Firestore database..."
    
    try {
        gcloud firestore databases describe --database="(default)" --project=$script:ProjectId 2>$null
        Write-Warning "Firestore database already exists"
    }
    catch {
        Write-Status "Creating Firestore database..."
        gcloud firestore databases create --database="(default)" --location=$script:Region --project=$script:ProjectId
        Write-Success "Firestore database created"
    }
    
    Write-Success "Firestore setup completed"
}

# Function to setup BigQuery
function Set-BigQuery {
    Write-Header "CONFIGURING BIGQUERY"
    
    Write-Status "Creating BigQuery dataset..."
    
    try {
        bq ls -d --project_id=$script:ProjectId social_media_analytics 2>$null
        Write-Warning "BigQuery dataset already exists"
    }
    catch {
        bq mk `
            --dataset `
            --location=$script:Region `
            --description="Social Media Manager Analytics Data Warehouse" `
            --label=app:social-media-manager `
            --label=env:production `
            "$($script:ProjectId):social_media_analytics"
        Write-Success "BigQuery dataset created"
    }
    
    Write-Success "BigQuery setup completed"
}

# Function to setup Secret Manager
function Set-SecretManager {
    Write-Header "CONFIGURING SECRET MANAGER"
    
    Write-Status "Setting up secret placeholders..."
    
    $secrets = @(
        "app-secret-key",
        "google-client-id",
        "google-client-secret",
        "youtube-api-key",
        "instagram-app-id",
        "instagram-app-secret",
        "google-search-api-key",
        "google-search-engine-id"
    )
    
    foreach ($secret in $secrets) {
        try {
            gcloud secrets describe $secret --project=$script:ProjectId 2>$null
            Write-Warning "Secret $secret already exists"
        }
        catch {
            "placeholder-value-update-after-deployment" | gcloud secrets create $secret `
                --data-file=- `
                --replication-policy="automatic" `
                --project=$script:ProjectId `
                --labels=app=social-media-manager,env=production
            Write-Success "Created secret: $secret"
        }
    }
    
    Write-Warning "⚠️  Remember to update secret values after deployment!"
    Write-Warning "Use: gcloud secrets versions add SECRET_NAME --data-file=FILE"
    
    Write-Success "Secret Manager setup completed"
}

# Function to setup Cloud Build
function Set-CloudBuild {
    Write-Header "CONFIGURING CLOUD BUILD"
    
    Write-Status "Configuring Cloud Build..."
    
    # Get project number
    $projectNumber = gcloud projects describe $script:ProjectId --format="value(projectNumber)"
    
    # Grant Cloud Run Admin role to Cloud Build service account
    gcloud projects add-iam-policy-binding $script:ProjectId `
        --member="serviceAccount:$<EMAIL>" `
        --role="roles/run.admin"
    
    # Grant Service Account User role
    gcloud projects add-iam-policy-binding $script:ProjectId `
        --member="serviceAccount:$<EMAIL>" `
        --role="roles/iam.serviceAccountUser"
    
    Write-Success "Cloud Build setup completed"
}

# Function to setup Container Registry
function Set-ContainerRegistry {
    Write-Header "CONFIGURING CONTAINER REGISTRY"
    
    Write-Status "Configuring Container Registry permissions..."
    
    # Configure Docker to use gcloud as a credential helper
    gcloud auth configure-docker --quiet
    
    Write-Success "Container Registry setup completed"
}

# Function to setup monitoring and logging
function Set-Monitoring {
    Write-Header "CONFIGURING MONITORING & LOGGING"
    
    Write-Status "Setting up monitoring workspace..."
    Write-Status "Configuring log retention..."
    
    # Set up log sink for important application logs
    try {
        gcloud logging sinks describe social-media-app-logs --project=$script:ProjectId 2>$null
        Write-Warning "Log sink already exists"
    }
    catch {
        gcloud logging sinks create social-media-app-logs `
            "bigquery.googleapis.com/projects/$($script:ProjectId)/datasets/social_media_analytics" `
            --log-filter='resource.type="cloud_run_revision" AND labels."service-name"=("social-media-backend" OR "social-media-frontend")' `
            --project=$script:ProjectId
        Write-Success "Log sink created"
    }
    
    Write-Success "Monitoring and logging setup completed"
}

# Function to create environment configuration
function New-EnvConfig {
    Write-Header "CREATING ENVIRONMENT CONFIGURATION"
    
    # Create environment file for deployment
    $envContent = @"
# Production Environment Configuration
GOOGLE_CLOUD_PROJECT=$($script:ProjectId)
PROJECT_ID=$($script:ProjectId)
REGION=$($script:Region)
ZONE=$($script:Zone)

# Service Configuration
ENV=production
DEBUG=false

# Database Configuration
FIRESTORE_PROJECT_ID=$($script:ProjectId)
BIGQUERY_PROJECT_ID=$($script:ProjectId)
BIGQUERY_DATASET_ID=social_media_analytics

# Service URLs (update after deployment)
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-backend-domain.com

# OAuth Configuration (values stored in Secret Manager)
# These are retrieved at runtime from Secret Manager
SECRET_KEY=`${SECRET_KEY}
GOOGLE_CLIENT_ID=`${GOOGLE_CLIENT_ID}
GOOGLE_CLIENT_SECRET=`${GOOGLE_CLIENT_SECRET}
YOUTUBE_API_KEY=`${YOUTUBE_API_KEY}
INSTAGRAM_APP_ID=`${INSTAGRAM_APP_ID}
INSTAGRAM_APP_SECRET=`${INSTAGRAM_APP_SECRET}
GOOGLE_SEARCH_API_KEY=`${GOOGLE_SEARCH_API_KEY}
GOOGLE_SEARCH_ENGINE_ID=`${GOOGLE_SEARCH_ENGINE_ID}
"@
    
    $envContent | Out-File -FilePath "deployment\.env.production" -Encoding UTF8
    
    Write-Success "Environment configuration created: deployment\.env.production"
}

# Function to run final validation
function Test-Validation {
    Write-Header "VALIDATION"
    
    Write-Status "Validating GCP setup..."
    
    # Check project exists and is accessible
    try {
        gcloud projects describe $script:ProjectId 2>$null
        Write-Success "✓ Project accessible"
    }
    catch {
        Write-Error "✗ Project not accessible"
        return $false
    }
    
    # Check service accounts
    try {
        gcloud iam service-accounts describe "social-media-backend@$($script:ProjectId).iam.gserviceaccount.com" --project=$script:ProjectId 2>$null
        Write-Success "✓ Backend service account exists"
    }
    catch {
        Write-Error "✗ Backend service account missing"
    }
    
    # Check Firestore database
    try {
        gcloud firestore databases describe --database="(default)" --project=$script:ProjectId 2>$null
        Write-Success "✓ Firestore database exists"
    }
    catch {
        Write-Error "✗ Firestore database missing"
    }
    
    # Check BigQuery dataset
    try {
        bq ls -d --project_id=$script:ProjectId social_media_analytics 2>$null
        Write-Success "✓ BigQuery dataset exists"
    }
    catch {
        Write-Error "✗ BigQuery dataset missing"
    }
    
    Write-Success "Validation completed"
}

# Function to display next steps
function Show-NextSteps {
    Write-Header "NEXT STEPS"
    
    Write-Host "🎉 GCP Infrastructure setup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next Steps:" -ForegroundColor Blue
    Write-Host "1. Update secrets in Secret Manager with actual values:"
    Write-Host "   gcloud secrets versions add SECRET_NAME --data-file=SECRET_FILE"
    Write-Host ""
    Write-Host "2. Run BigQuery setup:"
    Write-Host "   cd scripts && python setup_bigquery.py"
    Write-Host ""
    Write-Host "3. Deploy applications:"
    Write-Host "   cd deployment && .\deploy.ps1"
    Write-Host ""
    Write-Host "4. Configure OAuth applications:"
    Write-Host "   - Google: https://console.developers.google.com"
    Write-Host "   - Instagram: https://developers.facebook.com"
    Write-Host ""
    Write-Host "5. Update DNS/Domain settings for custom domains"
    Write-Host ""
    Write-Host "🔗 Useful Links:" -ForegroundColor Blue
    Write-Host "  Cloud Console: https://console.cloud.google.com/home/<USER>"
    Write-Host "  Secret Manager: https://console.cloud.google.com/security/secret-manager?project=$($script:ProjectId)"
    Write-Host "  Firestore: https://console.cloud.google.com/firestore?project=$($script:ProjectId)"
    Write-Host "  BigQuery: https://console.cloud.google.com/bigquery?project=$($script:ProjectId)"
    Write-Host "  Cloud Run: https://console.cloud.google.com/run?project=$($script:ProjectId)"
}

# Main execution function
function Main {
    if ($Help) {
        Write-Host "Usage: .\setup_gcp.ps1 [-ProjectId PROJECT_ID] [-Region REGION] [-Zone ZONE]"
        return
    }
    
    Write-Header "SOCIAL MEDIA MANAGER - GCP SETUP"
    
    Test-Prerequisites
    Set-ProjectConfig
    Enable-APIs
    New-ServiceAccounts
    Set-Firestore
    Set-BigQuery
    Set-SecretManager
    Set-CloudBuild
    Set-ContainerRegistry
    Set-Monitoring
    New-EnvConfig
    Test-Validation
    Show-NextSteps
    
    Write-Success "🚀 Setup completed successfully!"
}

# Handle script interruption
trap {
    Write-Error "Setup interrupted"
    exit 1
}

# Run main function
Main