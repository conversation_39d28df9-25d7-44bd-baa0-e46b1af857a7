"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Calendar,
  Plus,
  Filter,
  Download,
  ChevronLeft,
  ChevronRight,
  Youtube,
  Instagram,
  Twitter,
  Clock,
  Hash,
  Target
} from "lucide-react";
import { format, addDays, startOfWeek, isSameDay, isToday, isFuture } from "date-fns";
import { useContentPlans } from "@/hooks/use-content-plans";
import { PostBrief } from "./post-brief";
import { GeneratePlanModal } from "./generate-plan-modal";

interface ContentPlannerCalendarProps {
  timeframe?: number;
}

export function ContentPlannerCalendar({ timeframe = 14 }: ContentPlannerCalendarProps) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'week' | 'month'>('week');
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  
  const { data: plans, isLoading } = useContentPlans();

  const platformIcons = {
    youtube: Youtube,
    instagram: Instagram,
    twitter: Twitter,
  };

  const platformColors = {
    youtube: "bg-red-100 text-red-700 border-red-200",
    instagram: "bg-pink-100 text-pink-700 border-pink-200",
    twitter: "bg-blue-100 text-blue-700 border-blue-200",
  };

  // Mock data for demonstration
  const mockPosts = [
    {
      id: "1",
      date: new Date(),
      platform: "youtube",
      title: "How to Build AI Apps in 2024",
      hook: "The future of app development is here!",
      status: "scheduled",
      bestTime: "15:00",
      hashtags: ["#AI", "#WebDev", "#Tutorial"]
    },
    {
      id: "2",
      date: addDays(new Date(), 1),
      platform: "instagram",
      title: "Behind the Scenes: Development Setup",
      hook: "Ever wondered how developers work?",
      status: "draft",
      bestTime: "19:00",
      hashtags: ["#DevLife", "#BehindTheScenes"]
    },
    {
      id: "3",
      date: addDays(new Date(), 2),
      platform: "twitter",
      title: "Quick tip: React performance optimization",
      hook: "One simple trick to boost your React app speed",
      status: "scheduled",
      bestTime: "10:00",
      hashtags: ["#React", "#Performance", "#WebDev"]
    },
    {
      id: "4",
      date: addDays(new Date(), 3),
      platform: "youtube",
      title: "Live Coding Session: Building a Chat App",
      hook: "Join me as we build something amazing together!",
      status: "idea",
      bestTime: "20:00",
      hashtags: ["#LiveCoding", "#React", "#Tutorial"]
    }
  ];

  const getWeekDays = (date: Date) => {
    const start = startOfWeek(date, { weekStartsOn: 1 }); // Monday
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getPostsForDate = (date: Date) => {
    return mockPosts.filter(post => isSameDay(new Date(post.date), date));
  };

  const weekDays = getWeekDays(selectedDate);

  const handlePreviousWeek = () => {
    setSelectedDate(addDays(selectedDate, -7));
  };

  const handleNextWeek = () => {
    setSelectedDate(addDays(selectedDate, 7));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-green-100 text-green-700';
      case 'draft': return 'bg-yellow-100 text-yellow-700';
      case 'idea': return 'bg-blue-100 text-blue-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-4" />
          <div className="grid grid-cols-7 gap-4">
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="h-64 bg-muted rounded" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Content Planner</h1>
          <p className="text-muted-foreground">
            Plan and schedule your content across all platforms
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => setShowGenerateModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Generate Plan
          </Button>
        </div>
      </div>

      {/* Calendar Navigation */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={handlePreviousWeek}>
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <h2 className="text-lg font-semibold">
                  {format(weekDays[0], 'MMM d')} - {format(weekDays[6], 'MMM d, yyyy')}
                </h2>
                <Button variant="outline" size="sm" onClick={handleNextWeek}>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{mockPosts.length} posts planned</Badge>
              <Button variant={viewMode === 'week' ? 'default' : 'outline'} size="sm" onClick={() => setViewMode('week')}>
                Week
              </Button>
              <Button variant={viewMode === 'month' ? 'default' : 'outline'} size="sm" onClick={() => setViewMode('month')}>
                Month
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-4">
            {weekDays.map((day) => {
              const posts = getPostsForDate(day);
              const isSelected = isSameDay(day, selectedDate);
              const isTodayDate = isToday(day);
              const isFutureDate = isFuture(day);

              return (
                <div
                  key={day.toISOString()}
                  className={`
                    min-h-64 p-3 border rounded-lg transition-colors cursor-pointer
                    ${isSelected ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'}
                    ${isTodayDate ? 'bg-blue-50 border-blue-200' : ''}
                    ${!isFutureDate && !isTodayDate ? 'bg-muted/30' : ''}
                  `}
                  onClick={() => setSelectedDate(day)}
                >
                  {/* Day Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <p className="text-sm font-medium">{format(day, 'EEE')}</p>
                      <p className={`text-lg font-bold ${isTodayDate ? 'text-blue-600' : ''}`}>
                        {format(day, 'd')}
                      </p>
                    </div>
                    {posts.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {posts.length}
                      </Badge>
                    )}
                  </div>

                  {/* Posts for this day */}
                  <div className="space-y-2">
                    {posts.map((post) => {
                      const Icon = platformIcons[post.platform as keyof typeof platformIcons];
                      const platformColor = platformColors[post.platform as keyof typeof platformColors];

                      return (
                        <div
                          key={post.id}
                          className={`
                            p-2 rounded-md border text-xs transition-all hover:shadow-sm
                            ${platformColor}
                          `}
                        >
                          <div className="flex items-center gap-1 mb-1">
                            {Icon && <Icon className="w-3 h-3" />}
                            <span className="font-medium truncate">{post.title}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs opacity-75">
                            <Clock className="w-3 h-3" />
                            <span>{post.bestTime}</span>
                            <Badge className={`text-xs h-4 ${getStatusColor(post.status)}`}>
                              {post.status}
                            </Badge>
                          </div>
                          {post.hashtags.length > 0 && (
                            <div className="flex items-center gap-1 mt-1">
                              <Hash className="w-3 h-3" />
                              <span className="truncate">
                                {post.hashtags.slice(0, 2).join(' ')}
                              </span>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* Add Post Button */}
                  {isFutureDate && (
                    <button
                      className="w-full mt-2 p-2 border border-dashed border-muted-foreground/30 rounded-md hover:border-primary/50 hover:bg-primary/5 transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Open post creation modal
                      }}
                    >
                      <Plus className="w-4 h-4 mx-auto text-muted-foreground" />
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Post Details */}
      {selectedDate && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>
                  Posts for {format(selectedDate, 'EEEE, MMMM d, yyyy')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {getPostsForDate(selectedDate).length > 0 ? (
                  <div className="space-y-4">
                    {getPostsForDate(selectedDate).map((post) => (
                      <PostBrief key={post.id} post={post} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No posts scheduled</h3>
                    <p className="text-muted-foreground mb-4">
                      Start planning content for this day
                    </p>
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Post
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">This Week</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Posts Scheduled</span>
                  <span className="font-medium">{mockPosts.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Platforms</span>
                  <span className="font-medium">3</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Best Time</span>
                  <span className="font-medium">7-9 PM</span>
                </div>
              </CardContent>
            </Card>

            {/* Platform Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Platform Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {Object.entries({
                  youtube: 2,
                  instagram: 1,
                  twitter: 1
                }).map(([platform, count]) => {
                  const Icon = platformIcons[platform as keyof typeof platformIcons];
                  return (
                    <div key={platform} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {Icon && <Icon className="w-4 h-4" />}
                        <span className="text-sm capitalize">{platform}</span>
                      </div>
                      <span className="font-medium">{count}</span>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Generate Plan Modal */}
      <GeneratePlanModal
        isOpen={showGenerateModal}
        onClose={() => setShowGenerateModal(false)}
      />
    </div>
  );
}