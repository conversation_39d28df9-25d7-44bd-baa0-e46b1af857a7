-- Top Performing Content Analysis
-- Identifies highest performing content across platforms and content types

WITH content_stats AS (
  SELECT
    post_id,
    account_id,
    user_id,
    platform,
    content_type,
    published_at,
    title,
    hashtags,
    likes,
    comments,
    shares,
    saves,
    views,
    reach,
    impressions,
    engagement_rate,
    ai_generated,
    content_pillars,
    
    -- Calculate total engagement
    (likes + comments + shares + COALESCE(saves, 0)) AS total_engagement,
    
    -- Calculate engagement per view ratio
    CASE 
      WHEN views > 0 
      THEN ROUND((likes + comments + shares + COALESCE(saves, 0)) / views * 100, 2)
      ELSE 0 
    END AS engagement_per_view,
    
    -- Time since published
    DATE_DIFF(CURRENT_DATE(), DATE(published_at), DAY) AS days_since_published
    
  FROM `{project_id}.{dataset_id}.content_performance`
  WHERE published_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
    AND published_at <= CURRENT_TIMESTAMP()
),

ranked_content AS (
  SELECT
    *,
    -- Rank by engagement rate within platform and content type
    ROW_NUMBER() OVER (
      PARTITION BY platform, content_type 
      ORDER BY engagement_rate DESC, total_engagement DESC
    ) AS engagement_rank,
    
    -- Rank by total engagement
    ROW_NUMBER() OVER (
      ORDER BY total_engagement DESC
    ) AS total_engagement_rank,
    
    -- Platform averages for comparison
    AVG(engagement_rate) OVER (PARTITION BY platform) AS platform_avg_engagement,
    AVG(total_engagement) OVER (PARTITION BY platform) AS platform_avg_total_engagement
    
  FROM content_stats
  WHERE total_engagement > 0  -- Filter out content with no engagement
)

SELECT
  post_id,
  platform,
  content_type,
  title,
  published_at,
  days_since_published,
  
  -- Engagement metrics
  likes,
  comments,
  shares,
  saves,
  total_engagement,
  ROUND(engagement_rate, 4) AS engagement_rate,
  engagement_per_view,
  
  -- Reach and impressions
  reach,
  impressions,
  views,
  
  -- Performance indicators
  CASE 
    WHEN engagement_rate > platform_avg_engagement * 1.5 THEN 'Excellent'
    WHEN engagement_rate > platform_avg_engagement * 1.2 THEN 'Above Average'
    WHEN engagement_rate > platform_avg_engagement * 0.8 THEN 'Average'
    ELSE 'Below Average'
  END AS performance_category,
  
  -- Rankings
  engagement_rank,
  total_engagement_rank,
  
  -- Comparison to platform averages
  ROUND(engagement_rate - platform_avg_engagement, 4) AS engagement_vs_platform_avg,
  ROUND(total_engagement - platform_avg_total_engagement, 0) AS total_engagement_vs_platform_avg,
  
  -- Content attributes
  ai_generated,
  content_pillars,
  ARRAY_LENGTH(hashtags) AS hashtag_count,
  
  -- Extract top hashtags
  CASE 
    WHEN ARRAY_LENGTH(hashtags) > 0 THEN hashtags[OFFSET(0)]
    ELSE NULL 
  END AS primary_hashtag

FROM ranked_content
WHERE engagement_rank <= 10  -- Top 10 per platform/content type
   OR total_engagement_rank <= 20  -- Or top 20 overall

ORDER BY total_engagement_rank;