"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api-client";
import { Message } from "@/components/chat/chat-interface";

export function useSendMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (message: string): Promise<void> => {
      // Add user message immediately to the UI
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content: message,
        timestamp: new Date(),
      };

      queryClient.setQueryData(["chat", "history"], (old: Message[] = []) => [
        ...old,
        userMessage,
      ]);

      try {
        // Send message to backend with streaming
        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('access_token') || 'dev-token'}`,
          },
          body: JSON.stringify({ 
            message, 
            streaming: true,
            session_id: localStorage.getItem('chat_session_id') || undefined
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Handle streaming response
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('No response body reader available');
        }

        let assistantMessage: Message = {
          id: Date.now().toString() + "_assistant",
          role: 'assistant',
          content: '',
          timestamp: new Date(),
        };

        // Add empty assistant message to show typing
        queryClient.setQueryData(["chat", "history"], (old: Message[] = []) => [
          ...old,
          assistantMessage,
        ]);

        const decoder = new TextDecoder();
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const jsonStr = line.slice(6).trim();
                  if (jsonStr) {
                    const data = JSON.parse(jsonStr);
                    if (data.content) {
                      assistantMessage.content += data.content;
                      
                      // Update the message in real-time
                      queryClient.setQueryData(["chat", "history"], (old: Message[] = []) => {
                        const newMessages = [...old];
                        const lastIndex = newMessages.length - 1;
                        if (lastIndex >= 0 && newMessages[lastIndex].role === 'assistant') {
                          newMessages[lastIndex] = { ...assistantMessage };
                        }
                        return newMessages;
                      });
                    }
                    if (data.done) {
                      break;
                    }
                  }
                } catch (e) {
                  // Ignore parsing errors for non-JSON lines
                  console.warn('Failed to parse SSE data:', line);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }
      } catch (error) {
        console.error('Chat message error:', error);
        throw error;
      }
    },
    onError: (error) => {
      // Remove the user message if sending failed
      queryClient.setQueryData(["chat", "history"], (old: Message[] = []) => {
        return old.slice(0, -2); // Remove both user and assistant messages
      });
      console.error("Failed to send message:", error);
    },
  });
}