#!/usr/bin/env python3
"""
Test script to validate agent loading and fix the research_tool error.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

def test_agent_loading():
    """Test loading the main agent to check for errors."""
    try:
        print("🔄 Testing agent loading...")
        
        # Import the agents module
        from agents import agent
        
        print("✅ Successfully imported agents module")
        
        # Check if root_agent exists
        if hasattr(agent, 'root_agent'):
            root_agent = agent.root_agent
            print(f"✅ Root agent loaded: {root_agent.name}")
            print(f"   - Model: {root_agent.model}")
            print(f"   - Tools: {len(root_agent.tools) if root_agent.tools else 0}")
            print(f"   - Sub-agents: {len(root_agent.sub_agents) if root_agent.sub_agents else 0}")
            
            # Test research tool
            if root_agent.tools:
                research_tool_found = False
                for tool in root_agent.tools:
                    if hasattr(tool, '__name__') and tool.__name__ == 'research_tool':
                        research_tool_found = True
                        print("✅ Research tool found and properly configured")
                        break
                    elif hasattr(tool, 'name') and 'research' in str(tool.name).lower():
                        research_tool_found = True
                        print("✅ Research tool found as function")
                        break
                
                if not research_tool_found:
                    print("⚠️ Research tool not found in tools list")
            
            return True
        else:
            print("❌ root_agent not found in agents module")
            return False
            
    except Exception as e:
        print(f"❌ Error loading agents: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_research_tool():
    """Test the research tool function directly."""
    try:
        print("\n🔄 Testing research tool function...")
        
        from agents.agent import research_tool
        
        # Test the research tool
        result = research_tool(
            research_type="trend_analysis",
            query="AI content creation",
            platform="youtube",
            timeframe="30d"
        )
        
        print("✅ Research tool executed successfully")
        print(f"   - Status: {result.get('status')}")
        print(f"   - Research type: {result.get('research_type')}")
        print(f"   - Query: {result.get('query')}")
        print(f"   - Source: {result.get('source')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing research tool: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 ADK Agent Loading Test")
    print("=" * 50)
    
    # Test 1: Agent loading
    agent_test = test_agent_loading()
    
    # Test 2: Research tool
    research_test = test_research_tool()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Agent Loading: {'✅ PASS' if agent_test else '❌ FAIL'}")
    print(f"   Research Tool: {'✅ PASS' if research_test else '❌ FAIL'}")
    
    if agent_test and research_test:
        print("\n🎉 All tests passed! Agent should work with ADK web.")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")