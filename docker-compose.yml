version: '3.8'

services:
  # Frontend Application
  frontend:
    build:
      context: ./app-frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api
    depends_on:
      - backend
    volumes:
      - ./app-frontend:/app
      - /app/node_modules
    command: npm run dev

  # Backend Application
  backend:
    build:
      context: ./app-agents
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - SECRET_KEY=dev-secret-key-change-in-production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
    volumes:
      - ./app-agents:/app
      - /app/__pycache__
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  # PostgreSQL for local development (alternative to Firestore)
  # postgres:
  #   image: postgres:15-alpine
  #   ports:
  #     - "5432:5432"
  #   environment:
  #     - POSTGRES_DB=social_media_manager
  #     - POSTGRES_USER=dev
  #     - POSTGRES_PASSWORD=devpassword
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data

volumes:
  redis_data:
  # postgres_data: