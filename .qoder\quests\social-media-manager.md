# Social Media Manager Agent - Design Document

## Overview

A production-ready web application that enables content creators to connect their YouTube and Instagram accounts and interact with an intelligent social media manager agent. The system provides comprehensive analytics, insights, and content planning through AI-powered analysis and grounded web research.

**Core Value Proposition**: Creators get personalized insights and actionable content strategies by chatting with a unified manager agent that analyzes their cross-platform performance and market trends.

## Technology Stack & Dependencies

### Frontend Stack
- **Framework**: Next.js 14+ (App Router) with React 18+ and TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query (React Query) for server state
- **Forms**: React Hook Form with Zod validation
- **Authentication**: Next-Auth.js or custom JWT implementation

### Backend Stack
- **API Framework**: Python FastAPI with async/await support
- **Agent Framework**: Google ADK (Agent Development Kit) for Python
- **Authentication**: OAuth 2.0 flows for platform integrations
- **Task Queue**: Celery with Redis for background processing

### Cloud Infrastructure
- **Compute**: Google Cloud Run (containerized deployment)
- **Database**: Firestore (NoSQL document store)
- **Analytics Warehouse**: BigQuery for historical data
- **Secrets**: Google Secret Manager for API keys and tokens
- **Monitoring**: Cloud Logging and Cloud Trace
- **Optional**: Vertex AI Agent Engine for session management

### External APIs
- YouTube Data API v3 (channels, videos, analytics)
- Instagram Graph API (Professional accounts)
- Google Search API (grounding for research)
- TikTok Business API (future implementation)
- X API v2 (future implementation)

## Frontend Architecture

### Component Hierarchy

```
App (Next.js App Router)
├── Layout Components
│   ├── MainLayout
│   ├── Sidebar (Accounts)
│   └── Header (User menu)
├── Page Components
│   ├── ChatPage
│   ├── ProfilePage
│   ├── PlannerPage
│   └── ConnectionsModal
└── Feature Components
    ├── ChatInterface
    ├── AccountConnection
    ├── MetricsDashboard
    ├── ContentCalendar
    └── PlatformTiles
```

### Component Specifications

#### ChatInterface Component
```typescript
interface ChatInterfaceProps {
  userId: string;
  initialMessages?: Message[];
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: {
    platforms?: string[];
    insights?: InsightData[];
  };
}
```

#### AccountConnection Component
```typescript
interface ConnectionProps {
  platform: 'youtube' | 'instagram' | 'tiktok' | 'x';
  status: 'connected' | 'disconnected' | 'pending';
  onConnect: (platform: string) => Promise<void>;
  onDisconnect: (platform: string) => Promise<void>;
}

interface AccountData {
  id: string;
  platform: string;
  handle: string;
  avatar: string;
  lastSync: Date;
  metrics: PlatformMetrics;
}
```

#### MetricsDashboard Component
```typescript
interface MetricsProps {
  timeframe: '7d' | '30d' | '90d';
  platforms: AccountData[];
}

interface PlatformMetrics {
  followers: number;
  engagement: number;
  reach: number;
  growth: {
    followers: number;
    engagement: number;
  };
}
```

### Routing & Navigation

```typescript
// App Router Structure
app/
├── page.tsx                 // Home/Landing
├── chat/
│   └── page.tsx            // Main chat interface
├── profile/
│   └── page.tsx            // Analytics dashboard
├── planner/
│   └── page.tsx            // Content calendar
├── connections/
│   └── page.tsx            // Account management
└── api/
    ├── auth/
    ├── chat/
    └── platforms/
```

### State Management

```typescript
// TanStack Query Keys
export const queryKeys = {
  user: ['user'],
  accounts: ['accounts'],
  metrics: (timeframe: string) => ['metrics', timeframe],
  chat: (userId: string) => ['chat', userId],
  plans: ['plans'],
} as const;

// Custom Hooks
export function useConnectedAccounts() {
  return useQuery({
    queryKey: queryKeys.accounts,
    queryFn: fetchConnectedAccounts,
  });
}

export function usePlatformMetrics(timeframe: string) {
  return useQuery({
    queryKey: queryKeys.metrics(timeframe),
    queryFn: () => fetchMetrics(timeframe),
  });
}
```

### API Integration Layer

```typescript
// API Client Configuration
class ApiClient {
  private baseURL: string;
  private authToken?: string;

  async chatWithAgent(message: string): Promise<ReadableStream> {
    return this.streamPost('/chat', { message });
  }

  async connectPlatform(platform: string, authCode: string): Promise<void> {
    return this.post(`/connect/${platform}`, { authCode });
  }

  async generateContentPlan(params: PlanParams): Promise<ContentPlan> {
    return this.post('/planner/generate', params);
  }
}
```

## Backend Architecture

### Agent System Architecture

```mermaid
graph TB
    Client[Frontend Client] --> Gateway[FastAPI Gateway]
    Gateway --> Coordinator[ADK Coordinator Agent]
    
    Coordinator --> YouTube[YouTube Analyzer]
    Coordinator --> Instagram[Instagram Analyzer]
    Coordinator --> Research[Research Agent]
    Coordinator --> Planner[Content Planner]
    
    YouTube --> YTApi[YouTube Data API]
    Instagram --> IGApi[Instagram Graph API]
    Research --> SearchApi[Google Search API]
    
    Coordinator --> Cache[Redis Cache]
    Coordinator --> DB[(Firestore)]
    Coordinator --> BigQuery[(BigQuery)]
```

### Agent Specifications

#### ADK Coordinator Agent
```python
class CoordinatorAgent:
    def __init__(self):
        self.sub_agents = {
            'youtube': YouTubeAnalyzer(),
            'instagram': InstagramAnalyzer(),
            'research': ResearchAgent(),
            'planner': ContentPlanner()
        }
    
    async def process_chat_message(
        self, 
        user_id: str, 
        message: str, 
        session_state: dict
    ) -> AsyncGenerator[str, None]:
        # Determine which agents to invoke
        # Coordinate multi-agent workflow
        # Stream composed response
        pass
```

#### Platform Analyzer Agents
```python
class YouTubeAnalyzer:
    async def analyze(self, channel_id: str, timeframe: str) -> AnalysisResult:
        """
        Returns:
        - View velocity metrics
        - Topic clustering from titles/tags
        - Posting cadence analysis
        - Performance predictions
        """
        pass

class InstagramAnalyzer:
    async def analyze(self, account_id: str, timeframe: str) -> AnalysisResult:
        """
        Returns:
        - Engagement rate trends
        - Optimal posting times
        - Content format performance
        - Audience demographics
        """
        pass
```

#### Research Agent
```python
class ResearchAgent:
    async def search_trends(self, query: str, platforms: List[str]) -> ResearchResult:
        """
        Uses Google Search Grounding to:
        - Find trending hashtags
        - Analyze competitor content
        - Fact-check content claims
        - Identify market opportunities
        """
        pass
```

### API Endpoints Reference

#### Authentication Endpoints
```python
@app.post("/auth/callback/{platform}")
async def oauth_callback(
    platform: str, 
    code: str, 
    state: str
) -> ConnectionResult:
    """Complete OAuth flow and store encrypted tokens"""
    pass

@app.post("/connect/youtube")
async def connect_youtube_readonly(
    channel_url: str
) -> ChannelInfo:
    """Connect YouTube channel via public URL/handle"""
    pass
```

#### Core Feature Endpoints
```python
@app.post("/chat")
async def chat_endpoint(
    request: ChatRequest
) -> StreamingResponse:
    """
    Body: { userId, message, sessionId? }
    Returns: Server-sent events stream
    """
    pass

@app.get("/profile")
async def get_profile(
    user_id: str,
    timeframe: str = "30d"
) -> ProfileData:
    """Returns cached platform summaries and KPIs"""
    pass

@app.post("/planner/generate")
async def generate_content_plan(
    request: PlannerRequest
) -> ContentPlan:
    """
    Params: timeframe, goals, platforms
    Returns: Structured content calendar
    """
    pass
```

### Data Models & Storage

#### Firestore Collections Structure
```python
# users collection
{
    "user_id": "uuid",
    "email": "<EMAIL>",
    "created_at": "timestamp",
    "subscription_tier": "free|pro",
    "preferences": {
        "default_timeframe": "30d",
        "notification_settings": {}
    }
}

# connections collection  
{
    "connection_id": "uuid",
    "user_id": "uuid",
    "platform": "youtube|instagram",
    "platform_user_id": "string",
    "handle": "string",
    "avatar_url": "string",
    "token_ref": "secret_manager_path",
    "last_sync": "timestamp",
    "status": "active|expired|revoked"
}

# snapshots collection (daily metrics)
{
    "snapshot_id": "uuid", 
    "connection_id": "uuid",
    "date": "date",
    "metrics": {
        "followers": 0,
        "engagement_rate": 0.0,
        "reach": 0,
        "impressions": 0
    },
    "derived_kpis": {
        "growth_velocity": 0.0,
        "engagement_trend": "up|down|stable"
    }
}
```

#### BigQuery Schema
```sql
-- metrics_daily table
CREATE TABLE metrics_daily (
    date DATE,
    user_id STRING,
    platform STRING,
    connection_id STRING,
    followers INT64,
    engagement_rate FLOAT64,
    reach INT64,
    impressions INT64,
    posts_count INT64,
    avg_engagement FLOAT64,
    created_at TIMESTAMP
);
```

### Business Logic Layer

#### Analytics Engine
```python
class AnalyticsEngine:
    async def calculate_health_score(
        self, 
        user_id: str, 
        timeframe: str
    ) -> HealthScore:
        """
        Aggregates cross-platform metrics into unified score:
        - Growth momentum (40%)
        - Engagement quality (35%) 
        - Content consistency (25%)
        """
        pass
    
    async def detect_anomalies(
        self, 
        metrics: List[Metric]
    ) -> List[Anomaly]:
        """
        Identifies unusual patterns:
        - Sudden engagement drops
        - Viral content spikes
        - Posting cadence changes
        """
        pass
```

#### Content Planning Engine
```python
class ContentPlanningEngine:
    async def generate_posting_schedule(
        self, 
        goals: PlanningGoals,
        platforms: List[str],
        timeframe: int
    ) -> ContentPlan:
        """
        Creates optimized posting calendar:
        - Best posting times per platform
        - Content type recommendations
        - Cross-platform content adaptation
        - Hashtag and keyword suggestions
        """
        pass
```

### Platform Integration Tools

#### YouTube Integration
```python
class YouTubeTools:
    async def get_channel_analytics(
        self, 
        channel_id: str, 
        days: int
    ) -> YouTubeMetrics:
        """
        API Calls:
        - channels.list (basic info)
        - search.list (recent videos)
        - videos.list (detailed metrics)
        
        Derived Insights:
        - Upload frequency patterns
        - Title/thumbnail optimization opportunities
        - Audience retention analysis
        """
        pass
    
    async def analyze_content_performance(
        self, 
        videos: List[Video]
    ) -> ContentInsights:
        """
        Returns:
        - Top performing content themes
        - Optimal video length recommendations
        - Best publishing time windows
        """
        pass
```

#### Instagram Integration  
```python
class InstagramTools:
    async def get_account_insights(
        self, 
        account_id: str, 
        days: int
    ) -> InstagramMetrics:
        """
        API Calls:
        - /{account_id}/media (posts)
        - /{account_id}/insights (account metrics)
        - /{media_id}/insights (post metrics)
        
        Handles 2025 API deprecations
        """
        pass
    
    async def analyze_engagement_patterns(
        self, 
        posts: List[InstagramPost]
    ) -> EngagementInsights:
        """
        Returns:
        - Optimal posting times
        - High-performing content formats
        - Hashtag effectiveness analysis
        """
        pass
```

## Data Flow Between Layers

### Chat Message Processing Flow
```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant Coordinator
    participant SubAgents
    participant APIs
    participant Database
    
    Client->>FastAPI: POST /chat {message}
    FastAPI->>Coordinator: process_message()
    
    Coordinator->>Database: get_user_connections()
    Database-->>Coordinator: connected_accounts[]
    
    par Parallel Agent Analysis
        Coordinator->>SubAgents: youtube.analyze()
        Coordinator->>SubAgents: instagram.analyze()
        Coordinator->>SubAgents: research.search_trends()
    end
    
    SubAgents->>APIs: platform_api_calls()
    APIs-->>SubAgents: metrics_data
    SubAgents-->>Coordinator: analysis_results[]
    
    Coordinator->>Database: cache_insights()
    Coordinator-->>FastAPI: stream_response()
    FastAPI-->>Client: SSE stream
```

### Account Connection Flow
```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant OAuth
    participant SecretManager
    participant Database
    
    Client->>FastAPI: POST /connect/{platform}
    FastAPI->>OAuth: initiate_oauth_flow()
    OAuth-->>Client: redirect_to_platform()
    
    Client->>OAuth: user_authorizes()
    OAuth->>FastAPI: callback_with_code()
    
    FastAPI->>OAuth: exchange_code_for_tokens()
    OAuth-->>FastAPI: access_token + refresh_token
    
    FastAPI->>SecretManager: store_encrypted_tokens()
    FastAPI->>Database: save_connection_record()
    
    FastAPI-->>Client: connection_success
```

## Testing Strategy

### Frontend Testing
```typescript
// Component Testing with React Testing Library
describe('ChatInterface', () => {
  it('should stream agent responses', async () => {
    const mockStream = createMockStream();
    render(<ChatInterface userId="test" />);
    
    await user.type(screen.getByRole('textbox'), 'How is my YouTube performing?');
    await user.click(screen.getByRole('button', { name: 'Send' }));
    
    await waitFor(() => {
      expect(screen.getByText(/analyzing your youtube metrics/i)).toBeInTheDocument();
    });
  });
});

// Integration Testing with MSW
const handlers = [
  rest.post('/api/chat', (req, res, ctx) => {
    return res(ctx.json({ message: 'Mock agent response' }));
  }),
];
```

### Backend Testing
```python
# Agent Tool Testing
@pytest.mark.asyncio
async def test_youtube_analyzer():
    analyzer = YouTubeAnalyzer()
    mock_client = MockYouTubeClient()
    
    result = await analyzer.analyze(
        channel_id="test_channel",
        timeframe="30d"
    )
    
    assert result.growth_rate > 0
    assert len(result.content_insights) > 0

# End-to-End Chat Testing  
@pytest.mark.asyncio
async def test_chat_flow_with_connected_accounts():
    # Setup user with connected YouTube + Instagram
    user_id = await create_test_user_with_accounts()
    
    response = await client.post("/chat", json={
        "userId": user_id,
        "message": "Analyze my recent performance"
    })
    
    assert response.status_code == 200
    # Verify response references both platforms
    content = await response.stream().read()
    assert "youtube" in content.lower()
    assert "instagram" in content.lower()
```

## Security Implementation

### Token Management
```python
class SecretManager:
    async def store_platform_tokens(
        self, 
        user_id: str, 
        platform: str, 
        tokens: OAuthTokens
    ) -> str:
        """
        Stores encrypted tokens in Google Secret Manager
        Returns: secret_path for reference
        """
        secret_name = f"user-{user_id}-{platform}-tokens"
        encrypted_data = self.encrypt_tokens(tokens)
        return await self.create_secret(secret_name, encrypted_data)
    
    async def refresh_expired_token(
        self, 
        connection_id: str
    ) -> Optional[str]:
        """
        Automatic token refresh with rotation
        """
        pass
```

### Rate Limiting & Backoff
```python
class PlatformClient:
    def __init__(self):
        self.rate_limiters = {
            'youtube': RateLimiter(max_calls=100, window=100),  # 100/100s
            'instagram': RateLimiter(max_calls=200, window=3600)  # 200/hour
        }
    
    async def make_api_call(self, platform: str, endpoint: str, params: dict):
        await self.rate_limiters[platform].acquire()
        
        try:
            return await self.http_client.request(endpoint, params)
        except RateLimitError as e:
            # Exponential backoff
            await asyncio.sleep(e.retry_after)
            return await self.make_api_call(platform, endpoint, params)
```

### Data Encryption
```python
# Firestore document-level encryption
class EncryptedDocument:
    def __init__(self, kms_key_name: str):
        self.kms_client = kms.KeyManagementServiceClient()
        self.key_name = kms_key_name
    
    def encrypt_field(self, plaintext: str) -> str:
        response = self.kms_client.encrypt(
            request={
                "name": self.key_name,
                "plaintext": plaintext.encode("utf-8")
            }
        )
        return base64.b64encode(response.ciphertext).decode("utf-8")

## Monitoring & Observability

### Logging Strategy
```python
import structlog
from google.cloud import logging as cloud_logging

# Structured logging for agent interactions
logger = structlog.get_logger()

class AgentLogger:
    def log_agent_call(self, agent_name: str, input_data: dict, output_data: dict):
        logger.info(
            "agent_execution",
            agent=agent_name,
            input_tokens=len(str(input_data)),
            output_tokens=len(str(output_data)),
            execution_time_ms=self.get_execution_time(),
            user_id=self.get_current_user_id()
        )
    
    def log_platform_api_call(self, platform: str, endpoint: str, status_code: int):
        logger.info(
            "platform_api_call",
            platform=platform,
            endpoint=endpoint,
            status_code=status_code,
            rate_limit_remaining=self.get_rate_limit_remaining(platform)
        )
```

### Error Monitoring
```python
from google.cloud import monitoring_v3

class ErrorMonitoring:
    def __init__(self):
        self.client = monitoring_v3.MetricServiceClient()
        self.project_name = f"projects/{PROJECT_ID}"
    
    def track_agent_errors(self, error_type: str, agent_name: str):
        """Track agent execution errors for alerting"""
        series = monitoring_v3.TimeSeries()
        series.metric.type = "custom.googleapis.com/agent/errors"
        series.metric.labels["error_type"] = error_type
        series.metric.labels["agent_name"] = agent_name
        
        # Send to Cloud Monitoring
        self.client.create_time_series(
            name=self.project_name,
            time_series=[series]
        )
    
    def track_platform_api_errors(self, platform: str, error_code: str):
        """Monitor platform API failures and rate limits"""
        pass
```

### Performance Metrics
```python
# Custom metrics for business KPIs
METRICS = {
    'chat_response_time': 'Time to generate agent response',
    'platform_sync_duration': 'Time to sync platform data',
    'user_engagement_score': 'User interaction with chat responses',
    'content_plan_generation_time': 'Time to generate content plans',
    'token_refresh_success_rate': 'OAuth token refresh success rate'
}
```

## Deployment Architecture

### Container Configuration
```dockerfile
# Frontend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]

# Backend Dockerfile  
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Cloud Run Configuration
```yaml
# frontend-service.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: social-media-frontend
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/memory: "512Mi"
        run.googleapis.com/cpu: "1"
    spec:
      containers:
      - image: gcr.io/PROJECT_ID/frontend:latest
        ports:
        - containerPort: 3000
        env:
        - name: BACKEND_URL
          value: "https://agents-service-url"
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: nextauth-secret

# backend-service.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: social-media-agents
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "20"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
    spec:
      containers:
      - image: gcr.io/PROJECT_ID/agents:latest
        ports:
        - containerPort: 8000
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: FIRESTORE_DATABASE
          value: "social-media-db"
```

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy to Cloud Run

on:
  push:
    branches: [main]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    
    - name: Build and push frontend
      run: |
        cd frontend
        gcloud builds submit --tag gcr.io/$GCP_PROJECT_ID/frontend:$GITHUB_SHA
    
    - name: Deploy to Cloud Run
      run: |
        gcloud run deploy social-media-frontend \
          --image gcr.io/$GCP_PROJECT_ID/frontend:$GITHUB_SHA \
          --region us-central1 \
          --allow-unauthenticated

  deploy-backend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build and push backend
      run: |
        cd backend
        gcloud builds submit --tag gcr.io/$GCP_PROJECT_ID/agents:$GITHUB_SHA
    
    - name: Deploy to Cloud Run
      run: |
        gcloud run deploy social-media-agents \
          --image gcr.io/$GCP_PROJECT_ID/agents:$GITHUB_SHA \
          --region us-central1 \
          --memory 2Gi \
          --cpu 2
```

## Configuration Management

### Environment Configuration
```bash
# .env.sample

# Frontend Environment
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000

# Backend Environment
GOOGLE_CLOUD_PROJECT=your-project-id
FIRESTORE_DATABASE=social-media-db
REDIS_URL=redis://localhost:6379

# OAuth Credentials
YOUTUBE_CLIENT_ID=your-youtube-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-client-secret
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret

# API Keys
GOOGLE_SEARCH_API_KEY=your-search-api-key
GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id

# ADK Configuration
VERTEX_AI_LOCATION=us-central1
AGENT_ENGINE_SESSION_TTL=3600
```

### OAuth Redirect URIs
```
# YouTube OAuth
Redirect URI: https://your-domain.com/api/auth/callback/youtube
Origin: https://your-domain.com

# Instagram OAuth  
Redirect URI: https://your-domain.com/api/auth/callback/instagram
Valid OAuth Redirect URIs: https://your-domain.com/api/auth/callback/instagram

# Development
Local Frontend: http://localhost:3000/api/auth/callback/{platform}
Local Backend: http://localhost:8000/auth/callback/{platform}
```

## Acceptance Criteria Validation

### Core Functionality Tests

#### 1. First Chat with Connected Accounts
```python
@pytest.mark.asyncio
async def test_first_chat_with_live_metrics():
    """
    Verify: First chat references live metrics (90 days) 
    and produces 3+ platform-specific actions
    """
    # Setup user with YouTube + Instagram accounts
    user_id = await setup_user_with_accounts()
    
    # Mock platform APIs with realistic data
    mock_youtube_data(days=90, growth_trend="positive")
    mock_instagram_data(days=90, engagement_trend="declining")
    
    response = await client.post("/chat", json={
        "userId": user_id,
        "message": "How are my social media accounts performing?"
    })
    
    content = await response.stream().read()
    
    # Assertions
    assert "90 days" in content or "last 3 months" in content
    assert "youtube" in content.lower()
    assert "instagram" in content.lower()
    
    # Count actionable recommendations
    actions = extract_action_items(content)
    assert len(actions) >= 3
    assert any("youtube" in action.lower() for action in actions)
    assert any("instagram" in action.lower() for action in actions)
```

#### 2. Engagement Analysis Query
```python
@pytest.mark.asyncio
async def test_instagram_engagement_analysis():
    """
    Verify: "Why is my IG engagement down?" returns:
    - Metric deltas
    - Suspected causes
    - 3 experiments with posting windows
    """
    user_id = await setup_user_with_instagram()
    
    # Mock declining engagement data
    mock_instagram_declining_engagement()
    
    response = await client.post("/chat", json={
        "userId": user_id,
        "message": "Why is my Instagram engagement down?"
    })
    
    content = await response.stream().read()
    
    # Check for metric deltas
    assert "engagement" in content.lower()
    assert "%" in content  # Percentage changes
    
    # Check for suspected causes
    causes = extract_suspected_causes(content)
    assert len(causes) > 0
    assert any(cause in ["format", "time", "topic"] for cause in causes)
    
    # Check for experiments
    experiments = extract_experiments(content)
    assert len(experiments) >= 3
    assert any("time" in exp.lower() for exp in experiments)
```

#### 3. Content Planning
```python
@pytest.mark.asyncio
async def test_content_planning_product_drop():
    """
    Verify: "Plan my next 2 weeks for a product drop" produces:
    - Calendar with 8-12 posts across platforms
    - Post briefs and best times
    - Working export functionality
    """
    user_id = await setup_user_with_multiple_platforms()
    
    response = await client.post("/planner/generate", json={
        "userId": user_id,
        "timeframe": "2w",
        "goals": "product launch",
        "context": "launching new product next month"
    })
    
    plan = response.json()
    
    # Check calendar structure
    assert len(plan["posts"]) >= 8
    assert len(plan["posts"]) <= 12
    
    # Verify cross-platform distribution
    platforms = set(post["platform"] for post in plan["posts"])
    assert len(platforms) >= 2
    
    # Check post brief completeness
    for post in plan["posts"]:
        assert post["title"]
        assert post["hook"]
        assert post["outline"]
        assert post["cta"]
        assert post["best_time"]
    
    # Test export functionality
    export_response = await client.post("/planner/export", json={
        "planId": plan["id"],
        "format": "csv"
    })
    assert export_response.status_code == 200
    assert export_response.headers["content-type"] == "text/csv"
```

#### 4. Cold Start Analysis
```python
@pytest.mark.asyncio
async def test_cold_start_youtube_analysis():
    """
    Verify: Users with <2 accounts get public YouTube analysis
    via channel URL/handle input
    """
    user_id = await setup_user_no_accounts()
    
    response = await client.post("/chat", json={
        "userId": user_id,
        "message": "Analyze this YouTube channel: https://youtube.com/@example"
    })
    
    content = await response.stream().read()
    
    # Should provide public analysis
    assert "subscribers" in content.lower()
    assert "views" in content.lower()
    assert "upload" in content.lower()
    
    # Should suggest connecting account
    assert "connect" in content.lower() or "link" in content.lower()
```

### Performance Benchmarks
```python
@pytest.mark.performance
async def test_chat_response_time():
    """Chat responses should be under 10 seconds"""
    start_time = time.time()
    
    response = await client.post("/chat", json={
        "userId": "test_user",
        "message": "Analyze my performance"
    })
    
    # First chunk should arrive quickly
    first_chunk_time = time.time()
    first_chunk = await response.stream().read(100)
    
    assert first_chunk_time - start_time < 3.0  # First response in 3s
    
    # Complete response should finish in reasonable time
    complete_response = await response.stream().read()
    total_time = time.time() - start_time
    
    assert total_time < 10.0  # Complete response in 10s

@pytest.mark.performance
async def test_platform_sync_performance():
    """Platform data sync should complete within limits"""
    start_time = time.time()
    
    await sync_platform_data("youtube", "test_channel", days=90)
    
    sync_time = time.time() - start_time
    assert sync_time < 30.0  # Sync within 30 seconds
```
```