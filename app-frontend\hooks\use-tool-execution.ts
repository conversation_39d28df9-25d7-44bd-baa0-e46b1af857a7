/**
 * Tool Execution Hook
 * 
 * React hook for managing tool execution state and processing ADK events
 * to extract and display function calls and tool usage information.
 * 
 * Requirements covered: 4.5, 10.2
 */

import { useState, useCallback, useEffect } from 'react';
import { 
  ADKEvent, 
  FunctionCall, 
  FunctionResponse, 
  ToolExecution,
  EnhancedChatMessage 
} from '@/types/adk';
import {
  extractFunctionCalls,
  extractFunctionResponses,
  createToolExecutions,
  hasToolUsage,
  getToolUsageSummary
} from '@/lib/adk-event-parser';

export interface UseToolExecutionOptions {
  autoExpand?: boolean;
  showDetailModal?: boolean;
  onToolExecutionUpdate?: (executions: ToolExecution[]) => void;
  onError?: (error: Error) => void;
}

export interface UseToolExecutionReturn {
  toolExecutions: ToolExecution[];
  functionCalls: FunctionCall[];
  functionResponses: FunctionResponse[];
  hasTools: boolean;
  isExpanded: boolean;
  processingCount: number;
  completedCount: number;
  failedCount: number;
  toggleExpanded: () => void;
  setExpanded: (expanded: boolean) => void;
  processEvent: (event: ADKEvent) => void;
  processMessage: (message: EnhancedChatMessage) => void;
  updateExecution: (executionId: string, updates: Partial<ToolExecution>) => void;
  clearExecutions: () => void;
  getExecutionById: (id: string) => ToolExecution | undefined;
  getExecutionByName: (name: string) => ToolExecution | undefined;
}

export function useToolExecution(options: UseToolExecutionOptions = {}): UseToolExecutionReturn {
  const {
    autoExpand = false,
    showDetailModal = true,
    onToolExecutionUpdate,
    onError
  } = options;

  const [toolExecutions, setToolExecutions] = useState<ToolExecution[]>([]);
  const [functionCalls, setFunctionCalls] = useState<FunctionCall[]>([]);
  const [functionResponses, setFunctionResponses] = useState<FunctionResponse[]>([]);
  const [isExpanded, setIsExpanded] = useState(autoExpand);

  // Derived state
  const hasTools = toolExecutions.length > 0 || functionCalls.length > 0;
  const processingCount = toolExecutions.filter(exec => 
    exec.status === 'running' || exec.status === 'pending'
  ).length;
  const completedCount = toolExecutions.filter(exec => exec.status === 'completed').length;
  const failedCount = toolExecutions.filter(exec => exec.status === 'failed').length;

  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  const setExpanded = useCallback((expanded: boolean) => {
    setIsExpanded(expanded);
  }, []);

  const processEvent = useCallback((event: ADKEvent) => {
    try {
      if (!hasToolUsage(event)) {
        return;
      }

      const newFunctionCalls = extractFunctionCalls(event);
      const newFunctionResponses = extractFunctionResponses(event);
      const newToolExecutions = createToolExecutions(newFunctionCalls, newFunctionResponses, event);

      // Update function calls (merge with existing)
      setFunctionCalls(prev => {
        const merged = [...prev];
        newFunctionCalls.forEach(newCall => {
          const existingIndex = merged.findIndex(call => 
            call.id === newCall.id || (call.name === newCall.name && !call.id && !newCall.id)
          );
          if (existingIndex >= 0) {
            merged[existingIndex] = newCall;
          } else {
            merged.push(newCall);
          }
        });
        return merged;
      });

      // Update function responses (merge with existing)
      setFunctionResponses(prev => {
        const merged = [...prev];
        newFunctionResponses.forEach(newResponse => {
          const existingIndex = merged.findIndex(response => 
            response.id === newResponse.id || 
            (response.name === newResponse.name && !response.id && !newResponse.id)
          );
          if (existingIndex >= 0) {
            merged[existingIndex] = newResponse;
          } else {
            merged.push(newResponse);
          }
        });
        return merged;
      });

      // Update tool executions (merge with existing)
      setToolExecutions(prev => {
        const merged = [...prev];
        newToolExecutions.forEach(newExecution => {
          const existingIndex = merged.findIndex(exec => 
            exec.id === newExecution.id || 
            (exec.name === newExecution.name && !exec.id && !newExecution.id)
          );
          if (existingIndex >= 0) {
            // Update existing execution with new data
            merged[existingIndex] = {
              ...merged[existingIndex],
              ...newExecution,
              // Preserve duration if it was already calculated
              duration_ms: merged[existingIndex].duration_ms || newExecution.duration_ms
            };
          } else {
            merged.push(newExecution);
          }
        });
        return merged;
      });

      // Auto-expand if there are new tools and autoExpand is enabled
      if (autoExpand && newToolExecutions.length > 0) {
        setIsExpanded(true);
      }

    } catch (error) {
      console.error('Error processing ADK event for tool execution:', error);
      onError?.(error as Error);
    }
  }, [autoExpand, onError]);

  const processMessage = useCallback((message: EnhancedChatMessage) => {
    try {
      // Extract data from message metadata
      const messageFunctionCalls = message.function_calls || [];
      const messageFunctionResponses = message.metadata?.function_responses || [];
      const messageToolExecutions = message.metadata?.tool_executions || [];

      setFunctionCalls(messageFunctionCalls);
      setFunctionResponses(messageFunctionResponses);
      setToolExecutions(messageToolExecutions);

      // Auto-expand if there are tools and autoExpand is enabled
      if (autoExpand && (messageFunctionCalls.length > 0 || messageToolExecutions.length > 0)) {
        setIsExpanded(true);
      }

    } catch (error) {
      console.error('Error processing message for tool execution:', error);
      onError?.(error as Error);
    }
  }, [autoExpand, onError]);

  const updateExecution = useCallback((executionId: string, updates: Partial<ToolExecution>) => {
    setToolExecutions(prev => 
      prev.map(exec => 
        exec.id === executionId 
          ? { ...exec, ...updates }
          : exec
      )
    );
  }, []);

  const clearExecutions = useCallback(() => {
    setToolExecutions([]);
    setFunctionCalls([]);
    setFunctionResponses([]);
    setIsExpanded(autoExpand);
  }, [autoExpand]);

  const getExecutionById = useCallback((id: string) => {
    return toolExecutions.find(exec => exec.id === id);
  }, [toolExecutions]);

  const getExecutionByName = useCallback((name: string) => {
    return toolExecutions.find(exec => exec.name === name);
  }, [toolExecutions]);

  // Notify parent component of tool execution updates
  useEffect(() => {
    if (onToolExecutionUpdate) {
      onToolExecutionUpdate(toolExecutions);
    }
  }, [toolExecutions, onToolExecutionUpdate]);

  return {
    toolExecutions,
    functionCalls,
    functionResponses,
    hasTools,
    isExpanded,
    processingCount,
    completedCount,
    failedCount,
    toggleExpanded,
    setExpanded,
    processEvent,
    processMessage,
    updateExecution,
    clearExecutions,
    getExecutionById,
    getExecutionByName
  };
}

/**
 * Hook for managing a single tool execution detail view
 */
export interface UseToolExecutionDetailOptions {
  execution?: ToolExecution;
  functionCall?: FunctionCall;
  functionResponse?: FunctionResponse;
  onClose?: () => void;
  onCopy?: (content: string, type: string) => void;
  onExport?: (execution: ToolExecution) => void;
}

export interface UseToolExecutionDetailReturn {
  isOpen: boolean;
  activeTab: string;
  copiedSection: string | null;
  open: (execution: ToolExecution, functionCall?: FunctionCall, functionResponse?: FunctionResponse) => void;
  close: () => void;
  setActiveTab: (tab: string) => void;
  handleCopy: (content: string, type: string) => Promise<void>;
  handleExport: () => void;
}

export function useToolExecutionDetail(
  options: UseToolExecutionDetailOptions = {}
): UseToolExecutionDetailReturn {
  const { onClose, onCopy, onExport } = options;

  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedSection, setCopiedSection] = useState<string | null>(null);
  const [currentExecution, setCurrentExecution] = useState<ToolExecution | undefined>(options.execution);
  const [currentFunctionCall, setCurrentFunctionCall] = useState<FunctionCall | undefined>(options.functionCall);
  const [currentFunctionResponse, setCurrentFunctionResponse] = useState<FunctionResponse | undefined>(options.functionResponse);

  const open = useCallback((
    execution: ToolExecution, 
    functionCall?: FunctionCall, 
    functionResponse?: FunctionResponse
  ) => {
    setCurrentExecution(execution);
    setCurrentFunctionCall(functionCall);
    setCurrentFunctionResponse(functionResponse);
    setIsOpen(true);
    setActiveTab('overview');
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
    setCurrentExecution(undefined);
    setCurrentFunctionCall(undefined);
    setCurrentFunctionResponse(undefined);
    onClose?.();
  }, [onClose]);

  const handleCopy = useCallback(async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedSection(type);
      setTimeout(() => setCopiedSection(null), 2000);
      onCopy?.(content, type);
    } catch (error) {
      console.error('Failed to copy content:', error);
    }
  }, [onCopy]);

  const handleExport = useCallback(() => {
    if (currentExecution && onExport) {
      onExport(currentExecution);
    }
  }, [currentExecution, onExport]);

  return {
    isOpen,
    activeTab,
    copiedSection,
    open,
    close,
    setActiveTab,
    handleCopy,
    handleExport
  };
}