import { test, expect } from '@playwright/test';

test.describe('Complete User Journey E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up comprehensive API mocking for the full journey
    
    // Mock authentication
    await page.route('**/api/auth/**', async (route) => {
      const url = route.request().url();
      if (url.includes('/login')) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            access_token: 'mock-token',
            user: { id: 'user-1', email: '<EMAIL>' }
          })
        });
      }
    });

    // Mock empty state initially
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ status: 'success', data: [] })
      });
    });

    // Mock platform connection
    await page.route('**/api/auth/connect/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          auth_url: 'https://mock-oauth.example.com/authorize'
        })
      });
    });

    // Mock OAuth callback
    await page.route('**/api/auth/callback/**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          account: {
            id: 'yt-connected',
            platform: 'youtube',
            handle: '@newuser',
            connected: true
          }
        })
      });
    });

    await page.goto('/');
  });

  test('complete user onboarding journey', async ({ page }) => {
    // Step 1: Landing on empty dashboard
    await expect(page.getByText('Welcome to your Social Media Manager')).toBeVisible();

    // Step 2: Try to access profile - should show empty state
    await page.goto('/profile');
    await expect(page.getByText('No Connected Accounts')).toBeVisible();
    await expect(page.getByText(/Connect your social media accounts/)).toBeVisible();

    // Step 3: Navigate to connections
    const connectButton = page.getByRole('button', { name: /connect your first account/i });
    await connectButton.click();
    await page.waitForURL('**/connections');

    // Step 4: Open connection modal
    const addConnectionButton = page.getByRole('button', { name: /connect/i }).first();
    if (await addConnectionButton.isVisible()) {
      await addConnectionButton.click();
      await expect(page.getByText('Connect Your Accounts')).toBeVisible();
    }

    // Step 5: Connect YouTube
    const youtubeConnect = page.getByRole('button', { name: 'Connect' }).first();
    await youtubeConnect.click();
    await expect(page.getByText('Connecting...')).toBeVisible();

    // Simulate successful connection
    await page.waitForTimeout(2000);
  });

  test('user journey from chat to analysis to planning', async ({ page }) => {
    // Mock connected account for this journey
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: [{
            id: 'yt-1',
            platform: 'youtube',
            handle: '@journeyuser',
            metrics: {
              followers: 10000,
              engagement: 4.5,
              growth: { followers: 8.2 }
            }
          }]
        })
      });
    });

    // Mock chat API with contextual responses
    await page.route('**/api/chat', async (route) => {
      const body = JSON.parse(route.request().postData() || '{}');
      let response = '';
      
      if (body.message.toLowerCase().includes('youtube')) {
        response = 'Based on your YouTube analytics, you have 10K followers with 4.5% engagement rate. Your growth is strong at 8.2%. Would you like me to create a content plan to maintain this momentum?';
      } else if (body.message.toLowerCase().includes('content plan')) {
        response = 'I can help you create a content plan! Based on your YouTube performance, I recommend focusing on educational content. Should I generate a detailed plan for the next 2 weeks?';
      } else {
        response = 'Hello! I can analyze your social media performance and help create content plans. What would you like to know?';
      }

      const chunks = response.split(' ').map(word => `data: {"content": "${word} "}\n\n`);
      chunks.push('data: {"done": true}\n\n');

      const stream = new ReadableStream({
        start(controller) {
          let index = 0;
          const interval = setInterval(() => {
            if (index < chunks.length) {
              controller.enqueue(new TextEncoder().encode(chunks[index++]));
            } else {
              clearInterval(interval);
              controller.close();
            }
          }, 50);
        }
      });

      await route.fulfill({
        status: 200,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' },
        body: stream,
      });
    });

    // Start from chat
    await page.goto('/');
    
    // Step 1: Ask about YouTube performance
    const messageInput = page.getByRole('textbox');
    await messageInput.fill('How is my YouTube performance?');
    await messageInput.press('Enter');

    // Wait for AI response
    await expect(page.getByText(/Based on your YouTube analytics/)).toBeVisible({ timeout: 10000 });

    // Step 2: Navigate to profile to see detailed analytics
    await page.goto('/profile');
    await expect(page.getByText('@journeyuser')).toBeVisible();
    await expect(page.getByText('10.0K')).toBeVisible(); // Followers

    // Step 3: Ask for content plan in chat
    await page.goto('/');
    await messageInput.fill('Create a content plan for me');
    await messageInput.press('Enter');

    // Wait for response about content planning
    await expect(page.getByText(/I can help you create a content plan/)).toBeVisible({ timeout: 10000 });

    // Step 4: Navigate to content planner
    await page.goto('/planner');
    
    // Generate plan if modal is available
    const generateButton = page.getByRole('button', { name: /generate.*plan/i }).first();
    if (await generateButton.isVisible()) {
      await generateButton.click();
      
      // Complete plan generation quickly
      if (await page.getByText('14 Days').isVisible()) {
        await page.getByText('14 Days').click();
        await page.getByText('YouTube').click();
        await page.getByRole('button', { name: /next.*goals/i }).click();
        await page.getByText('Increase followers').click();
        await page.getByRole('button', { name: /next/i }).click();
        await page.getByText('Educational tutorials').click();
        
        const finalGenerate = page.getByRole('button', { name: /generate plan/i });
        if (await finalGenerate.isVisible()) {
          await finalGenerate.click();
        }
      }
    }
  });

  test('responsive user journey across devices', async ({ page }) => {
    // Test the journey on different viewport sizes
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1920, height: 1080, name: 'Desktop' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      // Navigate through key pages
      await page.goto('/');
      await expect(page.getByText('Welcome to your Social Media Manager')).toBeVisible();
      
      await page.goto('/profile');
      await expect(page.locator('body')).toBeVisible(); // Basic check page loads
      
      await page.goto('/planner');
      await expect(page.locator('body')).toBeVisible();
      
      // Test chat functionality
      await page.goto('/');
      const messageInput = page.getByRole('textbox');
      if (await messageInput.isVisible()) {
        await messageInput.fill(`Test on ${viewport.name}`);
        const sendButton = page.getByRole('button', { name: /send/i });
        if (await sendButton.isVisible()) {
          await sendButton.click();
          await expect(page.getByText(`Test on ${viewport.name}`)).toBeVisible();
        }
      }
    }
  });

  test('error recovery journey', async ({ page }) => {
    // Test how users can recover from various error states
    
    // Step 1: API failure scenario
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Server error' })
      });
    });

    await page.goto('/profile');
    
    // Page should handle error gracefully
    await expect(page.locator('body')).toBeVisible();
    
    // Step 2: Network recovery
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: [{
            id: 'yt-recovery',
            platform: 'youtube',
            handle: '@recovered',
            metrics: { followers: 5000 }
          }]
        })
      });
    });

    // Refresh and check recovery
    await page.reload();
    await expect(page.getByText('@recovered')).toBeVisible({ timeout: 10000 });
  });

  test('accessibility compliance throughout journey', async ({ page }) => {
    // Test keyboard navigation
    await page.goto('/');
    
    // Should be able to navigate with keyboard
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Check for proper focus indicators
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Test on different pages
    const pages = ['/', '/profile', '/planner', '/connections'];
    
    for (const path of pages) {
      await page.goto(path);
      
      // Check for heading structure
      const headings = page.locator('h1, h2, h3');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0); // Should have headings
      
      // Check for alt text on images
      const images = page.locator('img');
      const imageCount = await images.count();
      
      if (imageCount > 0) {
        // All images should have alt text or be decorative
        for (let i = 0; i < imageCount; i++) {
          const img = images.nth(i);
          const alt = await img.getAttribute('alt');
          const role = await img.getAttribute('role');
          
          // Should have alt text or be marked as decorative
          expect(alt !== null || role === 'presentation').toBeTruthy();
        }
      }
    }
  });

  test('performance validation during user journey', async ({ page }) => {
    // Monitor page load times
    const startTime = Date.now();
    
    await page.goto('/');
    const homeLoadTime = Date.now() - startTime;
    expect(homeLoadTime).toBeLessThan(5000); // Should load within 5 seconds
    
    // Test navigation performance
    const navStart = Date.now();
    await page.goto('/profile');
    const profileLoadTime = Date.now() - navStart;
    expect(profileLoadTime).toBeLessThan(3000); // Subsequent navigations should be faster
    
    // Test chat response time
    const messageInput = page.getByRole('textbox');
    if (await messageInput.isVisible()) {
      const chatStart = Date.now();
      await messageInput.fill('Performance test');
      await messageInput.press('Enter');
      
      await expect(page.getByText('Performance test')).toBeVisible();
      const chatResponseTime = Date.now() - chatStart;
      expect(chatResponseTime).toBeLessThan(2000); // Chat should be responsive
    }
  });

  test('data persistence across browser sessions', async ({ page, context }) => {
    // Mock login and save state
    await page.goto('/');
    
    // Simulate user login (if authentication is implemented)
    await page.evaluate(() => {
      localStorage.setItem('user_session', JSON.stringify({
        userId: 'test-user',
        token: 'mock-token'
      }));
    });

    // Send a chat message
    const messageInput = page.getByRole('textbox');
    if (await messageInput.isVisible()) {
      await messageInput.fill('Persistent message');
      await messageInput.press('Enter');
      await expect(page.getByText('Persistent message')).toBeVisible();
    }

    // Create new context (simulate new browser session)
    const newContext = await page.context().browser()?.newContext();
    if (newContext) {
      const newPage = await newContext.newPage();
      
      // Set up same mocks
      await newPage.route('**/api/**', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ status: 'success', data: [] })
        });
      });

      await newPage.goto('/');
      
      // Check if session persisted (implementation dependent)
      await expect(newPage.locator('body')).toBeVisible();
      
      await newContext.close();
    }
  });

  test('social sharing and export functionality', async ({ page }) => {
    // Mock content that can be shared
    await page.route('**/api/plans', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: [{
            id: 'share-plan',
            title: 'Shareable Content Plan',
            posts: [{ title: 'Sample Post', platform: 'youtube' }]
          }]
        })
      });
    });

    await page.goto('/planner');
    
    // Look for share or export functionality
    const shareButton = page.getByRole('button', { name: /share/i }).or(
      page.getByRole('button', { name: /export/i })
    );

    if (await shareButton.isVisible()) {
      await shareButton.click();
      
      // Should show sharing options
      await expect(page.locator('body')).toBeVisible(); // Basic check
    }
  });

  test('multi-platform content creation workflow', async ({ page }) => {
    // Mock multiple connected platforms
    await page.route('**/api/accounts', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'success',
          data: [
            { id: 'yt-1', platform: 'youtube', handle: '@user_youtube' },
            { id: 'ig-1', platform: 'instagram', handle: '@user_instagram' }
          ]
        })
      });
    });

    // Journey through multi-platform planning
    await page.goto('/profile');
    
    // Should see multiple platforms
    await expect(page.getByText('@user_youtube')).toBeVisible();
    await expect(page.getByText('@user_instagram')).toBeVisible();
    
    // Go to planner and create cross-platform content
    await page.goto('/planner');
    
    const generateButton = page.getByRole('button', { name: /generate.*plan/i }).first();
    if (await generateButton.isVisible()) {
      await generateButton.click();
      
      if (await page.getByText('YouTube').isVisible()) {
        // Select multiple platforms
        await page.getByText('YouTube').click();
        await page.getByText('Instagram').click();
        
        // Continue workflow
        const nextButton = page.getByRole('button', { name: /next/i });
        if (await nextButton.isVisible()) {
          await nextButton.click();
        }
      }
    }
  });
});