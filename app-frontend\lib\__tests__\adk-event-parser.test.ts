/**
 * ADK Event Parser Tests
 * 
 * Tests for parsing ADK Event objects and extracting function calls,
 * function responses, and tool execution information.
 * 
 * Requirements covered: 10.1, 10.2, 4.5
 */

import {
  extractFunctionCalls,
  extractFunctionResponses,
  extractTextContent,
  extractBinaryData,
  createToolExecutions,
  transformEventToMessage,
  hasToolUsage,
  getToolUsageSummary,
  parseStreamingChunk,
  mergeStreamingEvents,
  validateADKEvent,
  extractProcessingTime
} from '../adk-event-parser';
import { ADKEvent, FunctionCall, FunctionResponse, ToolExecution } from '@/types/adk';

describe('ADK Event Parser', () => {
  const mockADKEvent: ADKEvent = {
    author: 'test_agent',
    invocation_id: 'inv_123',
    content: {
      role: 'model',
      parts: [
        { text: 'I will search for information.' },
        {
          function_call: {
            id: 'call_1',
            name: 'search_web',
            arguments: { query: 'test search', limit: 10 }
          }
        },
        { text: ' Here are the results:' },
        {
          function_response: {
            id: 'call_1',
            name: 'search_web',
            content: 'Found 5 results',
            success: true
          }
        },
        { text: ' The search was successful.' }
      ]
    },
    interrupted: false,
    turn_complete: true,
    long_running_tool_ids: [],
    metadata: {
      processing_time_ms: 2500
    }
  };

  describe('extractFunctionCalls', () => {
    it('extracts function calls from event content parts', () => {
      const functionCalls = extractFunctionCalls(mockADKEvent);
      
      expect(functionCalls).toHaveLength(1);
      expect(functionCalls[0]).toEqual({
        id: 'call_1',
        name: 'search_web',
        arguments: { query: 'test search', limit: 10 }
      });
    });

    it('returns empty array when no function calls present', () => {
      const eventWithoutCalls: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [{ text: 'Just text content' }]
        }
      };

      const functionCalls = extractFunctionCalls(eventWithoutCalls);
      expect(functionCalls).toHaveLength(0);
    });

    it('handles event without content', () => {
      const eventWithoutContent: ADKEvent = {
        ...mockADKEvent,
        content: undefined
      };

      const functionCalls = extractFunctionCalls(eventWithoutContent);
      expect(functionCalls).toHaveLength(0);
    });

    it('generates IDs for function calls without IDs', () => {
      const eventWithoutIds: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [
            {
              function_call: {
                name: 'test_function',
                arguments: { test: 'value' }
              }
            }
          ]
        }
      };

      const functionCalls = extractFunctionCalls(eventWithoutIds);
      expect(functionCalls).toHaveLength(1);
      expect(functionCalls[0].id).toBe('call_0');
      expect(functionCalls[0].name).toBe('test_function');
    });
  });

  describe('extractFunctionResponses', () => {
    it('extracts function responses from event content parts', () => {
      const functionResponses = extractFunctionResponses(mockADKEvent);
      
      expect(functionResponses).toHaveLength(1);
      expect(functionResponses[0]).toEqual({
        id: 'call_1',
        name: 'search_web',
        content: 'Found 5 results',
        success: true,
        error: undefined
      });
    });

    it('handles failed function responses', () => {
      const eventWithFailure: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [
            {
              function_response: {
                id: 'call_1',
                name: 'search_web',
                content: '',
                error: 'Network timeout'
              }
            }
          ]
        }
      };

      const functionResponses = extractFunctionResponses(eventWithFailure);
      expect(functionResponses).toHaveLength(1);
      expect(functionResponses[0].success).toBe(false);
      expect(functionResponses[0].error).toBe('Network timeout');
    });

    it('returns empty array when no function responses present', () => {
      const eventWithoutResponses: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [{ text: 'Just text content' }]
        }
      };

      const functionResponses = extractFunctionResponses(eventWithoutResponses);
      expect(functionResponses).toHaveLength(0);
    });
  });

  describe('extractTextContent', () => {
    it('extracts and combines text content from all parts', () => {
      const textContent = extractTextContent(mockADKEvent);
      
      expect(textContent).toBe('I will search for information. Here are the results: The search was successful.');
    });

    it('returns empty string when no text parts present', () => {
      const eventWithoutText: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [
            {
              function_call: {
                name: 'test_function',
                arguments: {}
              }
            }
          ]
        }
      };

      const textContent = extractTextContent(eventWithoutText);
      expect(textContent).toBe('');
    });

    it('handles event without content', () => {
      const eventWithoutContent: ADKEvent = {
        ...mockADKEvent,
        content: undefined
      };

      const textContent = extractTextContent(eventWithoutContent);
      expect(textContent).toBe('');
    });
  });

  describe('extractBinaryData', () => {
    it('extracts binary data from inline_data parts', () => {
      const eventWithBinary: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [
            { text: 'Here is an image:' },
            {
              inline_data: {
                mime_type: 'image/png',
                data: 'base64encodeddata'
              }
            }
          ]
        }
      };

      const binaryData = extractBinaryData(eventWithBinary);
      expect(binaryData).toHaveLength(1);
      expect(binaryData[0]).toEqual({
        mime_type: 'image/png',
        data: 'base64encodeddata'
      });
    });

    it('returns empty array when no binary data present', () => {
      const binaryData = extractBinaryData(mockADKEvent);
      expect(binaryData).toHaveLength(0);
    });
  });

  describe('createToolExecutions', () => {
    it('creates tool executions from function calls and responses', () => {
      const functionCalls = extractFunctionCalls(mockADKEvent);
      const functionResponses = extractFunctionResponses(mockADKEvent);
      
      const toolExecutions = createToolExecutions(functionCalls, functionResponses, mockADKEvent);
      
      expect(toolExecutions).toHaveLength(1);
      expect(toolExecutions[0]).toMatchObject({
        id: 'call_1',
        name: 'search_web',
        status: 'completed',
        input: { query: 'test search', limit: 10 },
        output: 'Found 5 results',
        error: undefined
      });
    });

    it('handles failed executions', () => {
      const functionCalls: FunctionCall[] = [
        { id: 'call_1', name: 'test_function', arguments: {} }
      ];
      const functionResponses: FunctionResponse[] = [
        { id: 'call_1', name: 'test_function', content: '', success: false, error: 'Test error' }
      ];

      const toolExecutions = createToolExecutions(functionCalls, functionResponses, mockADKEvent);
      
      expect(toolExecutions[0].status).toBe('failed');
      expect(toolExecutions[0].error).toBe('Test error');
    });

    it('handles long running tools', () => {
      const eventWithLongRunning: ADKEvent = {
        ...mockADKEvent,
        long_running_tool_ids: ['call_1']
      };

      const functionCalls: FunctionCall[] = [
        { id: 'call_1', name: 'long_function', arguments: {} }
      ];

      const toolExecutions = createToolExecutions(functionCalls, [], eventWithLongRunning);
      
      expect(toolExecutions[0].status).toBe('running');
    });

    it('handles pending executions', () => {
      const functionCalls: FunctionCall[] = [
        { id: 'call_1', name: 'pending_function', arguments: {} }
      ];

      const toolExecutions = createToolExecutions(functionCalls, [], mockADKEvent);
      
      expect(toolExecutions[0].status).toBe('pending');
    });
  });

  describe('transformEventToMessage', () => {
    it('transforms ADK event to enhanced chat message', () => {
      const message = transformEventToMessage(mockADKEvent, 'session_123', 'user_456', 'test_agent');
      
      expect(message).toMatchObject({
        id: 'inv_123',
        session_id: 'session_123',
        role: 'model',
        content: 'I will search for information. Here are the results: The search was successful.',
        user_id: 'user_456',
        agent_name: 'test_agent',
        adk_invocation_id: 'inv_123',
        interrupted: false
      });

      expect(message.function_calls).toHaveLength(1);
      expect(message.metadata?.function_responses).toHaveLength(1);
      expect(message.metadata?.tool_executions).toHaveLength(1);
    });

    it('handles event without function calls', () => {
      const simpleEvent: ADKEvent = {
        author: 'agent',
        invocation_id: 'inv_456',
        content: {
          role: 'model',
          parts: [{ text: 'Simple response' }]
        },
        interrupted: false,
        turn_complete: true
      };

      const message = transformEventToMessage(simpleEvent, 'session_123', 'user_456');
      
      expect(message.content).toBe('Simple response');
      expect(message.function_calls).toBeUndefined();
      expect(message.metadata?.function_responses).toBeUndefined();
      expect(message.metadata?.tool_executions).toBeUndefined();
    });
  });

  describe('hasToolUsage', () => {
    it('returns true when event has function calls', () => {
      expect(hasToolUsage(mockADKEvent)).toBe(true);
    });

    it('returns false when event has no tool usage', () => {
      const simpleEvent: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [{ text: 'Simple response' }]
        }
      };

      expect(hasToolUsage(simpleEvent)).toBe(false);
    });

    it('returns false when event has no content', () => {
      const eventWithoutContent: ADKEvent = {
        ...mockADKEvent,
        content: undefined
      };

      expect(hasToolUsage(eventWithoutContent)).toBe(false);
    });
  });

  describe('getToolUsageSummary', () => {
    it('returns correct tool usage summary', () => {
      const summary = getToolUsageSummary(mockADKEvent);
      
      expect(summary).toEqual({
        functionCallCount: 1,
        functionResponseCount: 1,
        longRunningToolCount: 0,
        hasErrors: false
      });
    });

    it('detects errors in function responses', () => {
      const eventWithError: ADKEvent = {
        ...mockADKEvent,
        content: {
          role: 'model',
          parts: [
            {
              function_response: {
                id: 'call_1',
                name: 'test_function',
                content: '',
                error: 'Test error'
              }
            }
          ]
        }
      };

      const summary = getToolUsageSummary(eventWithError);
      expect(summary.hasErrors).toBe(true);
    });

    it('counts long running tools', () => {
      const eventWithLongRunning: ADKEvent = {
        ...mockADKEvent,
        long_running_tool_ids: ['tool_1', 'tool_2']
      };

      const summary = getToolUsageSummary(eventWithLongRunning);
      expect(summary.longRunningToolCount).toBe(2);
    });
  });

  describe('parseStreamingChunk', () => {
    it('parses valid SSE event data', () => {
      const eventData = 'data: {"author":"agent","content":{"role":"model","parts":[{"text":"Hello"}]},"interrupted":false,"turn_complete":false}';
      
      const parsed = parseStreamingChunk(eventData);
      
      expect(parsed).not.toBeNull();
      expect(parsed?.author).toBe('agent');
      expect(parsed?.content?.parts[0].text).toBe('Hello');
    });

    it('handles data without "data: " prefix', () => {
      const eventData = '{"author":"agent","content":{"role":"model","parts":[{"text":"Hello"}]},"interrupted":false,"turn_complete":false}';
      
      const parsed = parseStreamingChunk(eventData);
      
      expect(parsed).not.toBeNull();
      expect(parsed?.author).toBe('agent');
    });

    it('returns null for invalid JSON', () => {
      const eventData = 'data: invalid json';
      
      const parsed = parseStreamingChunk(eventData);
      
      expect(parsed).toBeNull();
    });

    it('returns null for empty or [DONE] data', () => {
      expect(parseStreamingChunk('')).toBeNull();
      expect(parseStreamingChunk('data: ')).toBeNull();
      expect(parseStreamingChunk('data: [DONE]')).toBeNull();
    });

    it('validates ADK event structure', () => {
      const invalidEvent = 'data: {"some":"data","but":"not","adk":"event"}';
      
      const parsed = parseStreamingChunk(invalidEvent);
      
      expect(parsed).toBeNull();
    });
  });

  describe('mergeStreamingEvents', () => {
    it('merges multiple streaming events into single message', () => {
      const events: ADKEvent[] = [
        {
          author: 'agent',
          invocation_id: 'inv_123',
          content: {
            role: 'model',
            parts: [{ text: 'Hello' }]
          },
          interrupted: false,
          turn_complete: false
        },
        {
          author: 'agent',
          invocation_id: 'inv_123',
          content: {
            role: 'model',
            parts: [{ text: ' world!' }]
          },
          interrupted: false,
          turn_complete: true
        }
      ];

      const merged = mergeStreamingEvents(events, 'session_123', 'user_456', 'agent');
      
      expect(merged.content).toBe('Hello world!');
      expect(merged.metadata?.event_count).toBe(2);
      expect(merged.metadata?.turn_complete).toBe(true);
    });

    it('combines function calls from multiple events', () => {
      const events: ADKEvent[] = [
        {
          author: 'agent',
          invocation_id: 'inv_123',
          content: {
            role: 'model',
            parts: [
              {
                function_call: {
                  id: 'call_1',
                  name: 'function_1',
                  arguments: {}
                }
              }
            ]
          },
          interrupted: false,
          turn_complete: false
        },
        {
          author: 'agent',
          invocation_id: 'inv_123',
          content: {
            role: 'model',
            parts: [
              {
                function_call: {
                  id: 'call_2',
                  name: 'function_2',
                  arguments: {}
                }
              }
            ]
          },
          interrupted: false,
          turn_complete: true
        }
      ];

      const merged = mergeStreamingEvents(events, 'session_123', 'user_456', 'agent');
      
      expect(merged.function_calls).toHaveLength(2);
      expect(merged.function_calls?.[0].name).toBe('function_1');
      expect(merged.function_calls?.[1].name).toBe('function_2');
    });

    it('throws error for empty events array', () => {
      expect(() => {
        mergeStreamingEvents([], 'session_123', 'user_456', 'agent');
      }).toThrow('Cannot merge empty events array');
    });
  });

  describe('validateADKEvent', () => {
    it('validates correct ADK event structure', () => {
      expect(validateADKEvent(mockADKEvent)).toBe(true);
    });

    it('rejects invalid event structure', () => {
      expect(validateADKEvent({})).toBe(false);
      expect(validateADKEvent(null)).toBe(false);
      expect(validateADKEvent(undefined)).toBe(false);
      expect(validateADKEvent({ interrupted: true })).toBe(false);
      expect(validateADKEvent({ turn_complete: true })).toBe(false);
    });

    it('validates event with content structure', () => {
      const validEvent = {
        interrupted: false,
        turn_complete: true,
        content: {
          role: 'model',
          parts: []
        }
      };

      expect(validateADKEvent(validEvent)).toBe(true);
    });

    it('rejects event with invalid content structure', () => {
      const invalidEvent = {
        interrupted: false,
        turn_complete: true,
        content: {
          role: 'model',
          parts: 'not an array'
        }
      };

      expect(validateADKEvent(invalidEvent)).toBe(false);
    });
  });

  describe('extractProcessingTime', () => {
    it('extracts processing time from metadata', () => {
      const time = extractProcessingTime(mockADKEvent);
      expect(time).toBe(2500);
    });

    it('returns undefined when no processing time available', () => {
      const eventWithoutTime: ADKEvent = {
        ...mockADKEvent,
        metadata: undefined
      };

      const time = extractProcessingTime(eventWithoutTime);
      expect(time).toBeUndefined();
    });

    it('tries alternative metadata fields', () => {
      const eventWithDuration: ADKEvent = {
        ...mockADKEvent,
        metadata: {
          duration_ms: 1500
        }
      };

      const time = extractProcessingTime(eventWithDuration);
      expect(time).toBe(1500);
    });
  });
});