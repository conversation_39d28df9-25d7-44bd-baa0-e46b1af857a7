{"users": [{"id": "user_1", "name": "<PERSON>", "email": "<EMAIL>", "bio": "Tech entrepreneur and content creator sharing insights about startups and innovation.", "avatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150", "industry": "Technology", "created_at": "2025-01-05T13:51:39.348096+00:00", "last_active": "2025-08-26T22:51:39.348096+00:00", "subscription_plan": "pro", "preferences": {"notifications": true, "auto_post": false, "ai_suggestions": true, "weekly_reports": true}}, {"id": "user_2", "name": "<PERSON>", "email": "<EMAIL>", "bio": "Digital marketing strategist helping businesses grow their online presence.", "avatar": "https://images.unsplash.com/photo-1494790108755-2616b5e4d89d?w=150", "industry": "Marketing", "created_at": "2025-04-18T13:51:39.348096+00:00", "last_active": "2025-08-26T17:51:39.348096+00:00", "subscription_plan": "enterprise", "preferences": {"notifications": true, "auto_post": false, "ai_suggestions": true, "weekly_reports": true}}, {"id": "user_3", "name": "<PERSON>", "email": "<EMAIL>", "bio": "Fitness coach and wellness advocate inspiring healthy lifestyle choices.", "avatar": "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150", "industry": "Health & Fitness", "created_at": "2025-02-21T13:51:39.348096+00:00", "last_active": "2025-08-26T22:51:39.348096+00:00", "subscription_plan": "free", "preferences": {"notifications": true, "auto_post": false, "ai_suggestions": true, "weekly_reports": true}}], "connected_accounts": [{"id": "instagram_user_1_3608", "user_id": "user_1", "platform": "instagram", "handle": "@lifestyleinspired", "display_name": "digitalmarketingpro", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-05-01T13:51:39.348096+00:00", "last_sync": "2025-08-27T04:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 5980, "following": 596, "engagement_rate": 5.43, "avg_likes": 227, "avg_comments": 22, "avg_shares": 11, "growth_rate": 7.4, "reach": 10722, "impressions": 21535, "stories_views": 14231, "profile_visits": 10763, "website_clicks": 423, "saves": 2347}, "account_info": {"verified": true, "business_account": true, "follower_count": 23338, "following_count": 1847, "post_count": 101}}, {"id": "youtube_user_1_2386", "user_id": "user_1", "platform": "youtube", "handle": "@InnovationDaily", "display_name": "InnovationDaily", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-03-28T13:51:39.348096+00:00", "last_sync": "2025-08-27T12:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 35334, "following": 882, "engagement_rate": 7.68, "avg_likes": 1899, "avg_comments": 189, "avg_shares": 94, "growth_rate": -0.6, "reach": 72211, "impressions": 140516, "subscribers": 35334, "views": 361545, "watch_time": 41796, "avg_view_duration": 6.7, "videos_published": 36}, "account_info": {"verified": true, "business_account": false, "follower_count": 23464, "following_count": 731, "post_count": 484}}, {"id": "twitter_user_1_8235", "user_id": "user_1", "platform": "twitter", "handle": "@fitnessjourney", "display_name": "marketingmind", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-05-16T13:51:39.348096+00:00", "last_sync": "2025-08-27T07:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 6146, "following": 1722, "engagement_rate": 3.18, "avg_likes": 136, "avg_comments": 13, "avg_shares": 6, "growth_rate": -1.0, "reach": 9308, "impressions": 26198, "tweets": 1923, "retweets": 7620, "mentions": 1355, "link_clicks": 575}, "account_info": {"verified": true, "business_account": false, "follower_count": 10295, "following_count": 300, "post_count": 145}}, {"id": "instagram_user_2_4631", "user_id": "user_2", "platform": "instagram", "handle": "@lifestyleinspired", "display_name": "creativecontent", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-04-24T13:51:39.348096+00:00", "last_sync": "2025-08-26T17:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 4931, "following": 1723, "engagement_rate": 4.31, "avg_likes": 148, "avg_comments": 14, "avg_shares": 7, "growth_rate": 11.9, "reach": 9877, "impressions": 21617, "stories_views": 11713, "profile_visits": 12132, "website_clicks": 1595, "saves": 3869}, "account_info": {"verified": true, "business_account": false, "follower_count": 23566, "following_count": 138, "post_count": 169}}, {"id": "youtube_user_2_7550", "user_id": "user_2", "platform": "youtube", "handle": "@InnovationDaily", "display_name": "TechWithAlex", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-05-24T13:51:39.348096+00:00", "last_sync": "2025-08-27T00:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 17373, "following": 1238, "engagement_rate": 4.81, "avg_likes": 584, "avg_comments": 58, "avg_shares": 29, "growth_rate": 8.3, "reach": 25483, "impressions": 40422, "subscribers": 17373, "views": 496703, "watch_time": 29667, "avg_view_duration": 4.2, "videos_published": 109}, "account_info": {"verified": true, "business_account": false, "follower_count": 31470, "following_count": 1390, "post_count": 291}}, {"id": "twitter_user_2_8451", "user_id": "user_2", "platform": "twitter", "handle": "@fitnessjourney", "display_name": "marketingmind", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-06-15T13:51:39.348096+00:00", "last_sync": "2025-08-26T16:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 10990, "following": 1645, "engagement_rate": 1.66, "avg_likes": 127, "avg_comments": 12, "avg_shares": 6, "growth_rate": 12.6, "reach": 20259, "impressions": 43118, "tweets": 4526, "retweets": 7519, "mentions": 1070, "link_clicks": 2317}, "account_info": {"verified": false, "business_account": false, "follower_count": 9102, "following_count": 1958, "post_count": 114}}, {"id": "twitter_user_3_5354", "user_id": "user_3", "platform": "twitter", "handle": "@marketingmind", "display_name": "techthoughts", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-07-11T13:51:39.348096+00:00", "last_sync": "2025-08-26T19:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 4711, "following": 362, "engagement_rate": 2.58, "avg_likes": 85, "avg_comments": 8, "avg_shares": 4, "growth_rate": 14.7, "reach": 12641, "impressions": 11176, "tweets": 3790, "retweets": 6051, "mentions": 798, "link_clicks": 1358}, "account_info": {"verified": true, "business_account": false, "follower_count": 6131, "following_count": 1187, "post_count": 166}}, {"id": "instagram_user_3_4872", "user_id": "user_3", "platform": "instagram", "handle": "@creativecontent", "display_name": "digitalmarketingpro", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-05-29T13:51:39.348096+00:00", "last_sync": "2025-08-27T06:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 17733, "following": 604, "engagement_rate": 3.18, "avg_likes": 394, "avg_comments": 39, "avg_shares": 19, "growth_rate": 2.0, "reach": 52979, "impressions": 46057, "stories_views": 12293, "profile_visits": 9573, "website_clicks": 165, "saves": 3244}, "account_info": {"verified": true, "business_account": false, "follower_count": 4905, "following_count": 568, "post_count": 454}}, {"id": "youtube_user_3_1048", "user_id": "user_3", "platform": "youtube", "handle": "@TechWithAlex", "display_name": "TechWithAlex", "avatar": "https://images.unsplash.com/photo-*************?w=150", "connected_at": "2025-02-28T13:51:39.348096+00:00", "last_sync": "2025-08-27T11:51:39.348096+00:00", "status": "active", "permissions": ["read", "write", "analytics"], "metrics": {"followers": 24576, "following": 219, "engagement_rate": 6.99, "avg_likes": 1202, "avg_comments": 120, "avg_shares": 60, "growth_rate": 7.7, "reach": 37188, "impressions": 62435, "subscribers": 24576, "views": 67368, "watch_time": 23923, "avg_view_duration": 6.1, "videos_published": 64}, "account_info": {"verified": true, "business_account": false, "follower_count": 14509, "following_count": 1942, "post_count": 306}}], "content_plans": [{"id": "plan_user_1_1_1756043499", "user_id": "user_1", "title": "Engagement Boost Plan", "description": "Product promotion with authentic storytelling approach", "timeframe_start": "2025-08-24T13:51:39.348096+00:00", "timeframe_end": "2025-09-07T13:51:39.348096+00:00", "status": "completed", "platforms": ["youtube"], "goals": ["Promote products/services", "Establish thought leadership", "Build brand awareness"], "target_metrics": {"follower_growth": 15, "engagement_increase": 31, "content_frequency": "weekly"}, "posts": [{"id": "post_1_1756035000", "title": "New Product Launch Coming Soon!", "description": "Here's what I learned and how you can apply it", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-08-24T11:30:00+00:00", "status": "published", "tags": ["#marketing", "#innovation", "#technology", "#tips"], "estimated_reach": 8843, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.6, "optimal_posting_time": false}}, {"id": "post_2_1756033200", "title": "Swipe for Business Tips →", "description": "Here's what I learned and how you can apply it", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-08-24T11:00:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#business"], "estimated_reach": 1961, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.0, "optimal_posting_time": true}}, {"id": "post_3_1756112400", "title": "Weekend Vibes and Reflections", "description": "Here's what I learned and how you can apply it", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-08-25T09:00:00+00:00", "status": "published", "tags": ["#innovation", "#business", "#tutorial"], "estimated_reach": 1038, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.9, "optimal_posting_time": false}}, {"id": "post_4_1756198800", "title": "Monday Motivation: Start Strong", "description": "Sharing valuable insights from my recent experience...", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-08-26T09:00:00+00:00", "status": "failed", "tags": ["#inspiration", "#marketing", "#tips", "#growth", "#tutorial"], "estimated_reach": 6596, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.5, "optimal_posting_time": true}}, {"id": "post_5_1756201500", "title": "Personal update and big announcement", "description": "Here's what I learned and how you can apply it", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-08-26T09:45:00+00:00", "status": "failed", "tags": ["#technology", "#productivity", "#marketing"], "estimated_reach": 4441, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.8, "optimal_posting_time": false}}, {"id": "post_6_1756285200", "title": "Review: Top Tools for Content Creators", "description": "Transform your approach with these proven methods", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-08-27T09:00:00+00:00", "status": "published", "tags": ["#marketing", "#business"], "estimated_reach": 8785, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.8, "optimal_posting_time": true}}, {"id": "post_7_1756381500", "title": "New Product Launch Coming Soon!", "description": "Sharing valuable insights from my recent experience...", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-08-28T11:45:00+00:00", "status": "draft", "tags": ["#business", "#inspiration", "#marketing", "#growth", "#tutorial"], "estimated_reach": 1700, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": false}}, {"id": "post_8_1756412100", "title": "Behind the Scenes: Building a Startup", "description": "Here's what I learned and how you can apply it", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-08-28T20:15:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#productivity", "#marketing", "#tips", "#business"], "estimated_reach": 6941, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": false}}, {"id": "post_9_1756374300", "title": "New Product Launch Coming Soon!", "description": "Let me know what you think in the comments below!", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-08-28T09:45:00+00:00", "status": "scheduled", "tags": ["#business", "#productivity", "#marketing", "#innovation", "#growth"], "estimated_reach": 8639, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.5, "optimal_posting_time": true}}, {"id": "post_10_1756497600", "title": "Personal update and big announcement", "description": "Join the conversation and share your thoughts!", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-08-29T20:00:00+00:00", "status": "draft", "tags": ["#tutorial", "#productivity"], "estimated_reach": 6645, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.8, "optimal_posting_time": true}}, {"id": "post_11_1756478700", "title": "5 Productivity Tips That Changed My Life", "description": "Here's what I learned and how you can apply it", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-08-29T14:45:00+00:00", "status": "draft", "tags": ["#marketing", "#inspiration", "#tutorial", "#technology", "#tips"], "estimated_reach": 1080, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.6, "optimal_posting_time": true}}, {"id": "post_12_1756491300", "title": "Industry insight: What's changing", "description": "Transform your approach with these proven methods", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-08-29T18:15:00+00:00", "status": "scheduled", "tags": ["#business", "#innovation", "#inspiration"], "estimated_reach": 9345, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": true}}, {"id": "post_13_1756551600", "title": "My Morning Routine for Success", "description": "Sharing valuable insights from my recent experience...", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-08-30T11:00:00+00:00", "status": "failed", "tags": ["#innovation", "#tutorial", "#marketing", "#growth"], "estimated_reach": 7292, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.4, "optimal_posting_time": false}}, {"id": "post_14_1756663200", "title": "Coffee and Creativity Session", "description": "Join the conversation and share your thoughts!", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-08-31T18:00:00+00:00", "status": "draft", "tags": ["#growth", "#inspiration", "#productivity"], "estimated_reach": 2779, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.4, "optimal_posting_time": false}}, {"id": "post_15_1756756800", "title": "My Morning Routine for Success", "description": "Here's what I learned and how you can apply it", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-01T20:00:00+00:00", "status": "draft", "tags": ["#tips", "#technology"], "estimated_reach": 5496, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.9, "optimal_posting_time": true}}, {"id": "post_16_1756743300", "title": "Review: Top Tools for Content Creators", "description": "What strategies have worked best for you?", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-01T16:15:00+00:00", "status": "published", "tags": ["#innovation", "#productivity", "#tutorial", "#marketing", "#inspiration"], "estimated_reach": 1683, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.2, "optimal_posting_time": true}}, {"id": "post_17_1756810800", "title": "Monday Motivation: Start Strong", "description": "Let me know what you think in the comments below!", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-02T11:00:00+00:00", "status": "published", "tags": ["#inspiration", "#marketing", "#growth", "#business"], "estimated_reach": 1126, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.5, "optimal_posting_time": true}}, {"id": "post_18_1756823400", "title": "Complete Guide to Social Media Marketing", "description": "Let me know what you think in the comments below!", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-02T14:30:00+00:00", "status": "failed", "tags": ["#technology", "#productivity"], "estimated_reach": 5693, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.1, "optimal_posting_time": false}}, {"id": "post_19_1756909800", "title": "Thread: 10 lessons learned this year", "description": "Here's what I learned and how you can apply it", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-03T14:30:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#tips", "#tutorial", "#growth"], "estimated_reach": 9286, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.4, "optimal_posting_time": true}}, {"id": "post_20_1756917900", "title": "Coffee and Creativity Session", "description": "Sharing valuable insights from my recent experience...", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-03T16:45:00+00:00", "status": "scheduled", "tags": ["#business", "#marketing", "#productivity", "#tips", "#tutorial"], "estimated_reach": 4204, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.7, "optimal_posting_time": true}}, {"id": "post_21_1756930500", "title": "Personal update and big announcement", "description": "Breaking down complex topics into actionable steps", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-03T20:15:00+00:00", "status": "published", "tags": ["#marketing", "#innovation", "#productivity", "#tutorial", "#inspiration"], "estimated_reach": 1207, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.6, "optimal_posting_time": false}}, {"id": "post_22_1757016000", "title": "Thread: 10 lessons learned this year", "description": "Transform your approach with these proven methods", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-04T20:00:00+00:00", "status": "scheduled", "tags": ["#technology", "#productivity"], "estimated_reach": 3703, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": true}}, {"id": "post_23_1757017800", "title": "Thread: 10 lessons learned this year", "description": "Join the conversation and share your thoughts!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-04T20:30:00+00:00", "status": "failed", "tags": ["#tips", "#business", "#marketing"], "estimated_reach": 5486, "content_pillars": ["Entertainment", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.5, "optimal_posting_time": true}}, {"id": "post_24_1756977300", "title": "My Morning Routine for Success", "description": "Transform your approach with these proven methods", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-04T09:15:00+00:00", "status": "scheduled", "tags": ["#tips", "#marketing", "#innovation", "#tutorial"], "estimated_reach": 7549, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.0, "optimal_posting_time": true}}, {"id": "post_25_1757070000", "title": "Behind the Scenes: Building a Startup", "description": "Let me know what you think in the comments below!", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-05T11:00:00+00:00", "status": "published", "tags": ["#technology", "#productivity", "#marketing"], "estimated_reach": 9386, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.3, "optimal_posting_time": true}}, {"id": "post_26_1757105100", "title": "Thread: 10 lessons learned this year", "description": "Breaking down complex topics into actionable steps", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-05T20:45:00+00:00", "status": "published", "tags": ["#tutorial", "#productivity", "#innovation"], "estimated_reach": 8196, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.9, "optimal_posting_time": true}}, {"id": "post_27_1757151000", "title": "Quick tip for better content creation", "description": "Save this post for later reference 📌", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-06T09:30:00+00:00", "status": "published", "tags": ["#business", "#productivity", "#innovation"], "estimated_reach": 3266, "content_pillars": ["Education", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": false}}, {"id": "post_28_1757167200", "title": "Personal update and big announcement", "description": "Save this post for later reference 📌", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-06T14:00:00+00:00", "status": "draft", "tags": ["#tutorial", "#innovation"], "estimated_reach": 1780, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.6, "optimal_posting_time": false}}, {"id": "post_29_1757261700", "title": "Weekend Vibes and Reflections", "description": "Breaking down complex topics into actionable steps", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-07T16:15:00+00:00", "status": "published", "tags": ["#innovation", "#tips"], "estimated_reach": 1344, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.9, "optimal_posting_time": true}}, {"id": "post_30_1757235600", "title": "Hot take: Social media strategy in 2024", "description": "Join the conversation and share your thoughts!", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-07T09:00:00+00:00", "status": "scheduled", "tags": ["#tips", "#marketing", "#innovation"], "estimated_reach": 3165, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.5, "optimal_posting_time": true}}], "created_at": "2025-08-19T13:51:39.348096+00:00", "updated_at": "2025-08-26T08:51:39.348096+00:00"}, {"id": "plan_user_1_2_1756561899", "user_id": "user_1", "title": "Q4 Content Calendar", "description": "Comprehensive content strategy to increase engagement and followers", "timeframe_start": "2025-08-30T13:51:39.348096+00:00", "timeframe_end": "2025-09-13T13:51:39.348096+00:00", "status": "paused", "platforms": ["instagram"], "goals": ["Drive website traffic", "Boost engagement", "Community building", "Promote products/services"], "target_metrics": {"follower_growth": 11, "engagement_increase": 25, "content_frequency": "weekly"}, "posts": [{"id": "post_1_1756547100", "title": "Hot take: Social media strategy in 2024", "description": "Breaking down complex topics into actionable steps", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-08-30T09:45:00+00:00", "status": "failed", "tags": ["#business", "#productivity", "#inspiration"], "estimated_reach": 5642, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.2, "optimal_posting_time": true}}, {"id": "post_2_1756551600", "title": "Industry insight: What's changing", "description": "Here's what I learned and how you can apply it", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-08-30T11:00:00+00:00", "status": "scheduled", "tags": ["#business", "#growth", "#marketing", "#innovation"], "estimated_reach": 8007, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.1, "optimal_posting_time": true}}, {"id": "post_3_1756656000", "title": "Hot take: Social media strategy in 2024", "description": "Let me know what you think in the comments below!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-08-31T16:00:00+00:00", "status": "scheduled", "tags": ["#marketing", "#inspiration", "#growth", "#tutorial", "#technology"], "estimated_reach": 2344, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": true}}, {"id": "post_4_1756725300", "title": "Behind the Scenes: Building a Startup", "description": "Sharing valuable insights from my recent experience...", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-01T11:15:00+00:00", "status": "published", "tags": ["#tips", "#technology", "#tutorial", "#productivity", "#inspiration"], "estimated_reach": 7503, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": true}}, {"id": "post_5_1756744200", "title": "Thread: 10 lessons learned this year", "description": "Save this post for later reference 📌", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-01T16:30:00+00:00", "status": "scheduled", "tags": ["#business", "#technology", "#productivity", "#growth"], "estimated_reach": 8915, "content_pillars": ["Inspiration", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.1, "optimal_posting_time": false}}, {"id": "post_6_1756843200", "title": "Personal update and big announcement", "description": "Transform your approach with these proven methods", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-02T20:00:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#innovation", "#inspiration", "#technology"], "estimated_reach": 2515, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.3, "optimal_posting_time": true}}, {"id": "post_7_1756890900", "title": "Coffee and Creativity Session", "description": "Save this post for later reference 📌", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-03T09:15:00+00:00", "status": "failed", "tags": ["#marketing", "#growth", "#inspiration"], "estimated_reach": 9365, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.9, "optimal_posting_time": true}}, {"id": "post_8_1756931400", "title": "Coffee and Creativity Session", "description": "Join the conversation and share your thoughts!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-03T20:30:00+00:00", "status": "failed", "tags": ["#growth", "#technology"], "estimated_reach": 8757, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.1, "optimal_posting_time": true}}, {"id": "post_9_1757010600", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-04T18:30:00+00:00", "status": "draft", "tags": ["#technology", "#marketing", "#tips", "#business", "#innovation"], "estimated_reach": 4494, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.7, "optimal_posting_time": true}}, {"id": "post_10_1757097900", "title": "Complete Guide to Social Media Marketing", "description": "Let me know what you think in the comments below!", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-05T18:45:00+00:00", "status": "scheduled", "tags": ["#tips", "#tutorial", "#technology", "#productivity", "#growth"], "estimated_reach": 9863, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.5, "optimal_posting_time": true}}, {"id": "post_11_1757167200", "title": "Industry insight: What's changing", "description": "Transform your approach with these proven methods", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-06T14:00:00+00:00", "status": "published", "tags": ["#growth", "#innovation", "#marketing"], "estimated_reach": 3143, "content_pillars": ["Behind-the-scenes", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.1, "optimal_posting_time": true}}, {"id": "post_12_1757254500", "title": "Complete Guide to Social Media Marketing", "description": "Breaking down complex topics into actionable steps", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-07T14:15:00+00:00", "status": "failed", "tags": ["#tips", "#technology", "#innovation", "#growth"], "estimated_reach": 6224, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.3, "optimal_posting_time": true}}, {"id": "post_13_1757245500", "title": "Behind the Scenes: Building a Startup", "description": "Sharing valuable insights from my recent experience...", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-07T11:45:00+00:00", "status": "failed", "tags": ["#tips", "#productivity"], "estimated_reach": 6832, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.4, "optimal_posting_time": true}}, {"id": "post_14_1757340900", "title": "New Product Launch Coming Soon!", "description": "Breaking down complex topics into actionable steps", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-08T14:15:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#tips", "#innovation", "#business", "#growth"], "estimated_reach": 6080, "content_pillars": ["Education", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.7, "optimal_posting_time": false}}, {"id": "post_15_1757415600", "title": "Swipe for Business Tips →", "description": "Join the conversation and share your thoughts!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-09T11:00:00+00:00", "status": "scheduled", "tags": ["#innovation", "#marketing", "#productivity", "#technology"], "estimated_reach": 7224, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.1, "optimal_posting_time": false}}, {"id": "post_16_1757529900", "title": "Swipe for Business Tips →", "description": "Sharing valuable insights from my recent experience...", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-10T18:45:00+00:00", "status": "scheduled", "tags": ["#business", "#marketing"], "estimated_reach": 5485, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.1, "optimal_posting_time": true}}, {"id": "post_17_1757502000", "title": "Weekend Vibes and Reflections", "description": "Breaking down complex topics into actionable steps", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-10T11:00:00+00:00", "status": "draft", "tags": ["#tips", "#marketing", "#growth", "#inspiration", "#business"], "estimated_reach": 3459, "content_pillars": ["Education", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.9, "optimal_posting_time": true}}, {"id": "post_18_1757513700", "title": "Monday Motivation: Start Strong", "description": "Breaking down complex topics into actionable steps", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-10T14:15:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#technology"], "estimated_reach": 8559, "content_pillars": ["Entertainment", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.4, "optimal_posting_time": true}}, {"id": "post_19_1757601900", "title": "Behind the Scenes: Building a Startup", "description": "Transform your approach with these proven methods", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-11T14:45:00+00:00", "status": "scheduled", "tags": ["#technology", "#growth", "#innovation", "#tutorial", "#business"], "estimated_reach": 2497, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.3, "optimal_posting_time": true}}, {"id": "post_20_1757700900", "title": "Behind the Scenes: Building a Startup", "description": "Here's what I learned and how you can apply it", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_1_2386", "scheduled_time": "2025-09-12T18:15:00+00:00", "status": "failed", "tags": ["#technology", "#productivity"], "estimated_reach": 3963, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.2, "optimal_posting_time": true}}, {"id": "post_21_1757707200", "title": "Swipe for Business Tips →", "description": "What strategies have worked best for you?", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-12T20:00:00+00:00", "status": "failed", "tags": ["#marketing", "#tips", "#business"], "estimated_reach": 2373, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.4, "optimal_posting_time": true}}, {"id": "post_22_1757676600", "title": "Monday Motivation: Start Strong", "description": "Join the conversation and share your thoughts!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_1_3608", "scheduled_time": "2025-09-12T11:30:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#business", "#marketing", "#productivity", "#growth"], "estimated_reach": 4137, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.5, "optimal_posting_time": true}}, {"id": "post_23_1757795400", "title": "Quick tip for better content creation", "description": "Transform your approach with these proven methods", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_1_8235", "scheduled_time": "2025-09-13T20:30:00+00:00", "status": "failed", "tags": ["#tutorial", "#productivity", "#marketing"], "estimated_reach": 2991, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.0, "optimal_posting_time": true}}], "created_at": "2025-08-28T13:51:39.348096+00:00", "updated_at": "2025-08-25T01:51:39.348096+00:00"}, {"id": "plan_user_2_1_1757685099", "user_id": "user_2", "title": "Brand Awareness Drive", "description": "Comprehensive content strategy to increase engagement and followers", "timeframe_start": "2025-09-12T13:51:39.348096+00:00", "timeframe_end": "2025-09-26T13:51:39.348096+00:00", "status": "completed", "platforms": ["youtube", "instagram"], "goals": ["Boost engagement", "Build brand awareness", "Generate leads"], "target_metrics": {"follower_growth": 6, "engagement_increase": 22, "content_frequency": "daily"}, "posts": [{"id": "post_1_1757695500", "title": "Complete Guide to Social Media Marketing", "description": "Transform your approach with these proven methods", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-12T16:45:00+00:00", "status": "failed", "tags": ["#inspiration", "#tutorial", "#growth", "#marketing"], "estimated_reach": 7240, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": false}}, {"id": "post_2_1757774700", "title": "Thread: 10 lessons learned this year", "description": "Sharing valuable insights from my recent experience...", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-13T14:45:00+00:00", "status": "draft", "tags": ["#inspiration", "#tips"], "estimated_reach": 4674, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.1, "optimal_posting_time": true}}, {"id": "post_3_1757786400", "title": "Thread: 10 lessons learned this year", "description": "Let me know what you think in the comments below!", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-13T18:00:00+00:00", "status": "scheduled", "tags": ["#innovation", "#tips", "#productivity"], "estimated_reach": 5644, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.1, "optimal_posting_time": false}}, {"id": "post_4_1757850300", "title": "Swipe for Business Tips →", "description": "What strategies have worked best for you?", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-14T11:45:00+00:00", "status": "failed", "tags": ["#productivity", "#innovation"], "estimated_reach": 4677, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_5_1757847600", "title": "Personal update and big announcement", "description": "What strategies have worked best for you?", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-14T11:00:00+00:00", "status": "published", "tags": ["#tips", "#inspiration", "#technology"], "estimated_reach": 9986, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": true}}, {"id": "post_6_1757842200", "title": "Weekend Vibes and Reflections", "description": "Join the conversation and share your thoughts!", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-14T09:30:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#business", "#tips", "#technology", "#tutorial"], "estimated_reach": 1098, "content_pillars": ["Education", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.9, "optimal_posting_time": true}}, {"id": "post_7_1757967300", "title": "Complete Guide to Social Media Marketing", "description": "Here's what I learned and how you can apply it", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-15T20:15:00+00:00", "status": "scheduled", "tags": ["#growth", "#inspiration", "#tips", "#innovation", "#technology"], "estimated_reach": 8172, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.9, "optimal_posting_time": true}}, {"id": "post_8_1757952000", "title": "My Morning Routine for Success", "description": "Breaking down complex topics into actionable steps", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-15T16:00:00+00:00", "status": "draft", "tags": ["#innovation", "#growth"], "estimated_reach": 3456, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.7, "optimal_posting_time": true}}, {"id": "post_9_1758048300", "title": "Swipe for Business Tips →", "description": "Let me know what you think in the comments below!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-16T18:45:00+00:00", "status": "draft", "tags": ["#innovation", "#inspiration", "#growth"], "estimated_reach": 9463, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": true}}, {"id": "post_10_1758107700", "title": "My Morning Routine for Success", "description": "Join the conversation and share your thoughts!", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-17T11:15:00+00:00", "status": "failed", "tags": ["#technology", "#tips", "#inspiration"], "estimated_reach": 1031, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.1, "optimal_posting_time": true}}, {"id": "post_11_1758204900", "title": "New Product Launch Coming Soon!", "description": "Let me know what you think in the comments below!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-18T14:15:00+00:00", "status": "failed", "tags": ["#tips", "#productivity"], "estimated_reach": 6685, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.0, "optimal_posting_time": false}}, {"id": "post_12_1758225600", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-18T20:00:00+00:00", "status": "published", "tags": ["#growth", "#marketing", "#tutorial", "#innovation"], "estimated_reach": 8407, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": true}}, {"id": "post_13_1758298500", "title": "Swipe for Business Tips →", "description": "Save this post for later reference 📌", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-19T16:15:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#productivity", "#marketing", "#business", "#innovation"], "estimated_reach": 8654, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.4, "optimal_posting_time": true}}, {"id": "post_14_1758282300", "title": "Coffee and Creativity Session", "description": "Join the conversation and share your thoughts!", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-19T11:45:00+00:00", "status": "scheduled", "tags": ["#tips", "#inspiration", "#growth"], "estimated_reach": 1395, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_15_1758274200", "title": "My Morning Routine for Success", "description": "Let me know what you think in the comments below!", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-19T09:30:00+00:00", "status": "failed", "tags": ["#productivity", "#technology"], "estimated_reach": 6062, "content_pillars": ["Behind-the-scenes", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_16_1758376800", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-20T14:00:00+00:00", "status": "published", "tags": ["#inspiration", "#marketing", "#innovation", "#business", "#growth"], "estimated_reach": 6299, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.3, "optimal_posting_time": false}}, {"id": "post_17_1758386700", "title": "Quick tip for better content creation", "description": "Sharing valuable insights from my recent experience...", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-20T16:45:00+00:00", "status": "draft", "tags": ["#business", "#technology"], "estimated_reach": 9438, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.0, "optimal_posting_time": true}}, {"id": "post_18_1758478500", "title": "Industry insight: What's changing", "description": "What strategies have worked best for you?", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-21T18:15:00+00:00", "status": "failed", "tags": ["#innovation", "#technology", "#productivity"], "estimated_reach": 4898, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.2, "optimal_posting_time": true}}, {"id": "post_19_1758573900", "title": "Complete Guide to Social Media Marketing", "description": "Sharing valuable insights from my recent experience...", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-22T20:45:00+00:00", "status": "draft", "tags": ["#innovation", "#tutorial", "#growth", "#marketing"], "estimated_reach": 5363, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.3, "optimal_posting_time": false}}, {"id": "post_20_1758558600", "title": "5 Productivity Tips That Changed My Life", "description": "What strategies have worked best for you?", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-22T16:30:00+00:00", "status": "failed", "tags": ["#technology", "#inspiration", "#tips", "#marketing"], "estimated_reach": 6685, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.6, "optimal_posting_time": true}}, {"id": "post_21_1758557700", "title": "Review: Top Tools for Content Creators", "description": "Save this post for later reference 📌", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-22T16:15:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#growth", "#technology", "#innovation", "#business"], "estimated_reach": 4022, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.2, "optimal_posting_time": false}}, {"id": "post_22_1758645000", "title": "Weekend Vibes and Reflections", "description": "Transform your approach with these proven methods", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-23T16:30:00+00:00", "status": "failed", "tags": ["#innovation", "#business"], "estimated_reach": 1274, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.0, "optimal_posting_time": true}}, {"id": "post_23_1758626100", "title": "Swipe for Business Tips →", "description": "Transform your approach with these proven methods", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-23T11:15:00+00:00", "status": "published", "tags": ["#tutorial", "#marketing"], "estimated_reach": 4190, "content_pillars": ["Education", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.3, "optimal_posting_time": false}}, {"id": "post_24_1758729600", "title": "Personal update and big announcement", "description": "Here's what I learned and how you can apply it", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-24T16:00:00+00:00", "status": "failed", "tags": ["#business", "#innovation"], "estimated_reach": 1641, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.6, "optimal_posting_time": false}}, {"id": "post_25_1758711600", "title": "Coffee and Creativity Session", "description": "Join the conversation and share your thoughts!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-24T11:00:00+00:00", "status": "failed", "tags": ["#innovation", "#productivity", "#technology", "#business"], "estimated_reach": 7288, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.8, "optimal_posting_time": true}}, {"id": "post_26_1758823200", "title": "Swipe for Business Tips →", "description": "Transform your approach with these proven methods", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-25T18:00:00+00:00", "status": "scheduled", "tags": ["#technology", "#inspiration", "#tutorial", "#tips", "#business"], "estimated_reach": 7451, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.7, "optimal_posting_time": true}}, {"id": "post_27_1758912300", "title": "My Morning Routine for Success", "description": "Let me know what you think in the comments below!", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-26T18:45:00+00:00", "status": "failed", "tags": ["#innovation", "#inspiration", "#growth", "#tips", "#tutorial"], "estimated_reach": 7448, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.1, "optimal_posting_time": true}}, {"id": "post_28_1758918600", "title": "Hot take: Social media strategy in 2024", "description": "Join the conversation and share your thoughts!", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-26T20:30:00+00:00", "status": "scheduled", "tags": ["#productivity", "#innovation", "#tips", "#inspiration"], "estimated_reach": 2172, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.4, "optimal_posting_time": false}}], "created_at": "2025-09-06T13:51:39.348096+00:00", "updated_at": "2025-08-26T07:51:39.348096+00:00"}, {"id": "plan_user_2_2_1756129899", "user_id": "user_2", "title": "Engagement Boost Plan", "description": "Product promotion with authentic storytelling approach", "timeframe_start": "2025-08-25T13:51:39.348096+00:00", "timeframe_end": "2025-09-01T13:51:39.348096+00:00", "status": "draft", "platforms": ["twitter"], "goals": ["Promote products/services", "Drive website traffic"], "target_metrics": {"follower_growth": 25, "engagement_increase": 34, "content_frequency": "bi-weekly"}, "posts": [{"id": "post_1_1756153800", "title": "Monday Motivation: Start Strong", "description": "Sharing valuable insights from my recent experience...", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-08-25T20:30:00+00:00", "status": "draft", "tags": ["#business", "#tips"], "estimated_reach": 1328, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": true}}, {"id": "post_2_1756114200", "title": "5 Productivity Tips That Changed My Life", "description": "Save this post for later reference 📌", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-08-25T09:30:00+00:00", "status": "failed", "tags": ["#technology", "#business"], "estimated_reach": 1907, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.5, "optimal_posting_time": true}}, {"id": "post_3_1756233900", "title": "Complete Guide to Social Media Marketing", "description": "Breaking down complex topics into actionable steps", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-08-26T18:45:00+00:00", "status": "draft", "tags": ["#tutorial", "#business", "#marketing", "#innovation", "#productivity"], "estimated_reach": 5190, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.8, "optimal_posting_time": false}}, {"id": "post_4_1756200600", "title": "Behind the Scenes: Building a Startup", "description": "Transform your approach with these proven methods", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-08-26T09:30:00+00:00", "status": "scheduled", "tags": ["#tips", "#marketing"], "estimated_reach": 7105, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.3, "optimal_posting_time": true}}, {"id": "post_5_1756287000", "title": "5 Productivity Tips That Changed My Life", "description": "Sharing valuable insights from my recent experience...", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-08-27T09:30:00+00:00", "status": "scheduled", "tags": ["#growth", "#business"], "estimated_reach": 1392, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.5, "optimal_posting_time": false}}, {"id": "post_6_1756312200", "title": "Weekend Vibes and Reflections", "description": "Join the conversation and share your thoughts!", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-08-27T16:30:00+00:00", "status": "published", "tags": ["#business", "#technology", "#marketing"], "estimated_reach": 5386, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.1, "optimal_posting_time": true}}, {"id": "post_7_1756378800", "title": "Personal update and big announcement", "description": "Breaking down complex topics into actionable steps", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-08-28T11:00:00+00:00", "status": "scheduled", "tags": ["#innovation", "#tips", "#tutorial", "#marketing", "#growth"], "estimated_reach": 7841, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 8.0, "optimal_posting_time": true}}, {"id": "post_8_1756374300", "title": "Hot take: Social media strategy in 2024", "description": "Here's what I learned and how you can apply it", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-08-28T09:45:00+00:00", "status": "scheduled", "tags": ["#innovation", "#tips", "#marketing", "#tutorial"], "estimated_reach": 3940, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.9, "optimal_posting_time": true}}, {"id": "post_9_1756460700", "title": "Personal update and big announcement", "description": "Here's what I learned and how you can apply it", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-08-29T09:45:00+00:00", "status": "published", "tags": ["#growth", "#business", "#productivity"], "estimated_reach": 1508, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.6, "optimal_posting_time": false}}, {"id": "post_10_1756492200", "title": "Thread: 10 lessons learned this year", "description": "Save this post for later reference 📌", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-08-29T18:30:00+00:00", "status": "scheduled", "tags": ["#productivity", "#tips", "#technology"], "estimated_reach": 9674, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.2, "optimal_posting_time": true}}, {"id": "post_11_1756553400", "title": "Personal update and big announcement", "description": "Save this post for later reference 📌", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-08-30T11:30:00+00:00", "status": "scheduled", "tags": ["#technology", "#inspiration", "#productivity", "#tips"], "estimated_reach": 8553, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.6, "optimal_posting_time": false}}, {"id": "post_12_1756656000", "title": "Quick tip for better content creation", "description": "What strategies have worked best for you?", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-08-31T16:00:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#technology", "#productivity", "#tutorial", "#marketing"], "estimated_reach": 6761, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.6, "optimal_posting_time": false}}, {"id": "post_13_1756630800", "title": "Swipe for Business Tips →", "description": "Breaking down complex topics into actionable steps", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-08-31T09:00:00+00:00", "status": "scheduled", "tags": ["#productivity", "#growth", "#technology"], "estimated_reach": 8854, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_14_1756630800", "title": "Swipe for Business Tips →", "description": "Here's what I learned and how you can apply it", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-08-31T09:00:00+00:00", "status": "failed", "tags": ["#marketing", "#tutorial"], "estimated_reach": 5419, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.8, "optimal_posting_time": true}}, {"id": "post_15_1756727100", "title": "Swipe for Business Tips →", "description": "Join the conversation and share your thoughts!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-01T11:45:00+00:00", "status": "failed", "tags": ["#tips", "#technology"], "estimated_reach": 9839, "content_pillars": ["Education", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": false}}, {"id": "post_16_1756749600", "title": "Swipe for Business Tips →", "description": "What strategies have worked best for you?", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-01T18:00:00+00:00", "status": "failed", "tags": ["#marketing", "#growth", "#business"], "estimated_reach": 8325, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.4, "optimal_posting_time": true}}, {"id": "post_17_1756737000", "title": "Quick tip for better content creation", "description": "Transform your approach with these proven methods", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-01T14:30:00+00:00", "status": "scheduled", "tags": ["#technology", "#marketing", "#productivity", "#tips"], "estimated_reach": 8365, "content_pillars": ["Behind-the-scenes", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.2, "optimal_posting_time": false}}], "created_at": "2025-08-24T13:51:39.348096+00:00", "updated_at": "2025-08-26T02:51:39.348096+00:00"}, {"id": "plan_user_2_3_1756475499", "user_id": "user_2", "title": "7-Day Growth Strategy", "description": "Multi-platform campaign focusing on brand awareness", "timeframe_start": "2025-08-29T13:51:39.348096+00:00", "timeframe_end": "2025-09-05T13:51:39.348096+00:00", "status": "active", "platforms": ["youtube", "instagram", "twitter"], "goals": ["Increase followers", "Community building"], "target_metrics": {"follower_growth": 22, "engagement_increase": 10, "content_frequency": "bi-weekly"}, "posts": [{"id": "post_1_1756485900", "title": "Swipe for Business Tips →", "description": "Save this post for later reference 📌", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-08-29T16:45:00+00:00", "status": "failed", "tags": ["#inspiration", "#innovation", "#productivity"], "estimated_reach": 9126, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.3, "optimal_posting_time": true}}, {"id": "post_2_1756493100", "title": "Behind the Scenes: Building a Startup", "description": "Here's what I learned and how you can apply it", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-08-29T18:45:00+00:00", "status": "draft", "tags": ["#marketing", "#tutorial", "#technology", "#productivity"], "estimated_reach": 3096, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.3, "optimal_posting_time": false}}, {"id": "post_3_1756547100", "title": "Monday Motivation: Start Strong", "description": "Breaking down complex topics into actionable steps", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-08-30T09:45:00+00:00", "status": "scheduled", "tags": ["#marketing", "#productivity", "#inspiration"], "estimated_reach": 2713, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": true}}, {"id": "post_4_1756633500", "title": "Behind the Scenes: Building a Startup", "description": "What strategies have worked best for you?", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-08-31T09:45:00+00:00", "status": "scheduled", "tags": ["#technology", "#tutorial", "#business", "#productivity"], "estimated_reach": 4634, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.2, "optimal_posting_time": true}}, {"id": "post_5_1756743300", "title": "My Morning Routine for Success", "description": "Join the conversation and share your thoughts!", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-01T16:15:00+00:00", "status": "published", "tags": ["#business", "#growth"], "estimated_reach": 6136, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.2, "optimal_posting_time": false}}, {"id": "post_6_1756750500", "title": "Hot take: Social media strategy in 2024", "description": "Here's what I learned and how you can apply it", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-01T18:15:00+00:00", "status": "scheduled", "tags": ["#business", "#innovation", "#technology", "#tips", "#inspiration"], "estimated_reach": 4770, "content_pillars": ["Education", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.5, "optimal_posting_time": true}}, {"id": "post_7_1756828800", "title": "Weekend Vibes and Reflections", "description": "Transform your approach with these proven methods", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-02T16:00:00+00:00", "status": "scheduled", "tags": ["#marketing", "#growth", "#technology", "#productivity", "#business"], "estimated_reach": 5642, "content_pillars": ["Education", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.2, "optimal_posting_time": true}}, {"id": "post_8_1756812600", "title": "My Morning Routine for Success", "description": "Transform your approach with these proven methods", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-02T11:30:00+00:00", "status": "failed", "tags": ["#technology", "#inspiration", "#tutorial", "#tips"], "estimated_reach": 5543, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.3, "optimal_posting_time": true}}, {"id": "post_9_1756844100", "title": "Industry insight: What's changing", "description": "Join the conversation and share your thoughts!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-02T20:15:00+00:00", "status": "published", "tags": ["#inspiration", "#technology"], "estimated_reach": 8328, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.0, "optimal_posting_time": false}}, {"id": "post_10_1756910700", "title": "New Product Launch Coming Soon!", "description": "Sharing valuable insights from my recent experience...", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-03T14:45:00+00:00", "status": "failed", "tags": ["#productivity", "#tips", "#business", "#growth"], "estimated_reach": 2228, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.1, "optimal_posting_time": true}}, {"id": "post_11_1756897200", "title": "Swipe for Business Tips →", "description": "Breaking down complex topics into actionable steps", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_2_4631", "scheduled_time": "2025-09-03T11:00:00+00:00", "status": "draft", "tags": ["#technology", "#tutorial", "#marketing", "#growth", "#productivity"], "estimated_reach": 5399, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.2, "optimal_posting_time": false}}, {"id": "post_12_1756916100", "title": "Quick tip for better content creation", "description": "Save this post for later reference 📌", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-03T16:15:00+00:00", "status": "published", "tags": ["#productivity", "#tutorial", "#growth", "#marketing"], "estimated_reach": 5771, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.0, "optimal_posting_time": true}}, {"id": "post_13_1756983600", "title": "My Morning Routine for Success", "description": "Breaking down complex topics into actionable steps", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_2_7550", "scheduled_time": "2025-09-04T11:00:00+00:00", "status": "published", "tags": ["#business", "#tutorial"], "estimated_reach": 9239, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_14_1757104200", "title": "Thread: 10 lessons learned this year", "description": "Let me know what you think in the comments below!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_2_8451", "scheduled_time": "2025-09-05T20:30:00+00:00", "status": "draft", "tags": ["#marketing", "#business", "#tutorial", "#productivity", "#tips"], "estimated_reach": 3269, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.3, "optimal_posting_time": true}}], "created_at": "2025-08-27T13:51:39.348096+00:00", "updated_at": "2025-08-27T04:51:39.348096+00:00"}, {"id": "plan_user_3_1_1756561899", "user_id": "user_3", "title": "30-Day Growth Strategy", "description": "Educational content series to establish thought leadership", "timeframe_start": "2025-08-30T13:51:39.348096+00:00", "timeframe_end": "2025-09-29T13:51:39.348096+00:00", "status": "active", "platforms": ["twitter"], "goals": ["Promote products/services", "Build brand awareness", "Establish thought leadership"], "target_metrics": {"follower_growth": 17, "engagement_increase": 23, "content_frequency": "bi-weekly"}, "posts": [{"id": "post_1_1756586700", "title": "Weekend Vibes and Reflections", "description": "Let me know what you think in the comments below!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-08-30T20:45:00+00:00", "status": "failed", "tags": ["#marketing", "#innovation", "#growth"], "estimated_reach": 4764, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.2, "optimal_posting_time": true}}, {"id": "post_2_1756638900", "title": "Industry insight: What's changing", "description": "Sharing valuable insights from my recent experience...", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-08-31T11:15:00+00:00", "status": "draft", "tags": ["#marketing", "#tips", "#business", "#growth", "#innovation"], "estimated_reach": 4559, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.0, "optimal_posting_time": true}}, {"id": "post_3_1756757700", "title": "Quick tip for better content creation", "description": "What strategies have worked best for you?", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-01T20:15:00+00:00", "status": "published", "tags": ["#productivity", "#inspiration", "#business"], "estimated_reach": 3443, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.7, "optimal_posting_time": false}}, {"id": "post_4_1756752300", "title": "Quick tip for better content creation", "description": "Join the conversation and share your thoughts!", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-01T18:45:00+00:00", "status": "scheduled", "tags": ["#growth", "#inspiration", "#tips"], "estimated_reach": 2706, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.1, "optimal_posting_time": true}}, {"id": "post_5_1756743300", "title": "Weekend Vibes and Reflections", "description": "What strategies have worked best for you?", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-01T16:15:00+00:00", "status": "draft", "tags": ["#business", "#innovation", "#inspiration"], "estimated_reach": 5993, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.6, "optimal_posting_time": false}}, {"id": "post_6_1756821600", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-02T14:00:00+00:00", "status": "published", "tags": ["#tutorial", "#productivity"], "estimated_reach": 8963, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.4, "optimal_posting_time": true}}, {"id": "post_7_1756890000", "title": "Personal update and big announcement", "description": "Save this post for later reference 📌", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-03T09:00:00+00:00", "status": "draft", "tags": ["#tips", "#productivity", "#business", "#marketing", "#innovation"], "estimated_reach": 4479, "content_pillars": ["Inspiration", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.6, "optimal_posting_time": false}}, {"id": "post_8_1756925100", "title": "New Product Launch Coming Soon!", "description": "Save this post for later reference 📌", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-03T18:45:00+00:00", "status": "scheduled", "tags": ["#business", "#growth"], "estimated_reach": 2922, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": false}}, {"id": "post_9_1757001600", "title": "Swipe for Business Tips →", "description": "Sharing valuable insights from my recent experience...", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-04T16:00:00+00:00", "status": "published", "tags": ["#technology", "#productivity", "#innovation", "#marketing", "#growth"], "estimated_reach": 5065, "content_pillars": ["Education", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.1, "optimal_posting_time": true}}, {"id": "post_10_1756996200", "title": "New Product Launch Coming Soon!", "description": "Here's what I learned and how you can apply it", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-04T14:30:00+00:00", "status": "scheduled", "tags": ["#marketing", "#productivity"], "estimated_reach": 8242, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.2, "optimal_posting_time": true}}, {"id": "post_11_1757097000", "title": "Hot take: Social media strategy in 2024", "description": "Save this post for later reference 📌", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-05T18:30:00+00:00", "status": "published", "tags": ["#marketing", "#business", "#productivity", "#innovation", "#tips"], "estimated_reach": 4525, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.9, "optimal_posting_time": false}}, {"id": "post_12_1757177100", "title": "Review: Top Tools for Content Creators", "description": "Here's what I learned and how you can apply it", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-06T16:45:00+00:00", "status": "failed", "tags": ["#tips", "#productivity", "#inspiration", "#business", "#marketing"], "estimated_reach": 7551, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.5, "optimal_posting_time": false}}, {"id": "post_13_1757181600", "title": "Hot take: Social media strategy in 2024", "description": "Here's what I learned and how you can apply it", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-06T18:00:00+00:00", "status": "failed", "tags": ["#tutorial", "#technology", "#innovation", "#inspiration"], "estimated_reach": 1472, "content_pillars": ["Education", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.3, "optimal_posting_time": true}}, {"id": "post_14_1757157300", "title": "5 Productivity Tips That Changed My Life", "description": "Transform your approach with these proven methods", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-06T11:15:00+00:00", "status": "draft", "tags": ["#technology", "#productivity", "#growth"], "estimated_reach": 9720, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.7, "optimal_posting_time": true}}, {"id": "post_15_1757262600", "title": "Industry insight: What's changing", "description": "Save this post for later reference 📌", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-07T16:30:00+00:00", "status": "published", "tags": ["#business", "#tutorial"], "estimated_reach": 3781, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.9, "optimal_posting_time": true}}, {"id": "post_16_1757242800", "title": "New Product Launch Coming Soon!", "description": "Let me know what you think in the comments below!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-07T11:00:00+00:00", "status": "scheduled", "tags": ["#marketing", "#tips"], "estimated_reach": 2673, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.9, "optimal_posting_time": true}}, {"id": "post_17_1757347200", "title": "New Product Launch Coming Soon!", "description": "Join the conversation and share your thoughts!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-08T16:00:00+00:00", "status": "published", "tags": ["#marketing", "#business", "#tips"], "estimated_reach": 3625, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.3, "optimal_posting_time": true}}, {"id": "post_18_1757341800", "title": "My Morning Routine for Success", "description": "Save this post for later reference 📌", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-08T14:30:00+00:00", "status": "failed", "tags": ["#inspiration", "#business", "#marketing", "#tips", "#tutorial"], "estimated_reach": 4441, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.0, "optimal_posting_time": true}}, {"id": "post_19_1757411100", "title": "Monday Motivation: Start Strong", "description": "What strategies have worked best for you?", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-09T09:45:00+00:00", "status": "scheduled", "tags": ["#growth", "#technology", "#inspiration", "#tips"], "estimated_reach": 9642, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.6, "optimal_posting_time": true}}, {"id": "post_20_1757443500", "title": "New Product Launch Coming Soon!", "description": "Save this post for later reference 📌", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-09T18:45:00+00:00", "status": "draft", "tags": ["#technology", "#innovation", "#tutorial", "#productivity"], "estimated_reach": 2793, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.3, "optimal_posting_time": false}}, {"id": "post_21_1757534400", "title": "Quick tip for better content creation", "description": "Save this post for later reference 📌", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-10T20:00:00+00:00", "status": "failed", "tags": ["#growth", "#tips", "#productivity", "#innovation", "#business"], "estimated_reach": 3739, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.1, "optimal_posting_time": true}}, {"id": "post_22_1757529000", "title": "Swipe for Business Tips →", "description": "Let me know what you think in the comments below!", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-10T18:30:00+00:00", "status": "failed", "tags": ["#inspiration", "#productivity"], "estimated_reach": 3360, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.0, "optimal_posting_time": false}}, {"id": "post_23_1757521800", "title": "Swipe for Business Tips →", "description": "Sharing valuable insights from my recent experience...", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-10T16:30:00+00:00", "status": "draft", "tags": ["#technology", "#growth", "#innovation", "#tips"], "estimated_reach": 6121, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": true}}, {"id": "post_24_1757582100", "title": "Thread: 10 lessons learned this year", "description": "What strategies have worked best for you?", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-11T09:15:00+00:00", "status": "draft", "tags": ["#growth", "#productivity"], "estimated_reach": 5683, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.2, "optimal_posting_time": true}}, {"id": "post_25_1757668500", "title": "Hot take: Social media strategy in 2024", "description": "Breaking down complex topics into actionable steps", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-12T09:15:00+00:00", "status": "draft", "tags": ["#marketing", "#technology", "#tutorial", "#inspiration", "#innovation"], "estimated_reach": 5971, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.2, "optimal_posting_time": true}}, {"id": "post_26_1757702700", "title": "Hot take: Social media strategy in 2024", "description": "Let me know what you think in the comments below!", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-12T18:45:00+00:00", "status": "scheduled", "tags": ["#marketing", "#business", "#tutorial", "#innovation", "#tips"], "estimated_reach": 4146, "content_pillars": ["Entertainment", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.9, "optimal_posting_time": false}}, {"id": "post_27_1757781900", "title": "Complete Guide to Social Media Marketing", "description": "Sharing valuable insights from my recent experience...", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-13T16:45:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#productivity", "#marketing", "#innovation", "#business"], "estimated_reach": 6191, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.3, "optimal_posting_time": true}}, {"id": "post_28_1757754000", "title": "New Product Launch Coming Soon!", "description": "Let me know what you think in the comments below!", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-13T09:00:00+00:00", "status": "draft", "tags": ["#marketing", "#technology", "#tutorial"], "estimated_reach": 1648, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": true}}, {"id": "post_29_1757773800", "title": "New Product Launch Coming Soon!", "description": "Let me know what you think in the comments below!", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-13T14:30:00+00:00", "status": "draft", "tags": ["#marketing", "#inspiration", "#technology"], "estimated_reach": 6594, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.0, "optimal_posting_time": false}}, {"id": "post_30_1757848500", "title": "Complete Guide to Social Media Marketing", "description": "Sharing valuable insights from my recent experience...", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-14T11:15:00+00:00", "status": "draft", "tags": ["#inspiration", "#productivity", "#business", "#tips", "#marketing"], "estimated_reach": 9185, "content_pillars": ["Education", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.7, "optimal_posting_time": true}}, {"id": "post_31_1757952900", "title": "5 Productivity Tips That Changed My Life", "description": "Join the conversation and share your thoughts!", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-15T16:15:00+00:00", "status": "draft", "tags": ["#marketing", "#productivity", "#tutorial", "#innovation", "#business"], "estimated_reach": 4591, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.6, "optimal_posting_time": true}}, {"id": "post_32_1757966400", "title": "Thread: 10 lessons learned this year", "description": "What strategies have worked best for you?", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-15T20:00:00+00:00", "status": "published", "tags": ["#inspiration", "#tips", "#technology", "#tutorial"], "estimated_reach": 4675, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.0, "optimal_posting_time": false}}, {"id": "post_33_1757967300", "title": "Behind the Scenes: Building a Startup", "description": "Breaking down complex topics into actionable steps", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-15T20:15:00+00:00", "status": "published", "tags": ["#innovation", "#inspiration", "#marketing", "#business"], "estimated_reach": 5351, "content_pillars": ["Education", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.5, "optimal_posting_time": true}}, {"id": "post_34_1758055500", "title": "Behind the Scenes: Building a Startup", "description": "What strategies have worked best for you?", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-16T20:45:00+00:00", "status": "failed", "tags": ["#inspiration", "#tutorial", "#marketing"], "estimated_reach": 1427, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.9, "optimal_posting_time": false}}, {"id": "post_35_1758124800", "title": "Review: Top Tools for Content Creators", "description": "What strategies have worked best for you?", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-17T16:00:00+00:00", "status": "failed", "tags": ["#business", "#innovation", "#productivity", "#tutorial"], "estimated_reach": 2126, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.6, "optimal_posting_time": true}}, {"id": "post_36_1758099600", "title": "Review: Top Tools for Content Creators", "description": "Breaking down complex topics into actionable steps", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-17T09:00:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#marketing", "#innovation", "#inspiration", "#technology"], "estimated_reach": 8685, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.2, "optimal_posting_time": false}}, {"id": "post_37_1758141000", "title": "Behind the Scenes: Building a Startup", "description": "Transform your approach with these proven methods", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-17T20:30:00+00:00", "status": "draft", "tags": ["#productivity", "#business", "#inspiration", "#tips"], "estimated_reach": 1231, "content_pillars": ["Inspiration", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.3, "optimal_posting_time": true}}, {"id": "post_38_1758188700", "title": "Personal update and big announcement", "description": "Here's what I learned and how you can apply it", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-18T09:45:00+00:00", "status": "published", "tags": ["#growth", "#marketing"], "estimated_reach": 4976, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.2, "optimal_posting_time": true}}, {"id": "post_39_1758225600", "title": "My Morning Routine for Success", "description": "Breaking down complex topics into actionable steps", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-18T20:00:00+00:00", "status": "published", "tags": ["#inspiration", "#growth"], "estimated_reach": 5340, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": true}}, {"id": "post_40_1758297600", "title": "My Morning Routine for Success", "description": "Breaking down complex topics into actionable steps", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-19T16:00:00+00:00", "status": "draft", "tags": ["#productivity", "#inspiration"], "estimated_reach": 2588, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.8, "optimal_posting_time": true}}, {"id": "post_41_1758299400", "title": "5 Productivity Tips That Changed My Life", "description": "What strategies have worked best for you?", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-19T16:30:00+00:00", "status": "draft", "tags": ["#inspiration", "#technology", "#growth", "#tips"], "estimated_reach": 2828, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.9, "optimal_posting_time": true}}, {"id": "post_42_1758275100", "title": "5 Productivity Tips That Changed My Life", "description": "Sharing valuable insights from my recent experience...", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-19T09:45:00+00:00", "status": "draft", "tags": ["#business", "#marketing", "#innovation", "#tutorial", "#productivity"], "estimated_reach": 8206, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.7, "optimal_posting_time": true}}, {"id": "post_43_1758384900", "title": "Thread: 10 lessons learned this year", "description": "What strategies have worked best for you?", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-20T16:15:00+00:00", "status": "draft", "tags": ["#growth", "#productivity", "#tips", "#business", "#tutorial"], "estimated_reach": 4402, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": true}}, {"id": "post_44_1758366900", "title": "Industry insight: What's changing", "description": "Here's what I learned and how you can apply it", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-20T11:15:00+00:00", "status": "failed", "tags": ["#tutorial", "#business", "#inspiration", "#productivity", "#tips"], "estimated_reach": 7589, "content_pillars": ["Education", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.7, "optimal_posting_time": true}}, {"id": "post_45_1758392100", "title": "Quick tip for better content creation", "description": "Breaking down complex topics into actionable steps", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-20T18:15:00+00:00", "status": "published", "tags": ["#marketing", "#innovation"], "estimated_reach": 1372, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.6, "optimal_posting_time": false}}, {"id": "post_46_1758454200", "title": "New Product Launch Coming Soon!", "description": "Join the conversation and share your thoughts!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-21T11:30:00+00:00", "status": "published", "tags": ["#tutorial", "#inspiration", "#productivity", "#marketing", "#technology"], "estimated_reach": 5084, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.8, "optimal_posting_time": false}}, {"id": "post_47_1758445200", "title": "Complete Guide to Social Media Marketing", "description": "Sharing valuable insights from my recent experience...", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-21T09:00:00+00:00", "status": "scheduled", "tags": ["#tips", "#business", "#tutorial"], "estimated_reach": 1355, "content_pillars": ["Entertainment", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.8, "optimal_posting_time": true}}, {"id": "post_48_1758485700", "title": "New Product Launch Coming Soon!", "description": "Join the conversation and share your thoughts!", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-21T20:15:00+00:00", "status": "scheduled", "tags": ["#marketing", "#technology", "#business", "#productivity"], "estimated_reach": 9702, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.5, "optimal_posting_time": true}}, {"id": "post_49_1758573900", "title": "Hot take: Social media strategy in 2024", "description": "What strategies have worked best for you?", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-22T20:45:00+00:00", "status": "failed", "tags": ["#business", "#tutorial", "#technology", "#marketing", "#productivity"], "estimated_reach": 9143, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.7, "optimal_posting_time": true}}, {"id": "post_50_1758636000", "title": "Weekend Vibes and Reflections", "description": "Join the conversation and share your thoughts!", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-23T14:00:00+00:00", "status": "failed", "tags": ["#inspiration", "#productivity"], "estimated_reach": 2747, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.5, "optimal_posting_time": true}}, {"id": "post_51_1758638700", "title": "Quick tip for better content creation", "description": "Breaking down complex topics into actionable steps", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-23T14:45:00+00:00", "status": "scheduled", "tags": ["#marketing", "#innovation"], "estimated_reach": 3324, "content_pillars": ["Education", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.9, "optimal_posting_time": true}}, {"id": "post_52_1758644100", "title": "5 Productivity Tips That Changed My Life", "description": "Sharing valuable insights from my recent experience...", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-23T16:15:00+00:00", "status": "draft", "tags": ["#innovation", "#growth", "#technology", "#tips", "#marketing"], "estimated_reach": 5988, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.3, "optimal_posting_time": false}}, {"id": "post_53_1758729600", "title": "Personal update and big announcement", "description": "Transform your approach with these proven methods", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-24T16:00:00+00:00", "status": "published", "tags": ["#business", "#innovation", "#inspiration", "#tips"], "estimated_reach": 8839, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.2, "optimal_posting_time": true}}, {"id": "post_54_1758713400", "title": "Industry insight: What's changing", "description": "Join the conversation and share your thoughts!", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-24T11:30:00+00:00", "status": "failed", "tags": ["#business", "#productivity", "#tips", "#technology", "#tutorial"], "estimated_reach": 6446, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_55_1758711600", "title": "Behind the Scenes: Building a Startup", "description": "Join the conversation and share your thoughts!", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-24T11:00:00+00:00", "status": "draft", "tags": ["#technology", "#growth"], "estimated_reach": 5502, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.9, "optimal_posting_time": false}}, {"id": "post_56_1758809700", "title": "Industry insight: What's changing", "description": "Let me know what you think in the comments below!", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-25T14:15:00+00:00", "status": "failed", "tags": ["#tutorial", "#inspiration", "#innovation"], "estimated_reach": 6772, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.0, "optimal_posting_time": false}}, {"id": "post_57_1758823200", "title": "New Product Launch Coming Soon!", "description": "Transform your approach with these proven methods", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-25T18:00:00+00:00", "status": "draft", "tags": ["#innovation", "#growth", "#inspiration", "#marketing"], "estimated_reach": 8022, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.6, "optimal_posting_time": true}}, {"id": "post_58_1758877200", "title": "Thread: 10 lessons learned this year", "description": "Let me know what you think in the comments below!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-26T09:00:00+00:00", "status": "draft", "tags": ["#innovation", "#productivity"], "estimated_reach": 9695, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.0, "optimal_posting_time": false}}, {"id": "post_59_1758903300", "title": "Review: Top Tools for Content Creators", "description": "Here's what I learned and how you can apply it", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-26T16:15:00+00:00", "status": "scheduled", "tags": ["#growth", "#technology"], "estimated_reach": 4524, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.4, "optimal_posting_time": true}}, {"id": "post_60_1758970800", "title": "Personal update and big announcement", "description": "Transform your approach with these proven methods", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-27T11:00:00+00:00", "status": "draft", "tags": ["#tutorial", "#growth"], "estimated_reach": 7111, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.6, "optimal_posting_time": true}}, {"id": "post_61_1758982500", "title": "Thread: 10 lessons learned this year", "description": "Join the conversation and share your thoughts!", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-27T14:15:00+00:00", "status": "failed", "tags": ["#business", "#productivity"], "estimated_reach": 1740, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.5, "optimal_posting_time": true}}, {"id": "post_62_1759058100", "title": "Industry insight: What's changing", "description": "Here's what I learned and how you can apply it", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-28T11:15:00+00:00", "status": "draft", "tags": ["#inspiration", "#innovation"], "estimated_reach": 5709, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": true}}, {"id": "post_63_1759068900", "title": "Swipe for Business Tips →", "description": "Let me know what you think in the comments below!", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-28T14:15:00+00:00", "status": "published", "tags": ["#innovation", "#growth", "#business", "#inspiration", "#technology"], "estimated_reach": 8802, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.3, "optimal_posting_time": true}}, {"id": "post_64_1759144500", "title": "Personal update and big announcement", "description": "Here's what I learned and how you can apply it", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-29T11:15:00+00:00", "status": "failed", "tags": ["#inspiration", "#business", "#technology"], "estimated_reach": 3756, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.4, "optimal_posting_time": true}}, {"id": "post_65_1759145400", "title": "Thread: 10 lessons learned this year", "description": "Join the conversation and share your thoughts!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-29T11:30:00+00:00", "status": "draft", "tags": ["#tutorial", "#inspiration", "#tips"], "estimated_reach": 4638, "content_pillars": ["Entertainment", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.4, "optimal_posting_time": true}}], "created_at": "2025-08-24T13:51:39.348096+00:00", "updated_at": "2025-08-25T23:51:39.348096+00:00"}, {"id": "plan_user_3_2_1758289899", "user_id": "user_3", "title": "30-Day Growth Strategy", "description": "Product promotion with authentic storytelling approach", "timeframe_start": "2025-09-19T13:51:39.348096+00:00", "timeframe_end": "2025-10-19T13:51:39.348096+00:00", "status": "paused", "platforms": ["youtube", "twitter"], "goals": ["Boost engagement", "Community building", "Drive website traffic"], "target_metrics": {"follower_growth": 7, "engagement_increase": 32, "content_frequency": "weekly"}, "posts": [{"id": "post_1_1758313800", "title": "Weekend Vibes and Reflections", "description": "Let me know what you think in the comments below!", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-19T20:30:00+00:00", "status": "scheduled", "tags": ["#marketing", "#inspiration", "#productivity", "#tips"], "estimated_reach": 3392, "content_pillars": ["Behind-the-scenes", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.5, "optimal_posting_time": true}}, {"id": "post_2_1758313800", "title": "Swipe for Business Tips →", "description": "Transform your approach with these proven methods", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-19T20:30:00+00:00", "status": "draft", "tags": ["#productivity", "#tutorial", "#inspiration", "#technology"], "estimated_reach": 1836, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.3, "optimal_posting_time": true}}, {"id": "post_3_1758312900", "title": "My Morning Routine for Success", "description": "Transform your approach with these proven methods", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-19T20:15:00+00:00", "status": "draft", "tags": ["#business", "#inspiration", "#productivity", "#growth"], "estimated_reach": 4292, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.3, "optimal_posting_time": true}}, {"id": "post_4_1758400200", "title": "My Morning Routine for Success", "description": "What strategies have worked best for you?", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-20T20:30:00+00:00", "status": "published", "tags": ["#productivity", "#innovation", "#marketing"], "estimated_reach": 7463, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.9, "optimal_posting_time": true}}, {"id": "post_5_1758384000", "title": "Weekend Vibes and Reflections", "description": "Save this post for later reference 📌", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-20T16:00:00+00:00", "status": "failed", "tags": ["#technology", "#growth"], "estimated_reach": 8704, "content_pillars": ["Behind-the-scenes", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.0, "optimal_posting_time": true}}, {"id": "post_6_1758368700", "title": "Personal update and big announcement", "description": "Let me know what you think in the comments below!", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-20T11:45:00+00:00", "status": "failed", "tags": ["#innovation", "#tutorial", "#marketing", "#business", "#productivity"], "estimated_reach": 2829, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.3, "optimal_posting_time": true}}, {"id": "post_7_1758447900", "title": "Weekend Vibes and Reflections", "description": "Join the conversation and share your thoughts!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-21T09:45:00+00:00", "status": "published", "tags": ["#growth", "#innovation", "#tutorial", "#productivity", "#business"], "estimated_reach": 1479, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.4, "optimal_posting_time": true}}, {"id": "post_8_1758533400", "title": "Review: Top Tools for Content Creators", "description": "Sharing valuable insights from my recent experience...", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-22T09:30:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#growth"], "estimated_reach": 4135, "content_pillars": ["Inspiration", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.9, "optimal_posting_time": true}}, {"id": "post_9_1758556800", "title": "Thread: 10 lessons learned this year", "description": "What strategies have worked best for you?", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-22T16:00:00+00:00", "status": "draft", "tags": ["#productivity", "#marketing", "#tutorial", "#technology", "#growth"], "estimated_reach": 8685, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.1, "optimal_posting_time": true}}, {"id": "post_10_1758533400", "title": "Thread: 10 lessons learned this year", "description": "What strategies have worked best for you?", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-22T09:30:00+00:00", "status": "published", "tags": ["#marketing", "#inspiration", "#tutorial", "#productivity"], "estimated_reach": 4421, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.6, "optimal_posting_time": true}}, {"id": "post_11_1758618000", "title": "Review: Top Tools for Content Creators", "description": "Sharing valuable insights from my recent experience...", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-23T09:00:00+00:00", "status": "published", "tags": ["#inspiration", "#growth", "#innovation"], "estimated_reach": 3152, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.7, "optimal_posting_time": false}}, {"id": "post_12_1758657600", "title": "My Morning Routine for Success", "description": "Sharing valuable insights from my recent experience...", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-23T20:00:00+00:00", "status": "failed", "tags": ["#technology", "#marketing", "#innovation"], "estimated_reach": 2870, "content_pillars": ["Inspiration", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.2, "optimal_posting_time": false}}, {"id": "post_13_1758618000", "title": "Quick tip for better content creation", "description": "Breaking down complex topics into actionable steps", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-23T09:00:00+00:00", "status": "scheduled", "tags": ["#business", "#innovation", "#inspiration", "#productivity", "#technology"], "estimated_reach": 3291, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.2, "optimal_posting_time": true}}, {"id": "post_14_1758738600", "title": "Review: Top Tools for Content Creators", "description": "Breaking down complex topics into actionable steps", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-24T18:30:00+00:00", "status": "published", "tags": ["#innovation", "#tips", "#productivity", "#tutorial", "#technology"], "estimated_reach": 4968, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.8, "optimal_posting_time": true}}, {"id": "post_15_1758704400", "title": "Monday Motivation: Start Strong", "description": "Here's what I learned and how you can apply it", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-24T09:00:00+00:00", "status": "published", "tags": ["#tutorial", "#growth", "#productivity", "#innovation"], "estimated_reach": 9471, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.9, "optimal_posting_time": true}}, {"id": "post_16_1758825900", "title": "Behind the Scenes: Building a Startup", "description": "Sharing valuable insights from my recent experience...", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-25T18:45:00+00:00", "status": "published", "tags": ["#marketing", "#tutorial", "#innovation", "#inspiration"], "estimated_reach": 2301, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.7, "optimal_posting_time": true}}, {"id": "post_17_1758811500", "title": "Quick tip for better content creation", "description": "Breaking down complex topics into actionable steps", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-25T14:45:00+00:00", "status": "published", "tags": ["#business", "#growth", "#innovation"], "estimated_reach": 8591, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.9, "optimal_posting_time": true}}, {"id": "post_18_1758904200", "title": "Industry insight: What's changing", "description": "Breaking down complex topics into actionable steps", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-26T16:30:00+00:00", "status": "failed", "tags": ["#growth", "#technology"], "estimated_reach": 9549, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.9, "optimal_posting_time": true}}, {"id": "post_19_1758916800", "title": "My Morning Routine for Success", "description": "Join the conversation and share your thoughts!", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-26T20:00:00+00:00", "status": "published", "tags": ["#technology", "#growth", "#business", "#innovation"], "estimated_reach": 2779, "content_pillars": ["Entertainment", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.5, "optimal_posting_time": true}}, {"id": "post_20_1758984300", "title": "Review: Top Tools for Content Creators", "description": "Join the conversation and share your thoughts!", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-27T14:45:00+00:00", "status": "draft", "tags": ["#productivity", "#business", "#technology", "#tips", "#tutorial"], "estimated_reach": 9114, "content_pillars": ["Education", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.6, "optimal_posting_time": false}}, {"id": "post_21_1759068000", "title": "Coffee and Creativity Session", "description": "Join the conversation and share your thoughts!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-28T14:00:00+00:00", "status": "draft", "tags": ["#inspiration", "#growth", "#business"], "estimated_reach": 1083, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.3, "optimal_posting_time": true}}, {"id": "post_22_1759075200", "title": "Swipe for Business Tips →", "description": "What strategies have worked best for you?", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-28T16:00:00+00:00", "status": "published", "tags": ["#inspiration", "#innovation", "#technology", "#growth"], "estimated_reach": 8194, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": false}}, {"id": "post_23_1759176000", "title": "Swipe for Business Tips →", "description": "Save this post for later reference 📌", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-29T20:00:00+00:00", "status": "scheduled", "tags": ["#productivity", "#tips", "#technology", "#marketing"], "estimated_reach": 9978, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.4, "optimal_posting_time": true}}, {"id": "post_24_1759257900", "title": "Monday Motivation: Start Strong", "description": "Here's what I learned and how you can apply it", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-30T18:45:00+00:00", "status": "draft", "tags": ["#business", "#growth", "#tutorial"], "estimated_reach": 4588, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.7, "optimal_posting_time": false}}, {"id": "post_25_1759310100", "title": "Industry insight: What's changing", "description": "Save this post for later reference 📌", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-01T09:15:00+00:00", "status": "published", "tags": ["#tutorial", "#business", "#tips", "#growth"], "estimated_reach": 5160, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.2, "optimal_posting_time": false}}, {"id": "post_26_1759404600", "title": "Personal update and big announcement", "description": "Transform your approach with these proven methods", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-02T11:30:00+00:00", "status": "published", "tags": ["#business", "#productivity", "#innovation", "#technology"], "estimated_reach": 1469, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.2, "optimal_posting_time": true}}, {"id": "post_27_1759436100", "title": "Monday Motivation: Start Strong", "description": "Here's what I learned and how you can apply it", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-02T20:15:00+00:00", "status": "published", "tags": ["#productivity", "#tips", "#technology"], "estimated_reach": 4663, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.6, "optimal_posting_time": true}}, {"id": "post_28_1759491900", "title": "Complete Guide to Social Media Marketing", "description": "Save this post for later reference 📌", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-03T11:45:00+00:00", "status": "scheduled", "tags": ["#growth", "#technology", "#business", "#tutorial", "#innovation"], "estimated_reach": 3221, "content_pillars": ["Inspiration", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.9, "optimal_posting_time": true}}, {"id": "post_29_1759608900", "title": "Quick tip for better content creation", "description": "Transform your approach with these proven methods", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-04T20:15:00+00:00", "status": "failed", "tags": ["#inspiration", "#business", "#technology", "#tips", "#tutorial"], "estimated_reach": 5455, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": false}}, {"id": "post_30_1759588200", "title": "Swipe for Business Tips →", "description": "Join the conversation and share your thoughts!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-04T14:30:00+00:00", "status": "draft", "tags": ["#business", "#marketing"], "estimated_reach": 2738, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": false}}, {"id": "post_31_1759695300", "title": "Complete Guide to Social Media Marketing", "description": "Here's what I learned and how you can apply it", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-05T20:15:00+00:00", "status": "draft", "tags": ["#growth", "#productivity"], "estimated_reach": 6063, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.8, "optimal_posting_time": true}}, {"id": "post_32_1759664700", "title": "Coffee and Creativity Session", "description": "Save this post for later reference 📌", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-05T11:45:00+00:00", "status": "failed", "tags": ["#productivity", "#inspiration", "#tips", "#technology"], "estimated_reach": 5030, "content_pillars": ["Inspiration", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.2, "optimal_posting_time": true}}, {"id": "post_33_1759687200", "title": "Hot take: Social media strategy in 2024", "description": "Let me know what you think in the comments below!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-05T18:00:00+00:00", "status": "published", "tags": ["#marketing", "#inspiration", "#productivity", "#business"], "estimated_reach": 5501, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.1, "optimal_posting_time": true}}, {"id": "post_34_1759768200", "title": "Industry insight: What's changing", "description": "Transform your approach with these proven methods", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-06T16:30:00+00:00", "status": "draft", "tags": ["#tips", "#innovation"], "estimated_reach": 1553, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.7, "optimal_posting_time": false}}, {"id": "post_35_1759775400", "title": "Weekend Vibes and Reflections", "description": "Here's what I learned and how you can apply it", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-06T18:30:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#tutorial", "#productivity", "#innovation"], "estimated_reach": 1534, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_36_1759759200", "title": "5 Productivity Tips That Changed My Life", "description": "Save this post for later reference 📌", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-06T14:00:00+00:00", "status": "draft", "tags": ["#marketing", "#technology", "#innovation", "#business"], "estimated_reach": 8080, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.4, "optimal_posting_time": false}}, {"id": "post_37_1759836600", "title": "New Product Launch Coming Soon!", "description": "Here's what I learned and how you can apply it", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-07T11:30:00+00:00", "status": "draft", "tags": ["#growth", "#tips", "#technology", "#marketing"], "estimated_reach": 2636, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.8, "optimal_posting_time": true}}, {"id": "post_38_1759946400", "title": "Personal update and big announcement", "description": "Save this post for later reference 📌", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-08T18:00:00+00:00", "status": "scheduled", "tags": ["#technology", "#business", "#inspiration"], "estimated_reach": 1210, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.4, "optimal_posting_time": false}}, {"id": "post_39_1759933800", "title": "Monday Motivation: Start Strong", "description": "Sharing valuable insights from my recent experience...", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-08T14:30:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#tips"], "estimated_reach": 9624, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.4, "optimal_posting_time": true}}, {"id": "post_40_1760010300", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-09T11:45:00+00:00", "status": "failed", "tags": ["#inspiration", "#growth", "#productivity", "#innovation", "#tips"], "estimated_reach": 7230, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.3, "optimal_posting_time": true}}, {"id": "post_41_1760025600", "title": "New Product Launch Coming Soon!", "description": "What strategies have worked best for you?", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-09T16:00:00+00:00", "status": "scheduled", "tags": ["#business", "#inspiration", "#marketing", "#growth", "#technology"], "estimated_reach": 5382, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.0, "optimal_posting_time": true}}, {"id": "post_42_1760113800", "title": "Industry insight: What's changing", "description": "Transform your approach with these proven methods", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-10T16:30:00+00:00", "status": "published", "tags": ["#tutorial", "#growth", "#technology", "#tips"], "estimated_reach": 6593, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": false}}, {"id": "post_43_1760120100", "title": "My Morning Routine for Success", "description": "Here's what I learned and how you can apply it", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-10T18:15:00+00:00", "status": "published", "tags": ["#tutorial", "#business", "#productivity", "#growth"], "estimated_reach": 3568, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.4, "optimal_posting_time": true}}, {"id": "post_44_1760206500", "title": "Industry insight: What's changing", "description": "Save this post for later reference 📌", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-11T18:15:00+00:00", "status": "published", "tags": ["#tutorial", "#innovation", "#growth"], "estimated_reach": 2216, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.5, "optimal_posting_time": false}}, {"id": "post_45_1760201100", "title": "5 Productivity Tips That Changed My Life", "description": "Sharing valuable insights from my recent experience...", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-11T16:45:00+00:00", "status": "draft", "tags": ["#innovation", "#tutorial", "#marketing", "#tips", "#technology"], "estimated_reach": 2336, "content_pillars": ["Education", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.2, "optimal_posting_time": true}}, {"id": "post_46_1760260500", "title": "My Morning Routine for Success", "description": "Join the conversation and share your thoughts!", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-12T09:15:00+00:00", "status": "draft", "tags": ["#technology", "#productivity"], "estimated_reach": 7404, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.8, "optimal_posting_time": true}}, {"id": "post_47_1760299200", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-12T20:00:00+00:00", "status": "failed", "tags": ["#marketing", "#tutorial", "#innovation", "#growth"], "estimated_reach": 2942, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.3, "optimal_posting_time": true}}, {"id": "post_48_1760346900", "title": "Behind the Scenes: Building a Startup", "description": "Sharing valuable insights from my recent experience...", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-13T09:15:00+00:00", "status": "scheduled", "tags": ["#business", "#inspiration"], "estimated_reach": 7591, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.7, "optimal_posting_time": true}}, {"id": "post_49_1760388300", "title": "Swipe for Business Tips →", "description": "Breaking down complex topics into actionable steps", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-13T20:45:00+00:00", "status": "draft", "tags": ["#productivity", "#innovation", "#technology"], "estimated_reach": 8083, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": false}}, {"id": "post_50_1760372100", "title": "Industry insight: What's changing", "description": "Join the conversation and share your thoughts!", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-13T16:15:00+00:00", "status": "scheduled", "tags": ["#innovation", "#tips", "#business"], "estimated_reach": 9368, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.8, "optimal_posting_time": true}}, {"id": "post_51_1760452200", "title": "Thread: 10 lessons learned this year", "description": "Here's what I learned and how you can apply it", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-14T14:30:00+00:00", "status": "failed", "tags": ["#growth", "#productivity", "#innovation", "#technology"], "estimated_reach": 1526, "content_pillars": ["Education", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.5, "optimal_posting_time": true}}, {"id": "post_52_1760467500", "title": "Complete Guide to Social Media Marketing", "description": "Transform your approach with these proven methods", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-14T18:45:00+00:00", "status": "failed", "tags": ["#marketing", "#productivity", "#tips"], "estimated_reach": 5435, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.5, "optimal_posting_time": true}}, {"id": "post_53_1760544900", "title": "Monday Motivation: Start Strong", "description": "Let me know what you think in the comments below!", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-15T16:15:00+00:00", "status": "draft", "tags": ["#productivity", "#tips", "#innovation"], "estimated_reach": 5067, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": true}}, {"id": "post_54_1760537700", "title": "Swipe for Business Tips →", "description": "What strategies have worked best for you?", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-15T14:15:00+00:00", "status": "published", "tags": ["#inspiration", "#growth"], "estimated_reach": 7449, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.5, "optimal_posting_time": true}}, {"id": "post_55_1760632200", "title": "Hot take: Social media strategy in 2024", "description": "Breaking down complex topics into actionable steps", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-16T16:30:00+00:00", "status": "failed", "tags": ["#tutorial", "#productivity"], "estimated_reach": 8659, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.5, "optimal_posting_time": true}}, {"id": "post_56_1760698800", "title": "Complete Guide to Social Media Marketing", "description": "Breaking down complex topics into actionable steps", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-17T11:00:00+00:00", "status": "draft", "tags": ["#technology", "#productivity", "#innovation"], "estimated_reach": 7314, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.8, "optimal_posting_time": true}}, {"id": "post_57_1760701500", "title": "Complete Guide to Social Media Marketing", "description": "Sharing valuable insights from my recent experience...", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-17T11:45:00+00:00", "status": "draft", "tags": ["#innovation", "#tutorial"], "estimated_reach": 2093, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.2, "optimal_posting_time": false}}, {"id": "post_58_1760725800", "title": "Review: Top Tools for Content Creators", "description": "Sharing valuable insights from my recent experience...", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-17T18:30:00+00:00", "status": "published", "tags": ["#inspiration", "#growth", "#productivity"], "estimated_reach": 1920, "content_pillars": ["Behind-the-scenes", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.6, "optimal_posting_time": false}}, {"id": "post_59_1760779800", "title": "Coffee and Creativity Session", "description": "Here's what I learned and how you can apply it", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-18T09:30:00+00:00", "status": "published", "tags": ["#tutorial", "#productivity", "#tips", "#technology"], "estimated_reach": 6484, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.5, "optimal_posting_time": false}}, {"id": "post_60_1760804100", "title": "New Product Launch Coming Soon!", "description": "What strategies have worked best for you?", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-18T16:15:00+00:00", "status": "scheduled", "tags": ["#growth", "#business"], "estimated_reach": 5410, "content_pillars": ["Behind-the-scenes", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.6, "optimal_posting_time": true}}, {"id": "post_61_1760817600", "title": "Behind the Scenes: Building a Startup", "description": "Breaking down complex topics into actionable steps", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-18T20:00:00+00:00", "status": "published", "tags": ["#tutorial", "#tips", "#innovation", "#business"], "estimated_reach": 1285, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.3, "optimal_posting_time": true}}, {"id": "post_62_1760889600", "title": "Thread: 10 lessons learned this year", "description": "Let me know what you think in the comments below!", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-19T16:00:00+00:00", "status": "draft", "tags": ["#technology", "#tutorial", "#inspiration", "#marketing"], "estimated_reach": 9578, "content_pillars": ["Entertainment", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.9, "optimal_posting_time": true}}, {"id": "post_63_1760904000", "title": "Personal update and big announcement", "description": "Sharing valuable insights from my recent experience...", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-19T20:00:00+00:00", "status": "published", "tags": ["#tips", "#marketing", "#tutorial", "#innovation", "#technology"], "estimated_reach": 4546, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.6, "optimal_posting_time": false}}, {"id": "post_64_1760885100", "title": "New Product Launch Coming Soon!", "description": "Breaking down complex topics into actionable steps", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-19T14:45:00+00:00", "status": "draft", "tags": ["#innovation", "#tips"], "estimated_reach": 3828, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.3, "optimal_posting_time": true}}], "created_at": "2025-09-15T13:51:39.348096+00:00", "updated_at": "2025-08-26T23:51:39.348096+00:00"}, {"id": "plan_user_3_3_1758030699", "user_id": "user_3", "title": "30-Day Growth Strategy", "description": "Educational content series to establish thought leadership", "timeframe_start": "2025-09-16T13:51:39.348096+00:00", "timeframe_end": "2025-10-16T13:51:39.348096+00:00", "status": "draft", "platforms": ["twitter", "youtube", "instagram"], "goals": ["Generate leads", "Drive website traffic"], "target_metrics": {"follower_growth": 12, "engagement_increase": 28, "content_frequency": "weekly"}, "posts": [{"id": "post_1_1758039300", "title": "Behind the Scenes: Building a Startup", "description": "Join the conversation and share your thoughts!", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-16T16:15:00+00:00", "status": "failed", "tags": ["#tutorial", "#innovation"], "estimated_reach": 4726, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.1, "optimal_posting_time": true}}, {"id": "post_2_1758127500", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-17T16:45:00+00:00", "status": "scheduled", "tags": ["#growth", "#business", "#technology", "#tips", "#tutorial"], "estimated_reach": 4531, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": true}}, {"id": "post_3_1758206700", "title": "My Morning Routine for Success", "description": "Let me know what you think in the comments below!", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-18T14:45:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#technology", "#productivity", "#tips", "#tutorial"], "estimated_reach": 3803, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.2, "optimal_posting_time": false}}, {"id": "post_4_1758193200", "title": "Behind the Scenes: Building a Startup", "description": "Save this post for later reference 📌", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-18T11:00:00+00:00", "status": "scheduled", "tags": ["#marketing", "#innovation"], "estimated_reach": 7024, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.9, "optimal_posting_time": true}}, {"id": "post_5_1758304800", "title": "My Morning Routine for Success", "description": "Sharing valuable insights from my recent experience...", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-19T18:00:00+00:00", "status": "draft", "tags": ["#growth", "#inspiration"], "estimated_reach": 2494, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.7, "optimal_posting_time": true}}, {"id": "post_6_1758401100", "title": "Behind the Scenes: Building a Startup", "description": "Save this post for later reference 📌", "content_type": "educational", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-20T20:45:00+00:00", "status": "scheduled", "tags": ["#productivity", "#tutorial", "#inspiration", "#technology"], "estimated_reach": 2778, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.0, "optimal_posting_time": false}}, {"id": "post_7_1758377700", "title": "5 Productivity Tips That Changed My Life", "description": "Sharing valuable insights from my recent experience...", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-20T14:15:00+00:00", "status": "scheduled", "tags": ["#growth", "#innovation"], "estimated_reach": 4759, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.5, "optimal_posting_time": false}}, {"id": "post_8_1758367800", "title": "Quick tip for better content creation", "description": "Transform your approach with these proven methods", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-20T11:30:00+00:00", "status": "failed", "tags": ["#innovation", "#business"], "estimated_reach": 8890, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.3, "optimal_posting_time": true}}, {"id": "post_9_1758487500", "title": "Quick tip for better content creation", "description": "What strategies have worked best for you?", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-21T20:45:00+00:00", "status": "published", "tags": ["#business", "#tips", "#innovation", "#inspiration", "#technology"], "estimated_reach": 8749, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.4, "optimal_posting_time": true}}, {"id": "post_10_1758573900", "title": "Thread: 10 lessons learned this year", "description": "Breaking down complex topics into actionable steps", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-22T20:45:00+00:00", "status": "published", "tags": ["#productivity", "#business", "#technology"], "estimated_reach": 3103, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": true}}, {"id": "post_11_1758540600", "title": "My Morning Routine for Success", "description": "Breaking down complex topics into actionable steps", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-22T11:30:00+00:00", "status": "scheduled", "tags": ["#tips", "#technology"], "estimated_reach": 8145, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.9, "optimal_posting_time": false}}, {"id": "post_12_1758532500", "title": "My Morning Routine for Success", "description": "Save this post for later reference 📌", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-22T09:15:00+00:00", "status": "failed", "tags": ["#productivity", "#growth", "#marketing"], "estimated_reach": 6908, "content_pillars": ["Education", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.5, "optimal_posting_time": true}}, {"id": "post_13_1758644100", "title": "Coffee and Creativity Session", "description": "Breaking down complex topics into actionable steps", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-23T16:15:00+00:00", "status": "draft", "tags": ["#tutorial", "#marketing", "#growth"], "estimated_reach": 1650, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.5, "optimal_posting_time": true}}, {"id": "post_14_1758729600", "title": "My Morning Routine for Success", "description": "Breaking down complex topics into actionable steps", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-24T16:00:00+00:00", "status": "draft", "tags": ["#marketing", "#inspiration", "#tips", "#productivity", "#innovation"], "estimated_reach": 7589, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": true}}, {"id": "post_15_1758793500", "title": "Monday Motivation: Start Strong", "description": "Breaking down complex topics into actionable steps", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-25T09:45:00+00:00", "status": "draft", "tags": ["#innovation", "#technology", "#tutorial", "#business", "#growth"], "estimated_reach": 2274, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.6, "optimal_posting_time": true}}, {"id": "post_16_1758790800", "title": "5 Productivity Tips That Changed My Life", "description": "Save this post for later reference 📌", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-25T09:00:00+00:00", "status": "published", "tags": ["#productivity", "#innovation", "#marketing", "#inspiration", "#growth"], "estimated_reach": 9730, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.1, "optimal_posting_time": false}}, {"id": "post_17_1758911400", "title": "Thread: 10 lessons learned this year", "description": "Save this post for later reference 📌", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-26T18:30:00+00:00", "status": "published", "tags": ["#productivity", "#tips"], "estimated_reach": 6299, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.2, "optimal_posting_time": true}}, {"id": "post_18_1758998700", "title": "Behind the Scenes: Building a Startup", "description": "Save this post for later reference 📌", "content_type": "review", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-27T18:45:00+00:00", "status": "draft", "tags": ["#marketing", "#growth", "#technology"], "estimated_reach": 3073, "content_pillars": ["Behind-the-scenes", "Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": true}}, {"id": "post_19_1758966300", "title": "Hot take: Social media strategy in 2024", "description": "Breaking down complex topics into actionable steps", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-27T09:45:00+00:00", "status": "published", "tags": ["#technology", "#marketing", "#innovation", "#growth", "#tips"], "estimated_reach": 5514, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.4, "optimal_posting_time": false}}, {"id": "post_20_1759091400", "title": "Monday Motivation: Start Strong", "description": "What strategies have worked best for you?", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-28T20:30:00+00:00", "status": "scheduled", "tags": ["#productivity", "#marketing", "#tutorial", "#technology", "#innovation"], "estimated_reach": 2375, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": true}}, {"id": "post_21_1759137300", "title": "5 Productivity Tips That Changed My Life", "description": "Here's what I learned and how you can apply it", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-09-29T09:15:00+00:00", "status": "failed", "tags": ["#inspiration", "#innovation", "#business", "#growth", "#tips"], "estimated_reach": 2458, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.7, "optimal_posting_time": false}}, {"id": "post_22_1759146300", "title": "Hot take: Social media strategy in 2024", "description": "Let me know what you think in the comments below!", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-29T11:45:00+00:00", "status": "draft", "tags": ["#technology", "#growth", "#marketing", "#tips"], "estimated_reach": 5407, "content_pillars": ["Inspiration", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 2.1, "optimal_posting_time": true}}, {"id": "post_23_1759168800", "title": "Weekend Vibes and Reflections", "description": "Sharing valuable insights from my recent experience...", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-29T18:00:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#innovation"], "estimated_reach": 2679, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.1, "optimal_posting_time": true}}, {"id": "post_24_1759240800", "title": "Monday Motivation: Start Strong", "description": "Transform your approach with these proven methods", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-09-30T14:00:00+00:00", "status": "scheduled", "tags": ["#innovation", "#tutorial", "#business"], "estimated_reach": 2667, "content_pillars": ["Education"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.9, "optimal_posting_time": false}}, {"id": "post_25_1759249800", "title": "Personal update and big announcement", "description": "Transform your approach with these proven methods", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-09-30T16:30:00+00:00", "status": "scheduled", "tags": ["#productivity", "#business", "#inspiration", "#tutorial"], "estimated_reach": 2089, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.8, "optimal_posting_time": true}}, {"id": "post_26_1759350600", "title": "Personal update and big announcement", "description": "Join the conversation and share your thoughts!", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-01T20:30:00+00:00", "status": "scheduled", "tags": ["#business", "#marketing"], "estimated_reach": 7224, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.6, "optimal_posting_time": true}}, {"id": "post_27_1759316400", "title": "Complete Guide to Social Media Marketing", "description": "Transform your approach with these proven methods", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-01T11:00:00+00:00", "status": "scheduled", "tags": ["#productivity", "#marketing", "#tips", "#innovation", "#inspiration"], "estimated_reach": 3855, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.0, "optimal_posting_time": true}}, {"id": "post_28_1759414500", "title": "Coffee and Creativity Session", "description": "What strategies have worked best for you?", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-02T14:15:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#productivity"], "estimated_reach": 7530, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": false}}, {"id": "post_29_1759398300", "title": "Industry insight: What's changing", "description": "Let me know what you think in the comments below!", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-02T09:45:00+00:00", "status": "draft", "tags": ["#inspiration", "#productivity", "#innovation"], "estimated_reach": 2755, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.0, "optimal_posting_time": true}}, {"id": "post_30_1759421700", "title": "Review: Top Tools for Content Creators", "description": "Breaking down complex topics into actionable steps", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-02T16:15:00+00:00", "status": "published", "tags": ["#tutorial", "#business", "#marketing"], "estimated_reach": 9795, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.6, "optimal_posting_time": true}}, {"id": "post_31_1759523400", "title": "Personal update and big announcement", "description": "What strategies have worked best for you?", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-03T20:30:00+00:00", "status": "scheduled", "tags": ["#business", "#inspiration", "#technology", "#tips"], "estimated_reach": 1194, "content_pillars": ["Education", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": true}}, {"id": "post_32_1759524300", "title": "Personal update and big announcement", "description": "Breaking down complex topics into actionable steps", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-03T20:45:00+00:00", "status": "failed", "tags": ["#productivity", "#technology", "#tutorial"], "estimated_reach": 5306, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 8.0, "optimal_posting_time": true}}, {"id": "post_33_1759514400", "title": "Weekend Vibes and Reflections", "description": "Here's what I learned and how you can apply it", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-03T18:00:00+00:00", "status": "published", "tags": ["#inspiration", "#marketing", "#growth"], "estimated_reach": 4575, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.5, "optimal_posting_time": false}}, {"id": "post_34_1759594500", "title": "New Product Launch Coming Soon!", "description": "Transform your approach with these proven methods", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-04T16:15:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#tips", "#growth", "#marketing", "#technology"], "estimated_reach": 5397, "content_pillars": ["Behind-the-scenes", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.8, "optimal_posting_time": false}}, {"id": "post_35_1759588200", "title": "Monday Motivation: Start Strong", "description": "Let me know what you think in the comments below!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-04T14:30:00+00:00", "status": "draft", "tags": ["#innovation", "#business"], "estimated_reach": 4671, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.1, "optimal_posting_time": true}}, {"id": "post_36_1759587300", "title": "Industry insight: What's changing", "description": "What strategies have worked best for you?", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-04T14:15:00+00:00", "status": "scheduled", "tags": ["#marketing", "#tips", "#business", "#tutorial", "#technology"], "estimated_reach": 4550, "content_pillars": ["Entertainment", "Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.1, "optimal_posting_time": false}}, {"id": "post_37_1759673700", "title": "Hot take: Social media strategy in 2024", "description": "Transform your approach with these proven methods", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-05T14:15:00+00:00", "status": "failed", "tags": ["#tutorial", "#tips", "#business", "#innovation"], "estimated_reach": 9366, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.9, "optimal_posting_time": true}}, {"id": "post_38_1759688100", "title": "5 Productivity Tips That Changed My Life", "description": "What strategies have worked best for you?", "content_type": "tutorial", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-05T18:15:00+00:00", "status": "published", "tags": ["#tips", "#marketing", "#tutorial", "#growth"], "estimated_reach": 4689, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.3, "optimal_posting_time": false}}, {"id": "post_39_1759654800", "title": "Monday Motivation: Start Strong", "description": "Breaking down complex topics into actionable steps", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-05T09:00:00+00:00", "status": "scheduled", "tags": ["#inspiration", "#tutorial"], "estimated_reach": 5941, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.3, "optimal_posting_time": false}}, {"id": "post_40_1759767300", "title": "Hot take: Social media strategy in 2024", "description": "Breaking down complex topics into actionable steps", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-06T16:15:00+00:00", "status": "failed", "tags": ["#inspiration", "#innovation"], "estimated_reach": 6183, "content_pillars": ["Inspiration", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.1, "optimal_posting_time": false}}, {"id": "post_41_1759868100", "title": "Hot take: Social media strategy in 2024", "description": "Sharing valuable insights from my recent experience...", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-07T20:15:00+00:00", "status": "draft", "tags": ["#technology", "#growth"], "estimated_reach": 5739, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.3, "optimal_posting_time": true}}, {"id": "post_42_1759941900", "title": "Quick tip for better content creation", "description": "Save this post for later reference 📌", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-08T16:45:00+00:00", "status": "draft", "tags": ["#marketing", "#business"], "estimated_reach": 6911, "content_pillars": ["Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.8, "optimal_posting_time": true}}, {"id": "post_43_1760009400", "title": "Swipe for Business Tips →", "description": "Save this post for later reference 📌", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-09T11:30:00+00:00", "status": "failed", "tags": ["#business", "#growth", "#innovation", "#tips"], "estimated_reach": 8684, "content_pillars": ["Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 3.0, "optimal_posting_time": false}}, {"id": "post_44_1760019300", "title": "Swipe for Business Tips →", "description": "Save this post for later reference 📌", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-09T14:15:00+00:00", "status": "failed", "tags": ["#tips", "#tutorial", "#innovation"], "estimated_reach": 6389, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 7.3, "optimal_posting_time": true}}, {"id": "post_45_1760129100", "title": "Personal update and big announcement", "description": "Transform your approach with these proven methods", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-10T20:45:00+00:00", "status": "draft", "tags": ["#growth", "#technology", "#tips"], "estimated_reach": 8605, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": true, "performance_prediction": {"expected_engagement": 6.1, "optimal_posting_time": true}}, {"id": "post_46_1760120100", "title": "Coffee and Creativity Session", "description": "Let me know what you think in the comments below!", "content_type": "post", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-10T18:15:00+00:00", "status": "scheduled", "tags": ["#productivity", "#growth", "#inspiration", "#business", "#innovation"], "estimated_reach": 7706, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.9, "optimal_posting_time": false}}, {"id": "post_47_1760213700", "title": "Behind the Scenes: Building a Startup", "description": "What strategies have worked best for you?", "content_type": "interview", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-11T20:15:00+00:00", "status": "failed", "tags": ["#marketing", "#technology", "#inspiration", "#productivity", "#tips"], "estimated_reach": 8089, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.7, "optimal_posting_time": true}}, {"id": "post_48_1760200200", "title": "Swipe for Business Tips →", "description": "Breaking down complex topics into actionable steps", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-11T16:30:00+00:00", "status": "published", "tags": ["#innovation", "#tutorial", "#growth", "#technology", "#marketing"], "estimated_reach": 2756, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 7.0, "optimal_posting_time": true}}, {"id": "post_49_1760193000", "title": "Swipe for Business Tips →", "description": "Breaking down complex topics into actionable steps", "content_type": "story", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-11T14:30:00+00:00", "status": "published", "tags": ["#technology", "#inspiration", "#innovation"], "estimated_reach": 7837, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 6.4, "optimal_posting_time": false}}, {"id": "post_50_1760269500", "title": "Complete Guide to Social Media Marketing", "description": "Transform your approach with these proven methods", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-12T11:45:00+00:00", "status": "scheduled", "tags": ["#tutorial", "#business", "#inspiration", "#innovation", "#tips"], "estimated_reach": 1955, "content_pillars": ["Inspiration", "Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.4, "optimal_posting_time": true}}, {"id": "post_51_1760268600", "title": "Quick tip for better content creation", "description": "Breaking down complex topics into actionable steps", "content_type": "thread", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-12T11:30:00+00:00", "status": "published", "tags": ["#business", "#inspiration", "#productivity", "#technology", "#growth"], "estimated_reach": 2823, "content_pillars": ["Education", "Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.6, "optimal_posting_time": true}}, {"id": "post_52_1760378400", "title": "Swipe for Business Tips →", "description": "Save this post for later reference 📌", "content_type": "igtv", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-13T18:00:00+00:00", "status": "failed", "tags": ["#marketing", "#technology", "#business"], "estimated_reach": 9020, "content_pillars": ["Entertainment"], "ai_generated": false, "performance_prediction": {"expected_engagement": 4.0, "optimal_posting_time": false}}, {"id": "post_53_1760380200", "title": "Behind the Scenes: Building a Startup", "description": "Breaking down complex topics into actionable steps", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-13T18:30:00+00:00", "status": "draft", "tags": ["#inspiration", "#tutorial"], "estimated_reach": 6131, "content_pillars": ["Entertainment", "Behind-the-scenes"], "ai_generated": false, "performance_prediction": {"expected_engagement": 5.1, "optimal_posting_time": false}}, {"id": "post_54_1760373900", "title": "5 Productivity Tips That Changed My Life", "description": "Transform your approach with these proven methods", "content_type": "entertainment", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-13T16:45:00+00:00", "status": "failed", "tags": ["#technology", "#tutorial"], "estimated_reach": 7557, "content_pillars": ["Entertainment", "Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.2, "optimal_posting_time": false}}, {"id": "post_55_1760439600", "title": "Quick tip for better content creation", "description": "Breaking down complex topics into actionable steps", "content_type": "quote_tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-14T11:00:00+00:00", "status": "draft", "tags": ["#innovation", "#business"], "estimated_reach": 9936, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.1, "optimal_posting_time": true}}, {"id": "post_56_1760439600", "title": "Complete Guide to Social Media Marketing", "description": "Join the conversation and share your thoughts!", "content_type": "vlog", "platform": "youtube", "account_id": "youtube_user_3_1048", "scheduled_time": "2025-10-14T11:00:00+00:00", "status": "draft", "tags": ["#innovation", "#tutorial"], "estimated_reach": 4699, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 3.9, "optimal_posting_time": false}}, {"id": "post_57_1760453100", "title": "Hot take: Social media strategy in 2024", "description": "Let me know what you think in the comments below!", "content_type": "tweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-14T14:45:00+00:00", "status": "failed", "tags": ["#growth", "#business", "#productivity"], "estimated_reach": 2675, "content_pillars": ["Inspiration"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.9, "optimal_posting_time": true}}, {"id": "post_58_1760561100", "title": "Swipe for Business Tips →", "description": "Save this post for later reference 📌", "content_type": "reel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-15T20:45:00+00:00", "status": "draft", "tags": ["#productivity", "#innovation", "#marketing", "#technology", "#inspiration"], "estimated_reach": 4620, "content_pillars": ["Inspiration"], "ai_generated": false, "performance_prediction": {"expected_engagement": 2.1, "optimal_posting_time": true}}, {"id": "post_59_1760637600", "title": "Coffee and Creativity Session", "description": "What strategies have worked best for you?", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-16T18:00:00+00:00", "status": "failed", "tags": ["#inspiration", "#growth", "#innovation", "#tutorial"], "estimated_reach": 8727, "content_pillars": ["Education"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.7, "optimal_posting_time": false}}, {"id": "post_60_1760638500", "title": "Coffee and Creativity Session", "description": "Let me know what you think in the comments below!", "content_type": "carousel", "platform": "instagram", "account_id": "instagram_user_3_4872", "scheduled_time": "2025-10-16T18:15:00+00:00", "status": "failed", "tags": ["#inspiration", "#business", "#innovation"], "estimated_reach": 9930, "content_pillars": ["Education", "Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 4.0, "optimal_posting_time": true}}, {"id": "post_61_1760624100", "title": "Hot take: Social media strategy in 2024", "description": "Here's what I learned and how you can apply it", "content_type": "retweet", "platform": "twitter", "account_id": "twitter_user_3_5354", "scheduled_time": "2025-10-16T14:15:00+00:00", "status": "draft", "tags": ["#technology", "#tips", "#innovation"], "estimated_reach": 6824, "content_pillars": ["Behind-the-scenes"], "ai_generated": true, "performance_prediction": {"expected_engagement": 5.8, "optimal_posting_time": true}}], "created_at": "2025-09-14T13:51:39.348096+00:00", "updated_at": "2025-08-24T21:51:39.348096+00:00"}], "chat_messages": [{"id": "msg_user_8_1756302699", "user_id": "user_1", "role": "user", "content": "Create a content plan for the next 14 days", "timestamp": "2025-07-29T02:54:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": "youtube", "intent": "planning"}}, {"id": "msg_ai_8_1756302699", "user_id": "user_1", "role": "assistant", "content": "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.", "timestamp": "2025-07-29T02:54:55.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.97, "processing_time": 4.4, "data_sources": ["instagram_api"]}}, {"id": "msg_user_6_1756302699", "user_id": "user_1", "role": "user", "content": "What hashtags should I use for my fitness content?", "timestamp": "2025-08-02T20:14:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": "twitter", "intent": "analysis"}}, {"id": "msg_ai_6_1756302699", "user_id": "user_1", "role": "assistant", "content": "Based on your analytics, your YouTube channel has shown strong growth with a 12.5% increase in subscribers this month. Your average view duration has improved to 4.2 minutes, indicating higher engagement.", "timestamp": "2025-08-02T20:14:52.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.98, "processing_time": 2.5, "data_sources": ["youtube_api", "instagram_api", "ai_model"]}}, {"id": "msg_user_3_1756302699", "user_id": "user_1", "role": "user", "content": "Can you analyze my Instagram engagement rate?", "timestamp": "2025-08-05T19:12:39.348096+00:00", "session_id": "session_2", "metadata": {"platform_context": "twitter", "intent": "analysis"}}, {"id": "msg_ai_3_1756302699", "user_id": "user_1", "role": "assistant", "content": "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach).", "timestamp": "2025-08-05T19:12:59.348096+00:00", "session_id": "session_2", "metadata": {"response_type": "analysis", "confidence_score": 0.92, "processing_time": 4.5, "data_sources": ["instagram_api", "ai_model"]}}, {"id": "msg_user_5_1756302699", "user_id": "user_1", "role": "user", "content": "Help me plan content for product launch", "timestamp": "2025-08-10T01:47:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": "instagram", "intent": "optimization"}}, {"id": "msg_ai_5_1756302699", "user_id": "user_1", "role": "assistant", "content": "I recommend focusing on educational content based on your audience preferences. Consider creating 'How-to' tutorials and behind-the-scenes content for maximum engagement.", "timestamp": "2025-08-10T01:47:49.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.85, "processing_time": 7.9, "data_sources": ["analytics_db", "youtube_api"]}}, {"id": "msg_user_9_1756302699", "user_id": "user_1", "role": "user", "content": "Help me plan content for product launch", "timestamp": "2025-08-11T18:59:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": "twitter", "intent": "planning"}}, {"id": "msg_ai_9_1756302699", "user_id": "user_1", "role": "assistant", "content": "To improve follower growth, I suggest: 1) Posting consistently at optimal times, 2) Using trending hashtags relevant to your niche, 3) Engaging with your audience within the first hour of posting.", "timestamp": "2025-08-11T18:59:49.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.98, "processing_time": 7.1, "data_sources": ["analytics_db", "youtube_api", "instagram_api"]}}, {"id": "msg_user_7_1756302699", "user_id": "user_1", "role": "user", "content": "Can you analyze my Instagram engagement rate?", "timestamp": "2025-08-14T15:02:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": "twitter", "intent": "question"}}, {"id": "msg_ai_7_1756302699", "user_id": "user_1", "role": "assistant", "content": "To improve follower growth, I suggest: 1) Posting consistently at optimal times, 2) Using trending hashtags relevant to your niche, 3) Engaging with your audience within the first hour of posting.", "timestamp": "2025-08-14T15:03:08.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.87, "processing_time": 6.8, "data_sources": ["youtube_api", "ai_model", "instagram_api"]}}, {"id": "msg_user_1_1756302699", "user_id": "user_1", "role": "user", "content": "Create a content plan for the next 14 days", "timestamp": "2025-08-15T00:13:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": "instagram", "intent": "question"}}, {"id": "msg_ai_1_1756302699", "user_id": "user_1", "role": "assistant", "content": "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.", "timestamp": "2025-08-15T00:13:46.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.95, "processing_time": 6.7, "data_sources": ["youtube_api", "instagram_api"]}}, {"id": "msg_user_2_1756302699", "user_id": "user_1", "role": "user", "content": "Help me plan content for product launch", "timestamp": "2025-08-17T09:32:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": "instagram", "intent": "planning"}}, {"id": "msg_ai_2_1756302699", "user_id": "user_1", "role": "assistant", "content": "I've generated a comprehensive 14-day content plan focusing on your top-performing content types. The plan includes 28 posts across YouTube, Instagram, and Twitter with optimal posting times.", "timestamp": "2025-08-17T09:32:54.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.93, "processing_time": 4.7, "data_sources": ["ai_model", "instagram_api"]}}, {"id": "msg_user_4_1756302699", "user_id": "user_1", "role": "user", "content": "Compare my performance across platforms", "timestamp": "2025-08-24T13:36:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": "twitter", "intent": "optimization"}}, {"id": "msg_ai_4_1756302699", "user_id": "user_1", "role": "assistant", "content": "I recommend focusing on educational content based on your audience preferences. Consider creating 'How-to' tutorials and behind-the-scenes content for maximum engagement.", "timestamp": "2025-08-24T13:36:45.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.94, "processing_time": 7.8, "data_sources": ["instagram_api", "youtube_api"]}}, {"id": "msg_user_10_1756302699", "user_id": "user_1", "role": "user", "content": "Create a content plan for the next 14 days", "timestamp": "2025-08-26T08:37:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": "instagram", "intent": "optimization"}}, {"id": "msg_ai_10_1756302699", "user_id": "user_1", "role": "assistant", "content": "Your Instagram engagement rate is currently at 4.8%, which is above the industry average of 3.5%. Your Reels are performing particularly well with 35% higher engagement than regular posts.", "timestamp": "2025-08-26T08:37:55.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.86, "processing_time": 3.9, "data_sources": ["instagram_api", "analytics_db"]}}, {"id": "msg_user_15_1756302699", "user_id": "user_2", "role": "user", "content": "What hashtags should I use for my fitness content?", "timestamp": "2025-07-28T02:30:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": "twitter", "intent": "question"}}, {"id": "msg_ai_15_1756302699", "user_id": "user_2", "role": "assistant", "content": "I've generated a comprehensive 14-day content plan focusing on your top-performing content types. The plan includes 28 posts across YouTube, Instagram, and Twitter with optimal posting times.", "timestamp": "2025-07-28T02:31:05.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.86, "processing_time": 5.9, "data_sources": ["ai_model", "analytics_db"]}}, {"id": "msg_user_6_1756302699", "user_id": "user_2", "role": "user", "content": "What hashtags should I use for my fitness content?", "timestamp": "2025-07-29T23:21:39.348096+00:00", "session_id": "session_5", "metadata": {"platform_context": "twitter", "intent": "analysis"}}, {"id": "msg_ai_6_1756302699", "user_id": "user_2", "role": "assistant", "content": "Your Instagram engagement rate is currently at 4.8%, which is above the industry average of 3.5%. Your Reels are performing particularly well with 35% higher engagement than regular posts.", "timestamp": "2025-07-29T23:22:03.348096+00:00", "session_id": "session_5", "metadata": {"response_type": "analysis", "confidence_score": 0.95, "processing_time": 3.8, "data_sources": ["ai_model", "youtube_api", "instagram_api"]}}, {"id": "msg_user_13_1756302699", "user_id": "user_2", "role": "user", "content": "Can you analyze my Instagram engagement rate?", "timestamp": "2025-07-31T01:44:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": null, "intent": "analysis"}}, {"id": "msg_ai_13_1756302699", "user_id": "user_2", "role": "assistant", "content": "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach).", "timestamp": "2025-07-31T01:45:03.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.96, "processing_time": 4.5, "data_sources": ["instagram_api"]}}, {"id": "msg_user_3_1756302699", "user_id": "user_2", "role": "user", "content": "How can I improve my follower growth?", "timestamp": "2025-08-01T07:47:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": "twitter", "intent": "optimization"}}, {"id": "msg_ai_3_1756302699", "user_id": "user_2", "role": "assistant", "content": "I've generated a comprehensive 14-day content plan focusing on your top-performing content types. The plan includes 28 posts across YouTube, Instagram, and Twitter with optimal posting times.", "timestamp": "2025-08-01T07:47:49.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.86, "processing_time": 1.3, "data_sources": ["youtube_api"]}}, {"id": "msg_user_10_1756302699", "user_id": "user_2", "role": "user", "content": "How can I improve my follower growth?", "timestamp": "2025-08-01T21:21:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": null, "intent": "planning"}}, {"id": "msg_ai_10_1756302699", "user_id": "user_2", "role": "assistant", "content": "I recommend focusing on educational content based on your audience preferences. Consider creating 'How-to' tutorials and behind-the-scenes content for maximum engagement.", "timestamp": "2025-08-01T21:22:05.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.87, "processing_time": 1.8, "data_sources": ["analytics_db", "instagram_api", "youtube_api"]}}, {"id": "msg_user_14_1756302699", "user_id": "user_2", "role": "user", "content": "How is my YouTube performance this month?", "timestamp": "2025-08-02T17:49:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": null, "intent": "planning"}}, {"id": "msg_ai_14_1756302699", "user_id": "user_2", "role": "assistant", "content": "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.", "timestamp": "2025-08-02T17:49:42.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.89, "processing_time": 1.2, "data_sources": ["ai_model", "analytics_db", "youtube_api"]}}, {"id": "msg_user_7_1756302699", "user_id": "user_2", "role": "user", "content": "What are the best times to post on Instagram?", "timestamp": "2025-08-03T18:45:39.348096+00:00", "session_id": "session_5", "metadata": {"platform_context": "instagram", "intent": "optimization"}}, {"id": "msg_ai_7_1756302699", "user_id": "user_2", "role": "assistant", "content": "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach).", "timestamp": "2025-08-03T18:45:49.348096+00:00", "session_id": "session_5", "metadata": {"response_type": "analysis", "confidence_score": 0.9, "processing_time": 8.0, "data_sources": ["analytics_db"]}}, {"id": "msg_user_5_1756302699", "user_id": "user_2", "role": "user", "content": "What are the best times to post on Instagram?", "timestamp": "2025-08-08T22:53:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": "instagram", "intent": "planning"}}, {"id": "msg_ai_5_1756302699", "user_id": "user_2", "role": "assistant", "content": "Your Instagram engagement rate is currently at 4.8%, which is above the industry average of 3.5%. Your Reels are performing particularly well with 35% higher engagement than regular posts.", "timestamp": "2025-08-08T22:54:03.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.89, "processing_time": 7.4, "data_sources": ["instagram_api"]}}, {"id": "msg_user_12_1756302699", "user_id": "user_2", "role": "user", "content": "How is my YouTube performance this month?", "timestamp": "2025-08-08T23:24:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": "youtube", "intent": "analysis"}}, {"id": "msg_ai_12_1756302699", "user_id": "user_2", "role": "assistant", "content": "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach).", "timestamp": "2025-08-08T23:25:04.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.88, "processing_time": 2.1, "data_sources": ["instagram_api", "youtube_api", "analytics_db"]}}, {"id": "msg_user_8_1756302699", "user_id": "user_2", "role": "user", "content": "Can you analyze my Instagram engagement rate?", "timestamp": "2025-08-10T12:31:39.348096+00:00", "session_id": "session_5", "metadata": {"platform_context": "youtube", "intent": "analysis"}}, {"id": "msg_ai_8_1756302699", "user_id": "user_2", "role": "assistant", "content": "I recommend focusing on educational content based on your audience preferences. Consider creating 'How-to' tutorials and behind-the-scenes content for maximum engagement.", "timestamp": "2025-08-10T12:32:09.348096+00:00", "session_id": "session_5", "metadata": {"response_type": "analysis", "confidence_score": 0.87, "processing_time": 7.2, "data_sources": ["ai_model"]}}, {"id": "msg_user_4_1756302699", "user_id": "user_2", "role": "user", "content": "What content should I post next week?", "timestamp": "2025-08-13T06:11:39.348096+00:00", "session_id": "session_5", "metadata": {"platform_context": "twitter", "intent": "analysis"}}, {"id": "msg_ai_4_1756302699", "user_id": "user_2", "role": "assistant", "content": "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.", "timestamp": "2025-08-13T06:12:07.348096+00:00", "session_id": "session_5", "metadata": {"response_type": "analysis", "confidence_score": 0.97, "processing_time": 1.8, "data_sources": ["ai_model", "analytics_db", "instagram_api"]}}, {"id": "msg_user_9_1756302699", "user_id": "user_2", "role": "user", "content": "Help me plan content for product launch", "timestamp": "2025-08-13T22:18:39.348096+00:00", "session_id": "session_2", "metadata": {"platform_context": null, "intent": "analysis"}}, {"id": "msg_ai_9_1756302699", "user_id": "user_2", "role": "assistant", "content": "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach).", "timestamp": "2025-08-13T22:19:07.348096+00:00", "session_id": "session_2", "metadata": {"response_type": "analysis", "confidence_score": 0.86, "processing_time": 3.4, "data_sources": ["analytics_db"]}}, {"id": "msg_user_1_1756302699", "user_id": "user_2", "role": "user", "content": "What hashtags should I use for my fitness content?", "timestamp": "2025-08-14T22:47:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": "twitter", "intent": "analysis"}}, {"id": "msg_ai_1_1756302699", "user_id": "user_2", "role": "assistant", "content": "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.", "timestamp": "2025-08-14T22:47:44.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.92, "processing_time": 7.2, "data_sources": ["analytics_db"]}}, {"id": "msg_user_11_1756302699", "user_id": "user_2", "role": "user", "content": "What hashtags should I use for my fitness content?", "timestamp": "2025-08-19T15:24:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": null, "intent": "optimization"}}, {"id": "msg_ai_11_1756302699", "user_id": "user_2", "role": "assistant", "content": "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.", "timestamp": "2025-08-19T15:25:06.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.96, "processing_time": 7.6, "data_sources": ["instagram_api", "analytics_db"]}}, {"id": "msg_user_2_1756302699", "user_id": "user_2", "role": "user", "content": "How is my YouTube performance this month?", "timestamp": "2025-08-21T13:33:39.348096+00:00", "session_id": "session_4", "metadata": {"platform_context": "twitter", "intent": "analysis"}}, {"id": "msg_ai_2_1756302699", "user_id": "user_2", "role": "assistant", "content": "Based on your analytics, your YouTube channel has shown strong growth with a 12.5% increase in subscribers this month. Your average view duration has improved to 4.2 minutes, indicating higher engagement.", "timestamp": "2025-08-21T13:33:48.348096+00:00", "session_id": "session_4", "metadata": {"response_type": "analysis", "confidence_score": 0.87, "processing_time": 2.4, "data_sources": ["youtube_api", "analytics_db", "ai_model"]}}, {"id": "msg_user_2_1756302699", "user_id": "user_3", "role": "user", "content": "Analyze my top performing posts", "timestamp": "2025-08-09T07:47:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": "instagram", "intent": "question"}}, {"id": "msg_ai_2_1756302699", "user_id": "user_3", "role": "assistant", "content": "Based on your analytics, your YouTube channel has shown strong growth with a 12.5% increase in subscribers this month. Your average view duration has improved to 4.2 minutes, indicating higher engagement.", "timestamp": "2025-08-09T07:47:51.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.85, "processing_time": 1.2, "data_sources": ["youtube_api", "instagram_api", "ai_model"]}}, {"id": "msg_user_5_1756302699", "user_id": "user_3", "role": "user", "content": "What are the best times to post on Instagram?", "timestamp": "2025-08-12T17:07:39.348096+00:00", "session_id": "session_2", "metadata": {"platform_context": "instagram", "intent": "optimization"}}, {"id": "msg_ai_5_1756302699", "user_id": "user_3", "role": "assistant", "content": "Based on your audience analytics, the best times to post on Instagram are: Weekdays 11 AM - 1 PM and 7 PM - 9 PM, Weekends 10 AM - 12 PM. Your audience is most active on Tuesdays and Thursdays.", "timestamp": "2025-08-12T17:07:57.348096+00:00", "session_id": "session_2", "metadata": {"response_type": "analysis", "confidence_score": 0.87, "processing_time": 2.0, "data_sources": ["analytics_db", "youtube_api", "ai_model"]}}, {"id": "msg_user_6_1756302699", "user_id": "user_3", "role": "user", "content": "Create a content plan for the next 14 days", "timestamp": "2025-08-16T00:43:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": null, "intent": "question"}}, {"id": "msg_ai_6_1756302699", "user_id": "user_3", "role": "assistant", "content": "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach).", "timestamp": "2025-08-16T00:43:53.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.89, "processing_time": 2.7, "data_sources": ["youtube_api", "ai_model"]}}, {"id": "msg_user_4_1756302699", "user_id": "user_3", "role": "user", "content": "Analyze my top performing posts", "timestamp": "2025-08-17T15:21:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": "twitter", "intent": "question"}}, {"id": "msg_ai_4_1756302699", "user_id": "user_3", "role": "assistant", "content": "I've generated a comprehensive 14-day content plan focusing on your top-performing content types. The plan includes 28 posts across YouTube, Instagram, and Twitter with optimal posting times.", "timestamp": "2025-08-17T15:21:54.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.95, "processing_time": 6.3, "data_sources": ["analytics_db", "instagram_api", "youtube_api"]}}, {"id": "msg_user_3_1756302699", "user_id": "user_3", "role": "user", "content": "Create a content plan for the next 14 days", "timestamp": "2025-08-21T02:11:39.348096+00:00", "session_id": "session_1", "metadata": {"platform_context": "instagram", "intent": "planning"}}, {"id": "msg_ai_3_1756302699", "user_id": "user_3", "role": "assistant", "content": "Based on your analytics, your YouTube channel has shown strong growth with a 12.5% increase in subscribers this month. Your average view duration has improved to 4.2 minutes, indicating higher engagement.", "timestamp": "2025-08-21T02:12:06.348096+00:00", "session_id": "session_1", "metadata": {"response_type": "analysis", "confidence_score": 0.9, "processing_time": 5.5, "data_sources": ["youtube_api", "analytics_db"]}}, {"id": "msg_user_7_1756302699", "user_id": "user_3", "role": "user", "content": "Compare my performance across platforms", "timestamp": "2025-08-23T15:07:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": "youtube", "intent": "question"}}, {"id": "msg_ai_7_1756302699", "user_id": "user_3", "role": "assistant", "content": "I recommend focusing on educational content based on your audience preferences. Consider creating 'How-to' tutorials and behind-the-scenes content for maximum engagement.", "timestamp": "2025-08-23T15:07:52.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.92, "processing_time": 7.8, "data_sources": ["analytics_db"]}}, {"id": "msg_user_1_1756302699", "user_id": "user_3", "role": "user", "content": "What are the best times to post on Instagram?", "timestamp": "2025-08-24T10:17:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": null, "intent": "analysis"}}, {"id": "msg_ai_1_1756302699", "user_id": "user_3", "role": "assistant", "content": "Based on your analytics, your YouTube channel has shown strong growth with a 12.5% increase in subscribers this month. Your average view duration has improved to 4.2 minutes, indicating higher engagement.", "timestamp": "2025-08-24T10:17:48.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.92, "processing_time": 4.1, "data_sources": ["analytics_db", "youtube_api", "instagram_api"]}}, {"id": "msg_user_8_1756302699", "user_id": "user_3", "role": "user", "content": "What hashtags should I use for my fitness content?", "timestamp": "2025-08-26T08:37:39.348096+00:00", "session_id": "session_3", "metadata": {"platform_context": "instagram", "intent": "planning"}}, {"id": "msg_ai_8_1756302699", "user_id": "user_3", "role": "assistant", "content": "Your top performing posts share these characteristics: Educational content (45% higher engagement), Posts with carousels (30% more saves), Content posted on Tuesday-Thursday (25% more reach).", "timestamp": "2025-08-26T08:37:51.348096+00:00", "session_id": "session_3", "metadata": {"response_type": "analysis", "confidence_score": 0.96, "processing_time": 2.9, "data_sources": ["ai_model"]}}], "analytics_data": {"instagram_user_1_3608": {"account_id": "instagram_user_1_3608", "platform": "instagram", "daily_metrics": [{"date": "2025-07-29", "followers": 23338, "engagement_rate": 6.86, "posts_published": 1, "likes": 816, "comments": 143, "shares": 31, "reach": 6411, "impressions": 6977, "story_views": 2243, "profile_visits": 525, "saves": 229}, {"date": "2025-07-30", "followers": 23517, "engagement_rate": 3.12, "posts_published": 0, "likes": 138, "comments": 83, "shares": 90, "reach": 2688, "impressions": 14159, "story_views": 1102, "profile_visits": 670, "saves": 201}, {"date": "2025-07-31", "followers": 23610, "engagement_rate": 4.3, "posts_published": 0, "likes": 1828, "comments": 139, "shares": 47, "reach": 4990, "impressions": 13271, "story_views": 1493, "profile_visits": 751, "saves": 295}, {"date": "2025-08-01", "followers": 23743, "engagement_rate": 5.16, "posts_published": 1, "likes": 1883, "comments": 115, "shares": 100, "reach": 8028, "impressions": 3856, "story_views": 901, "profile_visits": 674, "saves": 56}, {"date": "2025-08-02", "followers": 24026, "engagement_rate": 2.82, "posts_published": 0, "likes": 457, "comments": 103, "shares": 15, "reach": 8030, "impressions": 7175, "story_views": 519, "profile_visits": 712, "saves": 272}, {"date": "2025-08-03", "followers": 24093, "engagement_rate": 5.17, "posts_published": 1, "likes": 259, "comments": 82, "shares": 14, "reach": 6370, "impressions": 3462, "story_views": 2660, "profile_visits": 159, "saves": 249}, {"date": "2025-08-04", "followers": 24064, "engagement_rate": 6.47, "posts_published": 1, "likes": 1094, "comments": 182, "shares": 85, "reach": 2094, "impressions": 3417, "story_views": 2601, "profile_visits": 462, "saves": 92}, {"date": "2025-08-05", "followers": 24605, "engagement_rate": 7.85, "posts_published": 2, "likes": 1315, "comments": 67, "shares": 14, "reach": 1418, "impressions": 10220, "story_views": 922, "profile_visits": 267, "saves": 229}, {"date": "2025-08-06", "followers": 23954, "engagement_rate": 7.36, "posts_published": 1, "likes": 531, "comments": 116, "shares": 28, "reach": 8624, "impressions": 6114, "story_views": 2918, "profile_visits": 456, "saves": 231}, {"date": "2025-08-07", "followers": 24652, "engagement_rate": 3.67, "posts_published": 1, "likes": 1096, "comments": 117, "shares": 43, "reach": 9565, "impressions": 4387, "story_views": 1203, "profile_visits": 578, "saves": 125}, {"date": "2025-08-08", "followers": 25138, "engagement_rate": 2.56, "posts_published": 1, "likes": 1706, "comments": 125, "shares": 50, "reach": 9967, "impressions": 8913, "story_views": 1796, "profile_visits": 212, "saves": 118}, {"date": "2025-08-09", "followers": 23129, "engagement_rate": 4.98, "posts_published": 2, "likes": 1463, "comments": 180, "shares": 35, "reach": 6708, "impressions": 2789, "story_views": 1042, "profile_visits": 182, "saves": 201}, {"date": "2025-08-10", "followers": 24718, "engagement_rate": 6.19, "posts_published": 2, "likes": 105, "comments": 83, "shares": 96, "reach": 7289, "impressions": 8553, "story_views": 2771, "profile_visits": 881, "saves": 60}, {"date": "2025-08-11", "followers": 25366, "engagement_rate": 5.35, "posts_published": 3, "likes": 1189, "comments": 133, "shares": 90, "reach": 6257, "impressions": 2376, "story_views": 2509, "profile_visits": 679, "saves": 57}, {"date": "2025-08-12", "followers": 25760, "engagement_rate": 7.12, "posts_published": 2, "likes": 1483, "comments": 159, "shares": 69, "reach": 5604, "impressions": 5178, "story_views": 2627, "profile_visits": 365, "saves": 52}, {"date": "2025-08-13", "followers": 25153, "engagement_rate": 7.12, "posts_published": 3, "likes": 260, "comments": 193, "shares": 71, "reach": 4648, "impressions": 14008, "story_views": 2807, "profile_visits": 837, "saves": 135}, {"date": "2025-08-14", "followers": 23850, "engagement_rate": 7.97, "posts_published": 0, "likes": 1094, "comments": 114, "shares": 61, "reach": 5880, "impressions": 12944, "story_views": 1583, "profile_visits": 230, "saves": 216}, {"date": "2025-08-15", "followers": 25327, "engagement_rate": 5.52, "posts_published": 0, "likes": 848, "comments": 138, "shares": 78, "reach": 7134, "impressions": 11586, "story_views": 1173, "profile_visits": 134, "saves": 41}, {"date": "2025-08-16", "followers": 26506, "engagement_rate": 5.46, "posts_published": 2, "likes": 334, "comments": 135, "shares": 25, "reach": 1675, "impressions": 11286, "story_views": 513, "profile_visits": 534, "saves": 230}, {"date": "2025-08-17", "followers": 27043, "engagement_rate": 4.97, "posts_published": 3, "likes": 113, "comments": 127, "shares": 9, "reach": 6561, "impressions": 12529, "story_views": 2864, "profile_visits": 423, "saves": 122}, {"date": "2025-08-18", "followers": 23658, "engagement_rate": 3.46, "posts_published": 2, "likes": 776, "comments": 165, "shares": 75, "reach": 1960, "impressions": 10707, "story_views": 1238, "profile_visits": 780, "saves": 120}, {"date": "2025-08-19", "followers": 23380, "engagement_rate": 6.82, "posts_published": 2, "likes": 1068, "comments": 76, "shares": 49, "reach": 4096, "impressions": 12356, "story_views": 2697, "profile_visits": 300, "saves": 94}, {"date": "2025-08-20", "followers": 24900, "engagement_rate": 6.06, "posts_published": 2, "likes": 1915, "comments": 62, "shares": 52, "reach": 5545, "impressions": 11509, "story_views": 2822, "profile_visits": 791, "saves": 283}, {"date": "2025-08-21", "followers": 22878, "engagement_rate": 8.0, "posts_published": 1, "likes": 1963, "comments": 76, "shares": 61, "reach": 8664, "impressions": 9642, "story_views": 1977, "profile_visits": 591, "saves": 255}, {"date": "2025-08-22", "followers": 26386, "engagement_rate": 6.76, "posts_published": 0, "likes": 1113, "comments": 144, "shares": 69, "reach": 8772, "impressions": 5794, "story_views": 2493, "profile_visits": 772, "saves": 79}, {"date": "2025-08-23", "followers": 22188, "engagement_rate": 3.77, "posts_published": 3, "likes": 1983, "comments": 13, "shares": 54, "reach": 5487, "impressions": 7510, "story_views": 2133, "profile_visits": 720, "saves": 176}, {"date": "2025-08-24", "followers": 27238, "engagement_rate": 7.94, "posts_published": 1, "likes": 927, "comments": 166, "shares": 57, "reach": 4561, "impressions": 10716, "story_views": 614, "profile_visits": 904, "saves": 105}, {"date": "2025-08-25", "followers": 25282, "engagement_rate": 4.46, "posts_published": 1, "likes": 446, "comments": 166, "shares": 76, "reach": 7531, "impressions": 5786, "story_views": 2111, "profile_visits": 927, "saves": 264}, {"date": "2025-08-26", "followers": 23114, "engagement_rate": 7.34, "posts_published": 3, "likes": 423, "comments": 100, "shares": 73, "reach": 4566, "impressions": 14212, "story_views": 1719, "profile_visits": 331, "saves": 106}, {"date": "2025-08-27", "followers": 25600, "engagement_rate": 5.9, "posts_published": 0, "likes": 518, "comments": 162, "shares": 85, "reach": 2894, "impressions": 9802, "story_views": 971, "profile_visits": 942, "saves": 40}], "summary": {"total_followers_gained": -1978, "avg_engagement_rate": 5.68, "total_posts": 41, "best_performing_day": "2025-08-21"}}, "youtube_user_1_2386": {"account_id": "youtube_user_1_2386", "platform": "youtube", "daily_metrics": [{"date": "2025-07-29", "followers": 23464, "engagement_rate": 6.77, "posts_published": 1, "likes": 1977, "comments": 52, "shares": 90, "reach": 3653, "impressions": 2619, "views": 6125, "watch_time": 4023, "subscribers_gained": 8}, {"date": "2025-07-30", "followers": 23499, "engagement_rate": 5.88, "posts_published": 1, "likes": 648, "comments": 129, "shares": 60, "reach": 6403, "impressions": 6794, "views": 13740, "watch_time": 4660, "subscribers_gained": 6}, {"date": "2025-07-31", "followers": 23698, "engagement_rate": 5.61, "posts_published": 2, "likes": 1403, "comments": 107, "shares": 82, "reach": 4467, "impressions": 2803, "views": 5179, "watch_time": 2921, "subscribers_gained": 21}, {"date": "2025-08-01", "followers": 23740, "engagement_rate": 7.16, "posts_published": 3, "likes": 509, "comments": 120, "shares": 65, "reach": 4765, "impressions": 2816, "views": 19332, "watch_time": 3341, "subscribers_gained": 38}, {"date": "2025-08-02", "followers": 23500, "engagement_rate": 4.71, "posts_published": 1, "likes": 851, "comments": 82, "shares": 89, "reach": 3867, "impressions": 13389, "views": 10277, "watch_time": 4547, "subscribers_gained": 41}, {"date": "2025-08-03", "followers": 24199, "engagement_rate": 2.74, "posts_published": 2, "likes": 614, "comments": 11, "shares": 19, "reach": 3806, "impressions": 12929, "views": 19433, "watch_time": 4167, "subscribers_gained": 28}, {"date": "2025-08-04", "followers": 24502, "engagement_rate": 3.73, "posts_published": 1, "likes": 784, "comments": 88, "shares": 7, "reach": 9425, "impressions": 5144, "views": 19996, "watch_time": 1659, "subscribers_gained": 11}, {"date": "2025-08-05", "followers": 24360, "engagement_rate": 3.15, "posts_published": 3, "likes": 1833, "comments": 126, "shares": 31, "reach": 6302, "impressions": 12883, "views": 8514, "watch_time": 3827, "subscribers_gained": 17}, {"date": "2025-08-06", "followers": 23680, "engagement_rate": 6.61, "posts_published": 0, "likes": 1153, "comments": 151, "shares": 79, "reach": 1431, "impressions": 3199, "views": 13809, "watch_time": 1496, "subscribers_gained": 5}, {"date": "2025-08-07", "followers": 24391, "engagement_rate": 5.65, "posts_published": 2, "likes": 1830, "comments": 164, "shares": 71, "reach": 3399, "impressions": 8483, "views": 5443, "watch_time": 4686, "subscribers_gained": 11}, {"date": "2025-08-08", "followers": 24194, "engagement_rate": 7.07, "posts_published": 1, "likes": 1821, "comments": 22, "shares": 81, "reach": 8618, "impressions": 8041, "views": 6437, "watch_time": 2977, "subscribers_gained": 50}, {"date": "2025-08-09", "followers": 24212, "engagement_rate": 4.6, "posts_published": 0, "likes": 672, "comments": 78, "shares": 6, "reach": 6001, "impressions": 3731, "views": 12712, "watch_time": 3430, "subscribers_gained": 8}, {"date": "2025-08-10", "followers": 25528, "engagement_rate": 6.74, "posts_published": 3, "likes": 419, "comments": 174, "shares": 25, "reach": 3016, "impressions": 14658, "views": 11793, "watch_time": 2596, "subscribers_gained": 15}, {"date": "2025-08-11", "followers": 23243, "engagement_rate": 7.82, "posts_published": 1, "likes": 635, "comments": 134, "shares": 84, "reach": 8913, "impressions": 2246, "views": 13710, "watch_time": 1634, "subscribers_gained": 42}, {"date": "2025-08-12", "followers": 23310, "engagement_rate": 6.64, "posts_published": 1, "likes": 1881, "comments": 78, "shares": 94, "reach": 9708, "impressions": 8723, "views": 18621, "watch_time": 3014, "subscribers_gained": 24}, {"date": "2025-08-13", "followers": 24619, "engagement_rate": 5.85, "posts_published": 3, "likes": 547, "comments": 80, "shares": 26, "reach": 3775, "impressions": 10467, "views": 11734, "watch_time": 3264, "subscribers_gained": 25}, {"date": "2025-08-14", "followers": 24584, "engagement_rate": 4.24, "posts_published": 3, "likes": 1909, "comments": 105, "shares": 22, "reach": 4342, "impressions": 5523, "views": 1807, "watch_time": 1920, "subscribers_gained": 11}, {"date": "2025-08-15", "followers": 26694, "engagement_rate": 5.29, "posts_published": 1, "likes": 131, "comments": 18, "shares": 62, "reach": 5240, "impressions": 11526, "views": 2097, "watch_time": 1716, "subscribers_gained": 9}, {"date": "2025-08-16", "followers": 22708, "engagement_rate": 4.83, "posts_published": 2, "likes": 668, "comments": 44, "shares": 82, "reach": 1799, "impressions": 2096, "views": 14530, "watch_time": 1130, "subscribers_gained": -3}, {"date": "2025-08-17", "followers": 24889, "engagement_rate": 3.2, "posts_published": 2, "likes": 1571, "comments": 93, "shares": 76, "reach": 4246, "impressions": 3689, "views": 4577, "watch_time": 3829, "subscribers_gained": 46}, {"date": "2025-08-18", "followers": 25084, "engagement_rate": 7.79, "posts_published": 1, "likes": 928, "comments": 54, "shares": 9, "reach": 1327, "impressions": 8800, "views": 14556, "watch_time": 3307, "subscribers_gained": 6}, {"date": "2025-08-19", "followers": 26446, "engagement_rate": 3.58, "posts_published": 1, "likes": 1056, "comments": 39, "shares": 77, "reach": 9089, "impressions": 8091, "views": 8935, "watch_time": 4407, "subscribers_gained": 27}, {"date": "2025-08-20", "followers": 24938, "engagement_rate": 6.91, "posts_published": 3, "likes": 1135, "comments": 139, "shares": 55, "reach": 2227, "impressions": 8566, "views": 3260, "watch_time": 713, "subscribers_gained": 12}, {"date": "2025-08-21", "followers": 24476, "engagement_rate": 3.08, "posts_published": 3, "likes": 1267, "comments": 179, "shares": 97, "reach": 8688, "impressions": 14772, "views": 3869, "watch_time": 2075, "subscribers_gained": -3}, {"date": "2025-08-22", "followers": 25432, "engagement_rate": 4.4, "posts_published": 3, "likes": 830, "comments": 163, "shares": 32, "reach": 3924, "impressions": 10340, "views": 12773, "watch_time": 4806, "subscribers_gained": 36}, {"date": "2025-08-23", "followers": 26589, "engagement_rate": 4.83, "posts_published": 3, "likes": 1185, "comments": 112, "shares": 100, "reach": 6950, "impressions": 14547, "views": 9858, "watch_time": 1765, "subscribers_gained": 5}, {"date": "2025-08-24", "followers": 24738, "engagement_rate": 7.14, "posts_published": 3, "likes": 707, "comments": 25, "shares": 74, "reach": 7951, "impressions": 11654, "views": 15770, "watch_time": 4615, "subscribers_gained": 27}, {"date": "2025-08-25", "followers": 27892, "engagement_rate": 7.05, "posts_published": 1, "likes": 236, "comments": 172, "shares": 44, "reach": 4851, "impressions": 12963, "views": 16454, "watch_time": 2554, "subscribers_gained": 19}, {"date": "2025-08-26", "followers": 25340, "engagement_rate": 3.48, "posts_published": 3, "likes": 177, "comments": 200, "shares": 68, "reach": 8115, "impressions": 3739, "views": 6048, "watch_time": 2325, "subscribers_gained": 14}, {"date": "2025-08-27", "followers": 25001, "engagement_rate": 7.13, "posts_published": 2, "likes": 1487, "comments": 192, "shares": 55, "reach": 1786, "impressions": 6781, "views": 17779, "watch_time": 1052, "subscribers_gained": 45}], "summary": {"total_followers_gained": 4125, "avg_engagement_rate": 5.46, "total_posts": 56, "best_performing_day": "2025-08-11"}}, "twitter_user_1_8235": {"account_id": "twitter_user_1_8235", "platform": "twitter", "daily_metrics": [{"date": "2025-07-29", "followers": 10295, "engagement_rate": 7.16, "posts_published": 0, "likes": 773, "comments": 185, "shares": 80, "reach": 1530, "impressions": 12716}, {"date": "2025-07-30", "followers": 10458, "engagement_rate": 6.61, "posts_published": 2, "likes": 1024, "comments": 13, "shares": 51, "reach": 8661, "impressions": 4955}, {"date": "2025-07-31", "followers": 10291, "engagement_rate": 3.34, "posts_published": 1, "likes": 456, "comments": 32, "shares": 42, "reach": 7037, "impressions": 11887}, {"date": "2025-08-01", "followers": 10415, "engagement_rate": 7.47, "posts_published": 2, "likes": 1215, "comments": 157, "shares": 37, "reach": 3057, "impressions": 9012}, {"date": "2025-08-02", "followers": 10195, "engagement_rate": 6.01, "posts_published": 1, "likes": 535, "comments": 48, "shares": 80, "reach": 1557, "impressions": 6006}, {"date": "2025-08-03", "followers": 10480, "engagement_rate": 7.59, "posts_published": 3, "likes": 1470, "comments": 34, "shares": 18, "reach": 9870, "impressions": 5808}, {"date": "2025-08-04", "followers": 10247, "engagement_rate": 6.39, "posts_published": 1, "likes": 1870, "comments": 14, "shares": 22, "reach": 4804, "impressions": 3634}, {"date": "2025-08-05", "followers": 11541, "engagement_rate": 3.4, "posts_published": 3, "likes": 1002, "comments": 64, "shares": 66, "reach": 1852, "impressions": 7865}, {"date": "2025-08-06", "followers": 11383, "engagement_rate": 3.54, "posts_published": 1, "likes": 1007, "comments": 159, "shares": 82, "reach": 8371, "impressions": 6610}, {"date": "2025-08-07", "followers": 10061, "engagement_rate": 5.97, "posts_published": 1, "likes": 196, "comments": 37, "shares": 11, "reach": 3281, "impressions": 8015}, {"date": "2025-08-08", "followers": 12015, "engagement_rate": 5.68, "posts_published": 2, "likes": 1278, "comments": 54, "shares": 12, "reach": 5971, "impressions": 10359}, {"date": "2025-08-09", "followers": 10636, "engagement_rate": 5.27, "posts_published": 1, "likes": 1196, "comments": 138, "shares": 65, "reach": 6421, "impressions": 7359}, {"date": "2025-08-10", "followers": 12323, "engagement_rate": 4.35, "posts_published": 3, "likes": 649, "comments": 158, "shares": 93, "reach": 9188, "impressions": 6573}, {"date": "2025-08-11", "followers": 11868, "engagement_rate": 7.23, "posts_published": 0, "likes": 1758, "comments": 10, "shares": 45, "reach": 3443, "impressions": 2150}, {"date": "2025-08-12", "followers": 11975, "engagement_rate": 6.45, "posts_published": 0, "likes": 129, "comments": 41, "shares": 92, "reach": 5388, "impressions": 5000}, {"date": "2025-08-13", "followers": 11690, "engagement_rate": 5.19, "posts_published": 3, "likes": 1596, "comments": 119, "shares": 100, "reach": 7768, "impressions": 7744}, {"date": "2025-08-14", "followers": 13399, "engagement_rate": 3.7, "posts_published": 3, "likes": 698, "comments": 125, "shares": 65, "reach": 2022, "impressions": 11216}, {"date": "2025-08-15", "followers": 12488, "engagement_rate": 6.98, "posts_published": 0, "likes": 1966, "comments": 63, "shares": 76, "reach": 5312, "impressions": 2700}, {"date": "2025-08-16", "followers": 12329, "engagement_rate": 4.22, "posts_published": 2, "likes": 491, "comments": 179, "shares": 80, "reach": 2547, "impressions": 3361}, {"date": "2025-08-17", "followers": 12005, "engagement_rate": 5.19, "posts_published": 0, "likes": 1222, "comments": 28, "shares": 57, "reach": 8492, "impressions": 5369}, {"date": "2025-08-18", "followers": 10815, "engagement_rate": 7.3, "posts_published": 3, "likes": 1437, "comments": 169, "shares": 77, "reach": 1218, "impressions": 13162}, {"date": "2025-08-19", "followers": 11513, "engagement_rate": 2.15, "posts_published": 2, "likes": 1863, "comments": 152, "shares": 15, "reach": 5284, "impressions": 8395}, {"date": "2025-08-20", "followers": 10031, "engagement_rate": 2.69, "posts_published": 2, "likes": 541, "comments": 124, "shares": 77, "reach": 3427, "impressions": 10817}, {"date": "2025-08-21", "followers": 11008, "engagement_rate": 2.22, "posts_published": 1, "likes": 1119, "comments": 193, "shares": 9, "reach": 6004, "impressions": 13496}, {"date": "2025-08-22", "followers": 12671, "engagement_rate": 6.02, "posts_published": 3, "likes": 1183, "comments": 12, "shares": 86, "reach": 4588, "impressions": 8435}, {"date": "2025-08-23", "followers": 11145, "engagement_rate": 4.27, "posts_published": 3, "likes": 1837, "comments": 186, "shares": 57, "reach": 5099, "impressions": 10692}, {"date": "2025-08-24", "followers": 12505, "engagement_rate": 6.65, "posts_published": 1, "likes": 713, "comments": 124, "shares": 88, "reach": 1029, "impressions": 9938}, {"date": "2025-08-25", "followers": 10052, "engagement_rate": 2.41, "posts_published": 3, "likes": 1261, "comments": 191, "shares": 70, "reach": 2738, "impressions": 11882}, {"date": "2025-08-26", "followers": 9595, "engagement_rate": 6.22, "posts_published": 0, "likes": 1994, "comments": 145, "shares": 24, "reach": 6692, "impressions": 13771}, {"date": "2025-08-27", "followers": 9570, "engagement_rate": 4.68, "posts_published": 1, "likes": 610, "comments": 180, "shares": 67, "reach": 2264, "impressions": 3806}], "summary": {"total_followers_gained": -6034, "avg_engagement_rate": 5.21, "total_posts": 48, "best_performing_day": "2025-08-03"}}, "instagram_user_2_4631": {"account_id": "instagram_user_2_4631", "platform": "instagram", "daily_metrics": [{"date": "2025-07-29", "followers": 23566, "engagement_rate": 2.15, "posts_published": 1, "likes": 1372, "comments": 142, "shares": 85, "reach": 3686, "impressions": 5996, "story_views": 2658, "profile_visits": 232, "saves": 23}, {"date": "2025-07-30", "followers": 23541, "engagement_rate": 7.0, "posts_published": 0, "likes": 1421, "comments": 102, "shares": 40, "reach": 8981, "impressions": 8621, "story_views": 2096, "profile_visits": 153, "saves": 30}, {"date": "2025-07-31", "followers": 23578, "engagement_rate": 3.63, "posts_published": 3, "likes": 1670, "comments": 93, "shares": 68, "reach": 9876, "impressions": 9147, "story_views": 1585, "profile_visits": 921, "saves": 40}, {"date": "2025-08-01", "followers": 23785, "engagement_rate": 2.71, "posts_published": 2, "likes": 734, "comments": 170, "shares": 75, "reach": 9977, "impressions": 4036, "story_views": 507, "profile_visits": 290, "saves": 233}, {"date": "2025-08-02", "followers": 24010, "engagement_rate": 7.18, "posts_published": 0, "likes": 514, "comments": 156, "shares": 13, "reach": 3112, "impressions": 2091, "story_views": 513, "profile_visits": 345, "saves": 240}, {"date": "2025-08-03", "followers": 23791, "engagement_rate": 2.35, "posts_published": 3, "likes": 645, "comments": 28, "shares": 56, "reach": 5631, "impressions": 6362, "story_views": 2987, "profile_visits": 486, "saves": 116}, {"date": "2025-08-04", "followers": 23968, "engagement_rate": 3.34, "posts_published": 2, "likes": 872, "comments": 22, "shares": 91, "reach": 8877, "impressions": 2289, "story_views": 784, "profile_visits": 946, "saves": 47}, {"date": "2025-08-05", "followers": 24378, "engagement_rate": 4.24, "posts_published": 0, "likes": 1045, "comments": 163, "shares": 67, "reach": 3613, "impressions": 12124, "story_views": 917, "profile_visits": 452, "saves": 277}, {"date": "2025-08-06", "followers": 24910, "engagement_rate": 7.87, "posts_published": 3, "likes": 1910, "comments": 79, "shares": 47, "reach": 7611, "impressions": 14519, "story_views": 1282, "profile_visits": 174, "saves": 124}, {"date": "2025-08-07", "followers": 24745, "engagement_rate": 3.61, "posts_published": 2, "likes": 782, "comments": 84, "shares": 57, "reach": 8103, "impressions": 10122, "story_views": 2391, "profile_visits": 940, "saves": 113}, {"date": "2025-08-08", "followers": 24376, "engagement_rate": 6.55, "posts_published": 2, "likes": 1577, "comments": 71, "shares": 64, "reach": 3862, "impressions": 13707, "story_views": 1221, "profile_visits": 860, "saves": 214}, {"date": "2025-08-09", "followers": 24457, "engagement_rate": 6.88, "posts_published": 0, "likes": 976, "comments": 45, "shares": 72, "reach": 7146, "impressions": 8170, "story_views": 1893, "profile_visits": 270, "saves": 286}, {"date": "2025-08-10", "followers": 25894, "engagement_rate": 2.85, "posts_published": 1, "likes": 270, "comments": 25, "shares": 27, "reach": 7813, "impressions": 7502, "story_views": 2117, "profile_visits": 566, "saves": 24}, {"date": "2025-08-11", "followers": 26088, "engagement_rate": 5.93, "posts_published": 3, "likes": 883, "comments": 106, "shares": 91, "reach": 8511, "impressions": 14003, "story_views": 2033, "profile_visits": 574, "saves": 201}, {"date": "2025-08-12", "followers": 25568, "engagement_rate": 6.66, "posts_published": 2, "likes": 536, "comments": 12, "shares": 79, "reach": 6836, "impressions": 8058, "story_views": 2798, "profile_visits": 293, "saves": 155}, {"date": "2025-08-13", "followers": 23851, "engagement_rate": 3.39, "posts_published": 1, "likes": 1913, "comments": 32, "shares": 46, "reach": 3692, "impressions": 3683, "story_views": 2466, "profile_visits": 327, "saves": 64}, {"date": "2025-08-14", "followers": 24702, "engagement_rate": 3.42, "posts_published": 3, "likes": 686, "comments": 125, "shares": 24, "reach": 8217, "impressions": 12815, "story_views": 1803, "profile_visits": 561, "saves": 298}, {"date": "2025-08-15", "followers": 25572, "engagement_rate": 2.53, "posts_published": 0, "likes": 397, "comments": 81, "shares": 34, "reach": 5836, "impressions": 12631, "story_views": 2824, "profile_visits": 517, "saves": 70}, {"date": "2025-08-16", "followers": 24034, "engagement_rate": 4.4, "posts_published": 1, "likes": 1126, "comments": 189, "shares": 64, "reach": 8500, "impressions": 10868, "story_views": 710, "profile_visits": 370, "saves": 237}, {"date": "2025-08-17", "followers": 27347, "engagement_rate": 2.58, "posts_published": 3, "likes": 761, "comments": 187, "shares": 71, "reach": 5334, "impressions": 3521, "story_views": 1491, "profile_visits": 314, "saves": 150}, {"date": "2025-08-18", "followers": 23026, "engagement_rate": 5.68, "posts_published": 1, "likes": 1859, "comments": 152, "shares": 32, "reach": 5974, "impressions": 11629, "story_views": 1238, "profile_visits": 807, "saves": 168}, {"date": "2025-08-19", "followers": 27619, "engagement_rate": 6.59, "posts_published": 1, "likes": 1800, "comments": 75, "shares": 22, "reach": 3379, "impressions": 14285, "story_views": 2587, "profile_visits": 733, "saves": 45}, {"date": "2025-08-20", "followers": 23434, "engagement_rate": 2.83, "posts_published": 3, "likes": 786, "comments": 14, "shares": 30, "reach": 4174, "impressions": 3992, "story_views": 2287, "profile_visits": 310, "saves": 233}, {"date": "2025-08-21", "followers": 26211, "engagement_rate": 3.08, "posts_published": 2, "likes": 1463, "comments": 136, "shares": 22, "reach": 2042, "impressions": 2247, "story_views": 2604, "profile_visits": 813, "saves": 199}, {"date": "2025-08-22", "followers": 22966, "engagement_rate": 6.71, "posts_published": 1, "likes": 159, "comments": 85, "shares": 99, "reach": 3778, "impressions": 5557, "story_views": 1029, "profile_visits": 448, "saves": 86}, {"date": "2025-08-23", "followers": 27066, "engagement_rate": 3.74, "posts_published": 3, "likes": 1503, "comments": 139, "shares": 55, "reach": 7931, "impressions": 12504, "story_views": 698, "profile_visits": 842, "saves": 128}, {"date": "2025-08-24", "followers": 25308, "engagement_rate": 7.93, "posts_published": 3, "likes": 274, "comments": 41, "shares": 80, "reach": 4010, "impressions": 6963, "story_views": 503, "profile_visits": 330, "saves": 40}, {"date": "2025-08-25", "followers": 26158, "engagement_rate": 6.16, "posts_published": 3, "likes": 1003, "comments": 90, "shares": 11, "reach": 5043, "impressions": 8387, "story_views": 727, "profile_visits": 813, "saves": 252}, {"date": "2025-08-26", "followers": 22950, "engagement_rate": 6.97, "posts_published": 2, "likes": 660, "comments": 116, "shares": 99, "reach": 4474, "impressions": 2477, "story_views": 703, "profile_visits": 598, "saves": 44}, {"date": "2025-08-27", "followers": 28409, "engagement_rate": 2.84, "posts_published": 3, "likes": 1235, "comments": 191, "shares": 20, "reach": 5331, "impressions": 14984, "story_views": 1553, "profile_visits": 799, "saves": 86}], "summary": {"total_followers_gained": 3334, "avg_engagement_rate": 4.73, "total_posts": 54, "best_performing_day": "2025-08-24"}}, "youtube_user_2_7550": {"account_id": "youtube_user_2_7550", "platform": "youtube", "daily_metrics": [{"date": "2025-07-29", "followers": 31470, "engagement_rate": 3.37, "posts_published": 1, "likes": 1072, "comments": 192, "shares": 43, "reach": 2391, "impressions": 9029, "views": 15283, "watch_time": 4497, "subscribers_gained": 21}, {"date": "2025-07-30", "followers": 31510, "engagement_rate": 4.57, "posts_published": 2, "likes": 174, "comments": 193, "shares": 75, "reach": 8471, "impressions": 4836, "views": 17316, "watch_time": 3109, "subscribers_gained": 46}, {"date": "2025-07-31", "followers": 31680, "engagement_rate": 2.26, "posts_published": 1, "likes": 544, "comments": 61, "shares": 41, "reach": 3460, "impressions": 7624, "views": 7859, "watch_time": 4763, "subscribers_gained": 27}, {"date": "2025-08-01", "followers": 31554, "engagement_rate": 6.72, "posts_published": 2, "likes": 1194, "comments": 161, "shares": 85, "reach": 6229, "impressions": 7676, "views": 10210, "watch_time": 2423, "subscribers_gained": -2}, {"date": "2025-08-02", "followers": 32026, "engagement_rate": 6.95, "posts_published": 0, "likes": 1254, "comments": 163, "shares": 38, "reach": 3411, "impressions": 4414, "views": 4292, "watch_time": 862, "subscribers_gained": 2}, {"date": "2025-08-03", "followers": 32195, "engagement_rate": 7.23, "posts_published": 2, "likes": 170, "comments": 45, "shares": 12, "reach": 2467, "impressions": 8469, "views": 11949, "watch_time": 611, "subscribers_gained": 27}, {"date": "2025-08-04", "followers": 32382, "engagement_rate": 2.14, "posts_published": 0, "likes": 1996, "comments": 67, "shares": 63, "reach": 7367, "impressions": 6209, "views": 5395, "watch_time": 583, "subscribers_gained": 27}, {"date": "2025-08-05", "followers": 31449, "engagement_rate": 7.16, "posts_published": 0, "likes": 806, "comments": 92, "shares": 82, "reach": 5212, "impressions": 12815, "views": 17028, "watch_time": 4551, "subscribers_gained": -2}, {"date": "2025-08-06", "followers": 32158, "engagement_rate": 2.85, "posts_published": 1, "likes": 1262, "comments": 57, "shares": 54, "reach": 6653, "impressions": 13305, "views": 9616, "watch_time": 1455, "subscribers_gained": 49}, {"date": "2025-08-07", "followers": 31848, "engagement_rate": 3.35, "posts_published": 1, "likes": 1362, "comments": 119, "shares": 94, "reach": 7520, "impressions": 7910, "views": 4790, "watch_time": 4283, "subscribers_gained": 30}, {"date": "2025-08-08", "followers": 31790, "engagement_rate": 5.28, "posts_published": 0, "likes": 751, "comments": 120, "shares": 98, "reach": 9027, "impressions": 7236, "views": 4700, "watch_time": 2173, "subscribers_gained": 24}, {"date": "2025-08-09", "followers": 32581, "engagement_rate": 3.25, "posts_published": 1, "likes": 1821, "comments": 161, "shares": 30, "reach": 8739, "impressions": 5116, "views": 14377, "watch_time": 3039, "subscribers_gained": -5}, {"date": "2025-08-10", "followers": 32634, "engagement_rate": 2.42, "posts_published": 1, "likes": 1057, "comments": 72, "shares": 45, "reach": 5318, "impressions": 11496, "views": 11895, "watch_time": 1733, "subscribers_gained": 46}, {"date": "2025-08-11", "followers": 33862, "engagement_rate": 5.25, "posts_published": 2, "likes": 550, "comments": 18, "shares": 39, "reach": 3477, "impressions": 12749, "views": 7690, "watch_time": 614, "subscribers_gained": 17}, {"date": "2025-08-12", "followers": 32800, "engagement_rate": 3.54, "posts_published": 3, "likes": 1902, "comments": 126, "shares": 39, "reach": 1054, "impressions": 9921, "views": 9746, "watch_time": 3891, "subscribers_gained": 50}, {"date": "2025-08-13", "followers": 34290, "engagement_rate": 6.21, "posts_published": 0, "likes": 1628, "comments": 13, "shares": 30, "reach": 5000, "impressions": 12432, "views": 14469, "watch_time": 2298, "subscribers_gained": -5}, {"date": "2025-08-14", "followers": 30750, "engagement_rate": 7.08, "posts_published": 3, "likes": 1715, "comments": 66, "shares": 82, "reach": 2081, "impressions": 8601, "views": 11060, "watch_time": 2860, "subscribers_gained": 14}, {"date": "2025-08-15", "followers": 33238, "engagement_rate": 2.28, "posts_published": 3, "likes": 592, "comments": 75, "shares": 41, "reach": 8307, "impressions": 11623, "views": 13253, "watch_time": 4991, "subscribers_gained": 7}, {"date": "2025-08-16", "followers": 30606, "engagement_rate": 7.38, "posts_published": 3, "likes": 1555, "comments": 73, "shares": 96, "reach": 9156, "impressions": 6427, "views": 15523, "watch_time": 1806, "subscribers_gained": 17}, {"date": "2025-08-17", "followers": 34510, "engagement_rate": 4.49, "posts_published": 2, "likes": 1237, "comments": 51, "shares": 79, "reach": 1882, "impressions": 3148, "views": 18586, "watch_time": 1134, "subscribers_gained": 22}, {"date": "2025-08-18", "followers": 33350, "engagement_rate": 4.2, "posts_published": 3, "likes": 106, "comments": 185, "shares": 49, "reach": 8910, "impressions": 4099, "views": 12508, "watch_time": 3402, "subscribers_gained": 13}, {"date": "2025-08-19", "followers": 32100, "engagement_rate": 7.69, "posts_published": 0, "likes": 1440, "comments": 74, "shares": 99, "reach": 1407, "impressions": 2888, "views": 1773, "watch_time": 835, "subscribers_gained": -4}, {"date": "2025-08-20", "followers": 34990, "engagement_rate": 3.34, "posts_published": 0, "likes": 2000, "comments": 107, "shares": 56, "reach": 2140, "impressions": 8122, "views": 3474, "watch_time": 1819, "subscribers_gained": 47}, {"date": "2025-08-21", "followers": 32735, "engagement_rate": 4.94, "posts_published": 1, "likes": 159, "comments": 144, "shares": 14, "reach": 2164, "impressions": 12906, "views": 6152, "watch_time": 2949, "subscribers_gained": 21}, {"date": "2025-08-22", "followers": 35502, "engagement_rate": 7.27, "posts_published": 0, "likes": 1441, "comments": 41, "shares": 59, "reach": 1145, "impressions": 11791, "views": 4576, "watch_time": 4695, "subscribers_gained": 42}, {"date": "2025-08-23", "followers": 33020, "engagement_rate": 3.57, "posts_published": 0, "likes": 1334, "comments": 15, "shares": 94, "reach": 3799, "impressions": 4245, "views": 14552, "watch_time": 2363, "subscribers_gained": 15}, {"date": "2025-08-24", "followers": 31496, "engagement_rate": 5.17, "posts_published": 0, "likes": 1485, "comments": 15, "shares": 12, "reach": 1724, "impressions": 2112, "views": 7252, "watch_time": 2258, "subscribers_gained": 38}, {"date": "2025-08-25", "followers": 33144, "engagement_rate": 4.64, "posts_published": 0, "likes": 1913, "comments": 123, "shares": 69, "reach": 3280, "impressions": 8496, "views": 1947, "watch_time": 1028, "subscribers_gained": 12}, {"date": "2025-08-26", "followers": 34242, "engagement_rate": 3.99, "posts_published": 0, "likes": 985, "comments": 22, "shares": 28, "reach": 5205, "impressions": 7302, "views": 12340, "watch_time": 1059, "subscribers_gained": 40}, {"date": "2025-08-27", "followers": 35617, "engagement_rate": 7.24, "posts_published": 3, "likes": 1793, "comments": 65, "shares": 55, "reach": 9335, "impressions": 6996, "views": 17745, "watch_time": 4323, "subscribers_gained": 24}], "summary": {"total_followers_gained": 6212, "avg_engagement_rate": 4.86, "total_posts": 35, "best_performing_day": "2025-08-19"}}, "twitter_user_2_8451": {"account_id": "twitter_user_2_8451", "platform": "twitter", "daily_metrics": [{"date": "2025-07-29", "followers": 9102, "engagement_rate": 5.04, "posts_published": 2, "likes": 296, "comments": 58, "shares": 79, "reach": 4745, "impressions": 13776}, {"date": "2025-07-30", "followers": 9112, "engagement_rate": 2.94, "posts_published": 3, "likes": 290, "comments": 21, "shares": 90, "reach": 6079, "impressions": 6072}, {"date": "2025-07-31", "followers": 9304, "engagement_rate": 4.59, "posts_published": 1, "likes": 1128, "comments": 108, "shares": 63, "reach": 4065, "impressions": 13613}, {"date": "2025-08-01", "followers": 9510, "engagement_rate": 5.23, "posts_published": 3, "likes": 1919, "comments": 90, "shares": 96, "reach": 1420, "impressions": 13481}, {"date": "2025-08-02", "followers": 9442, "engagement_rate": 4.82, "posts_published": 0, "likes": 1753, "comments": 26, "shares": 98, "reach": 7128, "impressions": 3580}, {"date": "2025-08-03", "followers": 9577, "engagement_rate": 3.22, "posts_published": 2, "likes": 1511, "comments": 59, "shares": 36, "reach": 4838, "impressions": 7127}, {"date": "2025-08-04", "followers": 9318, "engagement_rate": 5.59, "posts_published": 2, "likes": 989, "comments": 11, "shares": 92, "reach": 5471, "impressions": 8255}, {"date": "2025-08-05", "followers": 9774, "engagement_rate": 7.2, "posts_published": 0, "likes": 1084, "comments": 171, "shares": 34, "reach": 3291, "impressions": 2831}, {"date": "2025-08-06", "followers": 10678, "engagement_rate": 6.21, "posts_published": 2, "likes": 1461, "comments": 91, "shares": 20, "reach": 4300, "impressions": 8094}, {"date": "2025-08-07", "followers": 8688, "engagement_rate": 7.13, "posts_published": 3, "likes": 790, "comments": 60, "shares": 8, "reach": 8800, "impressions": 14400}, {"date": "2025-08-08", "followers": 9422, "engagement_rate": 4.93, "posts_published": 2, "likes": 1442, "comments": 117, "shares": 38, "reach": 6080, "impressions": 2303}, {"date": "2025-08-09", "followers": 10246, "engagement_rate": 4.33, "posts_published": 2, "likes": 1490, "comments": 128, "shares": 15, "reach": 5799, "impressions": 7714}, {"date": "2025-08-10", "followers": 9270, "engagement_rate": 7.22, "posts_published": 2, "likes": 1147, "comments": 186, "shares": 98, "reach": 7856, "impressions": 8920}, {"date": "2025-08-11", "followers": 10415, "engagement_rate": 2.27, "posts_published": 0, "likes": 1505, "comments": 150, "shares": 30, "reach": 6158, "impressions": 7985}, {"date": "2025-08-12", "followers": 10152, "engagement_rate": 4.45, "posts_published": 2, "likes": 1293, "comments": 131, "shares": 49, "reach": 7350, "impressions": 3798}, {"date": "2025-08-13", "followers": 9282, "engagement_rate": 5.32, "posts_published": 3, "likes": 1384, "comments": 110, "shares": 95, "reach": 9103, "impressions": 13488}, {"date": "2025-08-14", "followers": 9150, "engagement_rate": 3.17, "posts_published": 3, "likes": 343, "comments": 150, "shares": 68, "reach": 4457, "impressions": 14405}, {"date": "2025-08-15", "followers": 9238, "engagement_rate": 4.33, "posts_published": 3, "likes": 1907, "comments": 128, "shares": 64, "reach": 3164, "impressions": 3375}, {"date": "2025-08-16", "followers": 12306, "engagement_rate": 5.23, "posts_published": 3, "likes": 1012, "comments": 121, "shares": 45, "reach": 1894, "impressions": 11484}, {"date": "2025-08-17", "followers": 9197, "engagement_rate": 3.45, "posts_published": 3, "likes": 1013, "comments": 134, "shares": 45, "reach": 4614, "impressions": 8527}, {"date": "2025-08-18", "followers": 8122, "engagement_rate": 5.88, "posts_published": 0, "likes": 871, "comments": 163, "shares": 44, "reach": 8991, "impressions": 4971}, {"date": "2025-08-19", "followers": 11370, "engagement_rate": 5.39, "posts_published": 1, "likes": 1888, "comments": 70, "shares": 10, "reach": 4982, "impressions": 14622}, {"date": "2025-08-20", "followers": 11104, "engagement_rate": 5.54, "posts_published": 0, "likes": 149, "comments": 19, "shares": 13, "reach": 3793, "impressions": 7562}, {"date": "2025-08-21", "followers": 13035, "engagement_rate": 6.0, "posts_published": 2, "likes": 613, "comments": 198, "shares": 31, "reach": 3718, "impressions": 2986}, {"date": "2025-08-22", "followers": 11142, "engagement_rate": 3.39, "posts_published": 1, "likes": 741, "comments": 182, "shares": 37, "reach": 7070, "impressions": 10016}, {"date": "2025-08-23", "followers": 10677, "engagement_rate": 7.0, "posts_published": 0, "likes": 1287, "comments": 129, "shares": 53, "reach": 9629, "impressions": 6754}, {"date": "2025-08-24", "followers": 9102, "engagement_rate": 7.07, "posts_published": 3, "likes": 913, "comments": 144, "shares": 100, "reach": 7735, "impressions": 13289}, {"date": "2025-08-25", "followers": 14016, "engagement_rate": 4.97, "posts_published": 0, "likes": 1842, "comments": 109, "shares": 64, "reach": 1541, "impressions": 8271}, {"date": "2025-08-26", "followers": 9046, "engagement_rate": 7.7, "posts_published": 1, "likes": 386, "comments": 79, "shares": 14, "reach": 9011, "impressions": 8138}, {"date": "2025-08-27", "followers": 13771, "engagement_rate": 4.94, "posts_published": 3, "likes": 1782, "comments": 191, "shares": 97, "reach": 7308, "impressions": 3007}], "summary": {"total_followers_gained": 10302, "avg_engagement_rate": 5.15, "total_posts": 52, "best_performing_day": "2025-08-26"}}, "twitter_user_3_5354": {"account_id": "twitter_user_3_5354", "platform": "twitter", "daily_metrics": [{"date": "2025-07-29", "followers": 6131, "engagement_rate": 5.32, "posts_published": 1, "likes": 1504, "comments": 123, "shares": 72, "reach": 7833, "impressions": 12262}, {"date": "2025-07-30", "followers": 6271, "engagement_rate": 7.69, "posts_published": 0, "likes": 1954, "comments": 137, "shares": 79, "reach": 9575, "impressions": 6001}, {"date": "2025-07-31", "followers": 6269, "engagement_rate": 2.29, "posts_published": 1, "likes": 735, "comments": 34, "shares": 8, "reach": 6983, "impressions": 4618}, {"date": "2025-08-01", "followers": 6665, "engagement_rate": 7.12, "posts_published": 2, "likes": 593, "comments": 12, "shares": 45, "reach": 5437, "impressions": 14317}, {"date": "2025-08-02", "followers": 6567, "engagement_rate": 4.79, "posts_published": 3, "likes": 1014, "comments": 19, "shares": 49, "reach": 6764, "impressions": 9926}, {"date": "2025-08-03", "followers": 7091, "engagement_rate": 6.59, "posts_published": 0, "likes": 576, "comments": 77, "shares": 78, "reach": 1472, "impressions": 7671}, {"date": "2025-08-04", "followers": 6407, "engagement_rate": 6.77, "posts_published": 2, "likes": 1919, "comments": 54, "shares": 61, "reach": 4576, "impressions": 9439}, {"date": "2025-08-05", "followers": 6250, "engagement_rate": 4.45, "posts_published": 3, "likes": 1885, "comments": 194, "shares": 62, "reach": 1887, "impressions": 14173}, {"date": "2025-08-06", "followers": 6179, "engagement_rate": 2.8, "posts_published": 3, "likes": 276, "comments": 118, "shares": 62, "reach": 9272, "impressions": 14954}, {"date": "2025-08-07", "followers": 7841, "engagement_rate": 7.42, "posts_published": 1, "likes": 188, "comments": 58, "shares": 55, "reach": 4993, "impressions": 8981}, {"date": "2025-08-08", "followers": 6041, "engagement_rate": 7.48, "posts_published": 1, "likes": 1051, "comments": 100, "shares": 94, "reach": 5921, "impressions": 12040}, {"date": "2025-08-09", "followers": 6956, "engagement_rate": 6.04, "posts_published": 0, "likes": 1991, "comments": 129, "shares": 16, "reach": 5980, "impressions": 2827}, {"date": "2025-08-10", "followers": 7487, "engagement_rate": 2.04, "posts_published": 3, "likes": 1615, "comments": 89, "shares": 91, "reach": 1686, "impressions": 7320}, {"date": "2025-08-11", "followers": 6014, "engagement_rate": 2.86, "posts_published": 0, "likes": 1736, "comments": 126, "shares": 75, "reach": 5004, "impressions": 5552}, {"date": "2025-08-12", "followers": 8553, "engagement_rate": 3.43, "posts_published": 1, "likes": 1157, "comments": 24, "shares": 41, "reach": 2726, "impressions": 4634}, {"date": "2025-08-13", "followers": 7946, "engagement_rate": 7.05, "posts_published": 2, "likes": 750, "comments": 185, "shares": 73, "reach": 6190, "impressions": 5174}, {"date": "2025-08-14", "followers": 6339, "engagement_rate": 3.61, "posts_published": 1, "likes": 1349, "comments": 165, "shares": 16, "reach": 3872, "impressions": 13862}, {"date": "2025-08-15", "followers": 6488, "engagement_rate": 7.22, "posts_published": 0, "likes": 1956, "comments": 63, "shares": 5, "reach": 7789, "impressions": 5894}, {"date": "2025-08-16", "followers": 6779, "engagement_rate": 5.81, "posts_published": 2, "likes": 1149, "comments": 135, "shares": 85, "reach": 3376, "impressions": 5932}, {"date": "2025-08-17", "followers": 7271, "engagement_rate": 5.51, "posts_published": 1, "likes": 229, "comments": 62, "shares": 14, "reach": 8917, "impressions": 6398}, {"date": "2025-08-18", "followers": 7231, "engagement_rate": 6.74, "posts_published": 1, "likes": 515, "comments": 118, "shares": 8, "reach": 6113, "impressions": 7444}, {"date": "2025-08-19", "followers": 7727, "engagement_rate": 2.53, "posts_published": 1, "likes": 1622, "comments": 34, "shares": 96, "reach": 5183, "impressions": 11707}, {"date": "2025-08-20", "followers": 5427, "engagement_rate": 2.01, "posts_published": 3, "likes": 1337, "comments": 58, "shares": 68, "reach": 2299, "impressions": 14684}, {"date": "2025-08-21", "followers": 6177, "engagement_rate": 5.03, "posts_published": 3, "likes": 1471, "comments": 185, "shares": 69, "reach": 3478, "impressions": 8828}, {"date": "2025-08-22", "followers": 9059, "engagement_rate": 4.29, "posts_published": 2, "likes": 466, "comments": 84, "shares": 75, "reach": 5900, "impressions": 13612}, {"date": "2025-08-23", "followers": 7931, "engagement_rate": 6.85, "posts_published": 1, "likes": 1156, "comments": 137, "shares": 62, "reach": 6662, "impressions": 2321}, {"date": "2025-08-24", "followers": 9901, "engagement_rate": 5.35, "posts_published": 1, "likes": 1251, "comments": 22, "shares": 48, "reach": 1611, "impressions": 7558}, {"date": "2025-08-25", "followers": 10694, "engagement_rate": 7.71, "posts_published": 2, "likes": 198, "comments": 139, "shares": 37, "reach": 4090, "impressions": 8729}, {"date": "2025-08-26", "followers": 6663, "engagement_rate": 7.99, "posts_published": 1, "likes": 1522, "comments": 68, "shares": 43, "reach": 1335, "impressions": 11481}, {"date": "2025-08-27", "followers": 10220, "engagement_rate": 3.54, "posts_published": 2, "likes": 376, "comments": 29, "shares": 81, "reach": 2955, "impressions": 3355}], "summary": {"total_followers_gained": 13383, "avg_engagement_rate": 5.28, "total_posts": 44, "best_performing_day": "2025-08-26"}}, "instagram_user_3_4872": {"account_id": "instagram_user_3_4872", "platform": "instagram", "daily_metrics": [{"date": "2025-07-29", "followers": 4905, "engagement_rate": 3.33, "posts_published": 3, "likes": 1227, "comments": 97, "shares": 21, "reach": 3204, "impressions": 4316, "story_views": 1487, "profile_visits": 416, "saves": 271}, {"date": "2025-07-30", "followers": 4912, "engagement_rate": 4.08, "posts_published": 0, "likes": 1564, "comments": 42, "shares": 43, "reach": 7269, "impressions": 12956, "story_views": 765, "profile_visits": 692, "saves": 134}, {"date": "2025-07-31", "followers": 4955, "engagement_rate": 3.81, "posts_published": 1, "likes": 1306, "comments": 55, "shares": 97, "reach": 3359, "impressions": 7881, "story_views": 1745, "profile_visits": 792, "saves": 289}, {"date": "2025-08-01", "followers": 4848, "engagement_rate": 4.95, "posts_published": 0, "likes": 1969, "comments": 76, "shares": 56, "reach": 4941, "impressions": 9713, "story_views": 924, "profile_visits": 488, "saves": 182}, {"date": "2025-08-02", "followers": 5697, "engagement_rate": 5.49, "posts_published": 0, "likes": 1813, "comments": 47, "shares": 81, "reach": 7173, "impressions": 10660, "story_views": 1341, "profile_visits": 284, "saves": 69}, {"date": "2025-08-03", "followers": 5335, "engagement_rate": 7.24, "posts_published": 0, "likes": 1377, "comments": 187, "shares": 62, "reach": 6210, "impressions": 9336, "story_views": 779, "profile_visits": 702, "saves": 292}, {"date": "2025-08-04", "followers": 5913, "engagement_rate": 2.35, "posts_published": 1, "likes": 1572, "comments": 191, "shares": 40, "reach": 1439, "impressions": 4036, "story_views": 2195, "profile_visits": 110, "saves": 100}, {"date": "2025-08-05", "followers": 5472, "engagement_rate": 7.6, "posts_published": 0, "likes": 1363, "comments": 80, "shares": 29, "reach": 1442, "impressions": 7405, "story_views": 2297, "profile_visits": 982, "saves": 84}, {"date": "2025-08-06", "followers": 4529, "engagement_rate": 2.4, "posts_published": 0, "likes": 1357, "comments": 69, "shares": 84, "reach": 8874, "impressions": 8854, "story_views": 722, "profile_visits": 666, "saves": 282}, {"date": "2025-08-07", "followers": 6471, "engagement_rate": 4.54, "posts_published": 0, "likes": 862, "comments": 119, "shares": 13, "reach": 7307, "impressions": 10755, "story_views": 1956, "profile_visits": 571, "saves": 142}, {"date": "2025-08-08", "followers": 6105, "engagement_rate": 3.16, "posts_published": 2, "likes": 1125, "comments": 141, "shares": 29, "reach": 3987, "impressions": 11910, "story_views": 1355, "profile_visits": 598, "saves": 188}, {"date": "2025-08-09", "followers": 4674, "engagement_rate": 2.42, "posts_published": 0, "likes": 716, "comments": 59, "shares": 21, "reach": 4536, "impressions": 5181, "story_views": 2078, "profile_visits": 701, "saves": 167}, {"date": "2025-08-10", "followers": 7089, "engagement_rate": 4.1, "posts_published": 1, "likes": 1439, "comments": 178, "shares": 20, "reach": 5288, "impressions": 10436, "story_views": 2316, "profile_visits": 801, "saves": 79}, {"date": "2025-08-11", "followers": 6634, "engagement_rate": 7.42, "posts_published": 3, "likes": 1694, "comments": 34, "shares": 15, "reach": 4063, "impressions": 6744, "story_views": 1635, "profile_visits": 832, "saves": 128}, {"date": "2025-08-12", "followers": 5633, "engagement_rate": 7.66, "posts_published": 3, "likes": 601, "comments": 108, "shares": 16, "reach": 9602, "impressions": 3819, "story_views": 844, "profile_visits": 280, "saves": 233}, {"date": "2025-08-13", "followers": 6825, "engagement_rate": 2.8, "posts_published": 2, "likes": 913, "comments": 48, "shares": 36, "reach": 6778, "impressions": 7042, "story_views": 1738, "profile_visits": 344, "saves": 71}, {"date": "2025-08-14", "followers": 5433, "engagement_rate": 5.22, "posts_published": 3, "likes": 479, "comments": 10, "shares": 82, "reach": 3786, "impressions": 10912, "story_views": 2537, "profile_visits": 363, "saves": 116}, {"date": "2025-08-15", "followers": 5704, "engagement_rate": 4.5, "posts_published": 3, "likes": 1416, "comments": 155, "shares": 64, "reach": 8658, "impressions": 8796, "story_views": 1761, "profile_visits": 189, "saves": 273}, {"date": "2025-08-16", "followers": 8361, "engagement_rate": 3.81, "posts_published": 3, "likes": 442, "comments": 53, "shares": 85, "reach": 9228, "impressions": 10563, "story_views": 609, "profile_visits": 762, "saves": 218}, {"date": "2025-08-17", "followers": 7280, "engagement_rate": 4.21, "posts_published": 1, "likes": 1843, "comments": 141, "shares": 57, "reach": 3059, "impressions": 14446, "story_views": 2257, "profile_visits": 243, "saves": 258}, {"date": "2025-08-18", "followers": 7925, "engagement_rate": 3.68, "posts_published": 0, "likes": 1046, "comments": 38, "shares": 16, "reach": 1396, "impressions": 9960, "story_views": 2636, "profile_visits": 735, "saves": 192}, {"date": "2025-08-19", "followers": 5787, "engagement_rate": 3.35, "posts_published": 0, "likes": 219, "comments": 152, "shares": 25, "reach": 5305, "impressions": 2483, "story_views": 2879, "profile_visits": 886, "saves": 177}, {"date": "2025-08-20", "followers": 4135, "engagement_rate": 7.5, "posts_published": 3, "likes": 814, "comments": 24, "shares": 61, "reach": 9612, "impressions": 8858, "story_views": 1645, "profile_visits": 343, "saves": 29}, {"date": "2025-08-21", "followers": 4123, "engagement_rate": 4.21, "posts_published": 0, "likes": 827, "comments": 40, "shares": 53, "reach": 1773, "impressions": 6061, "story_views": 627, "profile_visits": 699, "saves": 261}, {"date": "2025-08-22", "followers": 9105, "engagement_rate": 4.23, "posts_published": 2, "likes": 1090, "comments": 110, "shares": 51, "reach": 9310, "impressions": 2663, "story_views": 2819, "profile_visits": 572, "saves": 39}, {"date": "2025-08-23", "followers": 9155, "engagement_rate": 5.24, "posts_published": 3, "likes": 1859, "comments": 79, "shares": 90, "reach": 7156, "impressions": 7849, "story_views": 686, "profile_visits": 627, "saves": 233}, {"date": "2025-08-24", "followers": 8831, "engagement_rate": 6.29, "posts_published": 3, "likes": 1744, "comments": 113, "shares": 78, "reach": 7313, "impressions": 12946, "story_views": 1902, "profile_visits": 311, "saves": 43}, {"date": "2025-08-25", "followers": 6957, "engagement_rate": 3.97, "posts_published": 2, "likes": 734, "comments": 150, "shares": 88, "reach": 4757, "impressions": 9019, "story_views": 1435, "profile_visits": 867, "saves": 196}, {"date": "2025-08-26", "followers": 4569, "engagement_rate": 5.17, "posts_published": 3, "likes": 1997, "comments": 186, "shares": 70, "reach": 5757, "impressions": 4699, "story_views": 2450, "profile_visits": 404, "saves": 26}, {"date": "2025-08-27", "followers": 4992, "engagement_rate": 2.65, "posts_published": 3, "likes": 1358, "comments": 146, "shares": 85, "reach": 4049, "impressions": 7052, "story_views": 989, "profile_visits": 566, "saves": 138}], "summary": {"total_followers_gained": 3107, "avg_engagement_rate": 4.58, "total_posts": 45, "best_performing_day": "2025-08-12"}}, "youtube_user_3_1048": {"account_id": "youtube_user_3_1048", "platform": "youtube", "daily_metrics": [{"date": "2025-07-29", "followers": 14509, "engagement_rate": 3.46, "posts_published": 2, "likes": 1677, "comments": 102, "shares": 100, "reach": 3635, "impressions": 11905, "views": 8137, "watch_time": 517, "subscribers_gained": 20}, {"date": "2025-07-30", "followers": 14600, "engagement_rate": 3.53, "posts_published": 0, "likes": 343, "comments": 90, "shares": 13, "reach": 5141, "impressions": 2720, "views": 14370, "watch_time": 1301, "subscribers_gained": 39}, {"date": "2025-07-31", "followers": 14655, "engagement_rate": 4.66, "posts_published": 3, "likes": 1139, "comments": 192, "shares": 8, "reach": 5833, "impressions": 12598, "views": 16093, "watch_time": 1775, "subscribers_gained": 25}, {"date": "2025-08-01", "followers": 14692, "engagement_rate": 6.84, "posts_published": 2, "likes": 1996, "comments": 13, "shares": 66, "reach": 1021, "impressions": 5952, "views": 12541, "watch_time": 3399, "subscribers_gained": 50}, {"date": "2025-08-02", "followers": 15285, "engagement_rate": 2.62, "posts_published": 2, "likes": 653, "comments": 74, "shares": 90, "reach": 4626, "impressions": 11621, "views": 9019, "watch_time": 4635, "subscribers_gained": 49}, {"date": "2025-08-03", "followers": 14439, "engagement_rate": 2.87, "posts_published": 1, "likes": 738, "comments": 112, "shares": 64, "reach": 8490, "impressions": 2867, "views": 5420, "watch_time": 744, "subscribers_gained": 23}, {"date": "2025-08-04", "followers": 15607, "engagement_rate": 2.63, "posts_published": 3, "likes": 620, "comments": 71, "shares": 33, "reach": 2263, "impressions": 3231, "views": 19301, "watch_time": 1604, "subscribers_gained": 28}, {"date": "2025-08-05", "followers": 14838, "engagement_rate": 5.03, "posts_published": 2, "likes": 1580, "comments": 117, "shares": 8, "reach": 8630, "impressions": 7893, "views": 9926, "watch_time": 2518, "subscribers_gained": 18}, {"date": "2025-08-06", "followers": 15477, "engagement_rate": 2.02, "posts_published": 1, "likes": 1756, "comments": 94, "shares": 80, "reach": 3954, "impressions": 5014, "views": 19078, "watch_time": 3045, "subscribers_gained": 40}, {"date": "2025-08-07", "followers": 14464, "engagement_rate": 5.43, "posts_published": 3, "likes": 157, "comments": 165, "shares": 20, "reach": 8475, "impressions": 9811, "views": 6406, "watch_time": 3536, "subscribers_gained": 0}, {"date": "2025-08-08", "followers": 16089, "engagement_rate": 6.99, "posts_published": 0, "likes": 941, "comments": 161, "shares": 59, "reach": 6436, "impressions": 2484, "views": 1437, "watch_time": 4400, "subscribers_gained": 12}, {"date": "2025-08-09", "followers": 15070, "engagement_rate": 5.27, "posts_published": 2, "likes": 1187, "comments": 183, "shares": 43, "reach": 4114, "impressions": 6600, "views": 14963, "watch_time": 3667, "subscribers_gained": -2}, {"date": "2025-08-10", "followers": 16165, "engagement_rate": 2.47, "posts_published": 3, "likes": 533, "comments": 102, "shares": 98, "reach": 4645, "impressions": 5267, "views": 16167, "watch_time": 2030, "subscribers_gained": 34}, {"date": "2025-08-11", "followers": 14639, "engagement_rate": 2.88, "posts_published": 2, "likes": 951, "comments": 118, "shares": 82, "reach": 3680, "impressions": 7206, "views": 14067, "watch_time": 1605, "subscribers_gained": 6}, {"date": "2025-08-12", "followers": 16021, "engagement_rate": 7.23, "posts_published": 1, "likes": 1631, "comments": 111, "shares": 98, "reach": 1828, "impressions": 14588, "views": 15711, "watch_time": 4701, "subscribers_gained": 22}, {"date": "2025-08-13", "followers": 17104, "engagement_rate": 3.34, "posts_published": 2, "likes": 624, "comments": 188, "shares": 10, "reach": 7235, "impressions": 8010, "views": 9776, "watch_time": 4196, "subscribers_gained": 47}, {"date": "2025-08-14", "followers": 17389, "engagement_rate": 5.65, "posts_published": 2, "likes": 1084, "comments": 17, "shares": 86, "reach": 2030, "impressions": 8599, "views": 4978, "watch_time": 2300, "subscribers_gained": 26}, {"date": "2025-08-15", "followers": 13812, "engagement_rate": 4.26, "posts_published": 1, "likes": 1983, "comments": 144, "shares": 42, "reach": 6331, "impressions": 13071, "views": 19533, "watch_time": 4165, "subscribers_gained": 44}, {"date": "2025-08-16", "followers": 16381, "engagement_rate": 6.16, "posts_published": 1, "likes": 898, "comments": 20, "shares": 68, "reach": 3941, "impressions": 9762, "views": 9005, "watch_time": 3646, "subscribers_gained": 10}, {"date": "2025-08-17", "followers": 17169, "engagement_rate": 2.79, "posts_published": 1, "likes": 366, "comments": 18, "shares": 99, "reach": 1801, "impressions": 6758, "views": 5887, "watch_time": 1104, "subscribers_gained": 16}, {"date": "2025-08-18", "followers": 13909, "engagement_rate": 5.33, "posts_published": 2, "likes": 703, "comments": 122, "shares": 27, "reach": 7614, "impressions": 3159, "views": 4874, "watch_time": 2245, "subscribers_gained": 10}, {"date": "2025-08-19", "followers": 16693, "engagement_rate": 6.29, "posts_published": 1, "likes": 1548, "comments": 189, "shares": 84, "reach": 1536, "impressions": 10479, "views": 17049, "watch_time": 2122, "subscribers_gained": 3}, {"date": "2025-08-20", "followers": 18865, "engagement_rate": 7.38, "posts_published": 3, "likes": 1310, "comments": 25, "shares": 26, "reach": 1406, "impressions": 14106, "views": 10806, "watch_time": 2997, "subscribers_gained": 23}, {"date": "2025-08-21", "followers": 18442, "engagement_rate": 2.09, "posts_published": 3, "likes": 423, "comments": 47, "shares": 27, "reach": 7175, "impressions": 5453, "views": 10528, "watch_time": 1166, "subscribers_gained": 16}, {"date": "2025-08-22", "followers": 18565, "engagement_rate": 6.1, "posts_published": 2, "likes": 686, "comments": 148, "shares": 32, "reach": 7248, "impressions": 7164, "views": 1969, "watch_time": 3745, "subscribers_gained": 28}, {"date": "2025-08-23", "followers": 13534, "engagement_rate": 6.79, "posts_published": 3, "likes": 389, "comments": 132, "shares": 16, "reach": 1331, "impressions": 14099, "views": 14077, "watch_time": 3852, "subscribers_gained": -1}, {"date": "2025-08-24", "followers": 19111, "engagement_rate": 2.46, "posts_published": 3, "likes": 1944, "comments": 73, "shares": 44, "reach": 2322, "impressions": 5095, "views": 2302, "watch_time": 3544, "subscribers_gained": 46}, {"date": "2025-08-25", "followers": 15076, "engagement_rate": 4.75, "posts_published": 0, "likes": 490, "comments": 24, "shares": 10, "reach": 9840, "impressions": 2232, "views": 18609, "watch_time": 2317, "subscribers_gained": 29}, {"date": "2025-08-26", "followers": 19829, "engagement_rate": 6.78, "posts_published": 0, "likes": 353, "comments": 111, "shares": 54, "reach": 9310, "impressions": 6963, "views": 19267, "watch_time": 4637, "subscribers_gained": 3}, {"date": "2025-08-27", "followers": 17931, "engagement_rate": 2.7, "posts_published": 1, "likes": 1435, "comments": 98, "shares": 49, "reach": 6710, "impressions": 11072, "views": 17404, "watch_time": 2823, "subscribers_gained": 17}], "summary": {"total_followers_gained": 8270, "avg_engagement_rate": 4.56, "total_posts": 52, "best_performing_day": "2025-08-20"}}}, "metadata": {"generated_at": "2025-08-27T13:51:39.348096+00:00", "version": "1.0", "description": "Sample data for Social Media Manager development and demo", "total_records": {"users": 3, "accounts": 9, "plans": 8, "messages": 66, "analytics_accounts": 9}}}