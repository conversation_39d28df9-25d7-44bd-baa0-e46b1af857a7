"use client";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/lib/api-client";

export interface AccountData {
  id: string;
  platform: string;
  handle: string;
  avatar: string;
  lastSync: Date;
  metrics: {
    followers: number;
    engagement: number;
    reach: number;
    growth: {
      followers: number;
      engagement: number;
    };
  } | null;
}

export function useConnectedAccounts() {
  return useQuery({
    queryKey: ["accounts"],
    queryFn: async (): Promise<AccountData[]> => {
      const response = await apiClient.get("/accounts");
      return response.data.map((account: any) => ({
        ...account,
        lastSync: new Date(account.lastSync),
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}