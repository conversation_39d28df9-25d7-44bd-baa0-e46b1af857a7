import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useSendMessage } from '@/hooks/use-send-message'
import { mockFetch } from '../../utils/test-utils'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Mock ReadableStream for streaming tests
class MockReadableStream {
  constructor(private chunks: string[]) {}
  
  getReader() {
    let index = 0
    return {
      read: jest.fn().mockImplementation(() => {
        if (index >= this.chunks.length) {
          return Promise.resolve({ done: true })
        }
        const value = new TextEncoder().encode(this.chunks[index++])
        return Promise.resolve({ done: false, value })
      }),
      releaseLock: jest.fn(),
    }
  }
}

describe('useSendMessage', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false, gcTime: 0 },
        mutations: { retry: false },
      },
    })
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue('test-token')
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  describe('Basic Functionality', () => {
    it('sends message and updates cache with user message', async () => {
      const streamChunks = [
        'data: {"content": "Hello"}\n',
        'data: {"content": " there"}\n',
        'data: {"done": true}\n',
      ]

      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      // Check if user message was added to cache
      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      expect(chatHistory).toHaveLength(2) // User message + assistant message
      expect(chatHistory[0].content).toBe('Test message')
      expect(chatHistory[0].role).toBe('user')
    })

    it('makes correct API call with proper headers', async () => {
      const streamChunks = ['data: {"done": true}\n']
      
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      expect(fetch).toHaveBeenCalledWith('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
        },
        body: JSON.stringify({ message: 'Test message' }),
      })
    })

    it('uses dev-token when no access token in localStorage', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      const streamChunks = ['data: {"done": true}\n']
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      expect(fetch).toHaveBeenCalledWith('/api/chat', expect.objectContaining({
        headers: expect.objectContaining({
          'Authorization': 'Bearer dev-token',
        }),
      }))
    })
  })

  describe('Streaming Response Handling', () => {
    it('processes streaming chunks correctly', async () => {
      const streamChunks = [
        'data: {"content": "Hello"}\n',
        'data: {"content": " there!"}\n',
        'data: {"done": true}\n',
      ]

      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      const assistantMessage = chatHistory.find(m => m.role === 'assistant')
      
      expect(assistantMessage.content).toBe('Hello there!')
    })

    it('handles multiple data chunks in single stream chunk', async () => {
      const streamChunks = [
        'data: {"content": "First"}\ndata: {"content": " Second"}\n',
        'data: {"done": true}\n',
      ]

      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      const assistantMessage = chatHistory.find(m => m.role === 'assistant')
      
      expect(assistantMessage.content).toBe('First Second')
    })

    it('ignores invalid JSON lines', async () => {
      const consoleWarn = jest.spyOn(console, 'warn').mockImplementation(() => {})
      
      const streamChunks = [
        'data: {"content": "Valid"}\n',
        'data: invalid json\n',
        'data: {"content": " content"}\n',
        'data: {"done": true}\n',
      ]

      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      const assistantMessage = chatHistory.find(m => m.role === 'assistant')
      
      expect(assistantMessage.content).toBe('Valid content')
      expect(consoleWarn).toHaveBeenCalledWith('Failed to parse SSE data:', 'data: invalid json')
      
      consoleWarn.mockRestore()
    })

    it('handles empty chunks gracefully', async () => {
      const streamChunks = [
        'data: \n',
        'data: {"content": "Hello"}\n',
        'data: {"done": true}\n',
      ]

      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      const assistantMessage = chatHistory.find(m => m.role === 'assistant')
      
      expect(assistantMessage.content).toBe('Hello')
    })
  })

  describe('Error Handling', () => {
    it('handles HTTP errors', async () => {
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: false,
          status: 500,
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await expect(result.current.mutateAsync('Test message')).rejects.toThrow(
        'HTTP error! status: 500'
      )
    })

    it('handles network errors', async () => {
      global.fetch = jest.fn(() =>
        Promise.reject(new Error('Network error'))
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await expect(result.current.mutateAsync('Test message')).rejects.toThrow(
        'Network error'
      )
    })

    it('handles missing response body reader', async () => {
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: null,
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await expect(result.current.mutateAsync('Test message')).rejects.toThrow(
        'No response body reader available'
      )
    })

    it('removes messages from cache on error', async () => {
      global.fetch = jest.fn(() =>
        Promise.reject(new Error('Network error'))
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      try {
        await result.current.mutateAsync('Test message')
      } catch (error) {
        // Expected to fail
      }

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      expect(chatHistory).toHaveLength(0) // Messages should be removed
    })

    it('logs errors to console', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {})
      
      global.fetch = jest.fn(() =>
        Promise.reject(new Error('Network error'))
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      try {
        await result.current.mutateAsync('Test message')
      } catch (error) {
        // Expected to fail
      }

      expect(consoleError).toHaveBeenCalledWith('Chat message error:', expect.any(Error))
      expect(consoleError).toHaveBeenCalledWith('Failed to send message:', expect.any(Error))
      
      consoleError.mockRestore()
    })
  })

  describe('Cache Management', () => {
    it('adds user message immediately to cache', async () => {
      const streamChunks = ['data: {"done": true}\n']
      
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      // Check cache before mutation completes
      const mutationPromise = result.current.mutateAsync('Test message')
      
      // User message should be added immediately
      await waitFor(() => {
        const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
        expect(chatHistory).toHaveLength(1)
        expect(chatHistory[0].content).toBe('Test message')
        expect(chatHistory[0].role).toBe('user')
      })

      await mutationPromise
    })

    it('updates assistant message content in real-time', async () => {
      const streamChunks = [
        'data: {"content": "Hello"}\n',
        'data: {"content": " there"}\n',
        'data: {"done": true}\n',
      ]

      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      const assistantMessage = chatHistory.find(m => m.role === 'assistant')
      
      expect(assistantMessage.content).toBe('Hello there')
    })

    it('handles existing messages in cache', async () => {
      // Pre-populate cache
      const existingMessages = [
        { id: '1', role: 'user', content: 'Previous message', timestamp: new Date() },
      ]
      queryClient.setQueryData(["chat", "history"], existingMessages)

      const streamChunks = ['data: {"content": "Response"}\n', 'data: {"done": true}\n']
      
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('New message')

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      expect(chatHistory).toHaveLength(3) // Previous + user + assistant
      expect(chatHistory[0].content).toBe('Previous message')
      expect(chatHistory[1].content).toBe('New message')
      expect(chatHistory[2].content).toBe('Response')
    })
  })

  describe('Message Timestamps and IDs', () => {
    it('generates unique IDs for messages', async () => {
      const streamChunks = ['data: {"done": true}\n']
      
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      await result.current.mutateAsync('Test message')

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      const [userMessage, assistantMessage] = chatHistory
      
      expect(userMessage.id).toBeDefined()
      expect(assistantMessage.id).toBeDefined()
      expect(userMessage.id).not.toBe(assistantMessage.id)
      expect(assistantMessage.id).toContain('_assistant')
    })

    it('sets proper timestamps', async () => {
      const streamChunks = ['data: {"done": true}\n']
      
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          body: new MockReadableStream(streamChunks),
        })
      ) as jest.Mock

      const { result } = renderHook(() => useSendMessage(), { wrapper })

      const beforeSend = new Date()
      await result.current.mutateAsync('Test message')
      const afterSend = new Date()

      const chatHistory = queryClient.getQueryData(["chat", "history"]) as any[]
      const [userMessage, assistantMessage] = chatHistory
      
      expect(new Date(userMessage.timestamp)).toBeInstanceOf(Date)
      expect(new Date(assistantMessage.timestamp)).toBeInstanceOf(Date)
      
      // Timestamps should be within reasonable range
      expect(userMessage.timestamp).toBeGreaterThanOrEqual(beforeSend)
      expect(userMessage.timestamp).toBeLessThanOrEqual(afterSend)
    })
  })
})