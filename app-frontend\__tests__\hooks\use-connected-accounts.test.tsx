import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useConnectedAccounts } from '@/hooks/use-connected-accounts'
import { mockApiResponse } from '../../utils/test-utils'

// Mock the API client
jest.mock('@/lib/api-client', () => ({
  apiClient: {
    get: jest.fn(),
  },
}))

const mockApiClient = require('@/lib/api-client').apiClient

describe('useConnectedAccounts', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false, gcTime: 0 },
      },
    })
    jest.clearAllMocks()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  describe('Successful Data Fetching', () => {
    const mockAccountsData = [
      {
        id: 'yt-1',
        platform: 'youtube',
        handle: '@testchannel',
        avatar: 'https://example.com/avatar1.jpg',
        lastSync: '2024-01-01T12:00:00Z',
        metrics: {
          followers: 10000,
          engagement: 5.2,
          reach: 50000,
          growth: {
            followers: 7.8,
            engagement: 2.1,
          },
        },
      },
      {
        id: 'ig-1',
        platform: 'instagram',
        handle: '@testaccount',
        avatar: 'https://example.com/avatar2.jpg',
        lastSync: '2024-01-01T11:30:00Z',
        metrics: {
          followers: 8500,
          engagement: 3.1,
          reach: 25000,
          growth: {
            followers: 5.2,
            engagement: 1.5,
          },
        },
      },
    ]

    beforeEach(() => {
      mockApiClient.get.mockResolvedValue({
        data: mockAccountsData,
      })
    })

    it('fetches connected accounts successfully', async () => {
      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toHaveLength(2)
      expect(result.current.data![0].platform).toBe('youtube')
      expect(result.current.data![1].platform).toBe('instagram')
    })

    it('calls API client with correct endpoint', async () => {
      renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(mockApiClient.get).toHaveBeenCalledWith('/accounts')
      })
    })

    it('transforms lastSync string to Date object', async () => {
      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const account = result.current.data![0]
      expect(account.lastSync).toBeInstanceOf(Date)
      expect(account.lastSync.toISOString()).toBe('2024-01-01T12:00:00.000Z')
    })

    it('preserves all account properties', async () => {
      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const youtubeAccount = result.current.data![0]
      expect(youtubeAccount).toMatchObject({
        id: 'yt-1',
        platform: 'youtube',
        handle: '@testchannel',
        avatar: 'https://example.com/avatar1.jpg',
        metrics: {
          followers: 10000,
          engagement: 5.2,
          reach: 50000,
          growth: {
            followers: 7.8,
            engagement: 2.1,
          },
        },
      })
    })

    it('handles multiple accounts correctly', async () => {
      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toHaveLength(2)
      
      const platforms = result.current.data!.map(account => account.platform)
      expect(platforms).toContain('youtube')
      expect(platforms).toContain('instagram')
    })
  })

  describe('Empty Response Handling', () => {
    it('handles empty accounts array', async () => {
      mockApiClient.get.mockResolvedValue({
        data: [],
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual([])
    })
  })

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      const apiError = new Error('Failed to fetch accounts')
      mockApiClient.get.mockRejectedValue(apiError)

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBe(apiError)
    })

    it('handles network errors', async () => {
      const networkError = new Error('Network error')
      mockApiClient.get.mockRejectedValue(networkError)

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBe(networkError)
    })

    it('handles malformed response data', async () => {
      mockApiClient.get.mockResolvedValue({
        data: null,
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })
    })
  })

  describe('Loading States', () => {
    it('shows loading state initially', () => {
      mockApiClient.get.mockImplementation(() => new Promise(() => {})) // Never resolves

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      expect(result.current.isLoading).toBe(true)
      expect(result.current.data).toBeUndefined()
    })

    it('transitions from loading to success', async () => {
      mockApiClient.get.mockResolvedValue({
        data: [],
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      expect(result.current.isLoading).toBe(true)

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
        expect(result.current.isSuccess).toBe(true)
      })
    })
  })

  describe('Caching Behavior', () => {
    beforeEach(() => {
      mockApiClient.get.mockResolvedValue({
        data: [{
          id: 'test-1',
          platform: 'youtube',
          handle: '@test',
          avatar: '',
          lastSync: '2024-01-01T12:00:00Z',
          metrics: null,
        }],
      })
    })

    it('uses stale time of 5 minutes', async () => {
      const { result, rerender } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Clear the mock call count
      mockApiClient.get.mockClear()

      // Re-render the hook
      rerender()

      // Should not call API again due to stale time
      expect(mockApiClient.get).not.toHaveBeenCalled()
    })

    it('caches data between renders', async () => {
      const { result, rerender } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const firstData = result.current.data

      rerender()

      expect(result.current.data).toBe(firstData) // Same reference
    })

    it('provides refetch functionality', async () => {
      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      mockApiClient.get.mockClear()
      
      // Trigger refetch
      await result.current.refetch()

      expect(mockApiClient.get).toHaveBeenCalledTimes(1)
    })
  })

  describe('Data Type Validation', () => {
    it('correctly types the returned data', async () => {
      const mockData = [{
        id: 'test-1',
        platform: 'youtube',
        handle: '@test',
        avatar: 'https://example.com/avatar.jpg',
        lastSync: '2024-01-01T12:00:00Z',
        metrics: {
          followers: 1000,
          engagement: 5.0,
          reach: 5000,
          growth: {
            followers: 2.5,
            engagement: 1.0,
          },
        },
      }]

      mockApiClient.get.mockResolvedValue({
        data: mockData,
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const account = result.current.data![0]
      
      // Type checking through property access
      expect(typeof account.id).toBe('string')
      expect(typeof account.platform).toBe('string')
      expect(typeof account.handle).toBe('string')
      expect(typeof account.avatar).toBe('string')
      expect(account.lastSync).toBeInstanceOf(Date)
      
      if (account.metrics) {
        expect(typeof account.metrics.followers).toBe('number')
        expect(typeof account.metrics.engagement).toBe('number')
        expect(typeof account.metrics.reach).toBe('number')
        expect(typeof account.metrics.growth.followers).toBe('number')
        expect(typeof account.metrics.growth.engagement).toBe('number')
      }
    })

    it('handles null metrics correctly', async () => {
      const mockData = [{
        id: 'test-1',
        platform: 'youtube',
        handle: '@test',
        avatar: '',
        lastSync: '2024-01-01T12:00:00Z',
        metrics: null,
      }]

      mockApiClient.get.mockResolvedValue({
        data: mockData,
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const account = result.current.data![0]
      expect(account.metrics).toBeNull()
    })
  })

  describe('Query Key Consistency', () => {
    it('uses consistent query key', async () => {
      mockApiClient.get.mockResolvedValue({
        data: [],
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Check that data is accessible via the expected query key
      const cachedData = queryClient.getQueryData(['accounts'])
      expect(cachedData).toEqual([])
    })
  })

  describe('Date Parsing Edge Cases', () => {
    it('handles invalid date strings gracefully', async () => {
      const mockData = [{
        id: 'test-1',
        platform: 'youtube',
        handle: '@test',
        avatar: '',
        lastSync: 'invalid-date',
        metrics: null,
      }]

      mockApiClient.get.mockResolvedValue({
        data: mockData,
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const account = result.current.data![0]
      expect(account.lastSync).toBeInstanceOf(Date)
      expect(isNaN(account.lastSync.getTime())).toBe(true) // Invalid Date
    })

    it('handles different date formats', async () => {
      const mockData = [
        {
          id: 'test-1',
          platform: 'youtube',
          handle: '@test',
          avatar: '',
          lastSync: '2024-01-01T12:00:00.000Z',
          metrics: null,
        },
        {
          id: 'test-2',
          platform: 'instagram',
          handle: '@test2',
          avatar: '',
          lastSync: '2024-01-01T12:00:00Z',
          metrics: null,
        },
      ]

      mockApiClient.get.mockResolvedValue({
        data: mockData,
      })

      const { result } = renderHook(() => useConnectedAccounts(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      const accounts = result.current.data!
      accounts.forEach(account => {
        expect(account.lastSync).toBeInstanceOf(Date)
        expect(isNaN(account.lastSync.getTime())).toBe(false)
      })
    })
  })
})