import React from 'react'
import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ConnectionModal } from '@/components/connections/connection-modal'
import { renderWithProviders } from '../../utils/test-utils'

describe('ConnectionModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Modal Behavior', () => {
    it('renders when isOpen is true', () => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      expect(screen.getByText('Connect Your Accounts')).toBeInTheDocument()
      expect(screen.getByText(/Link your social media accounts/)).toBeInTheDocument()
    })

    it('does not render when isOpen is false', () => {
      renderWithProviders(<ConnectionModal {...defaultProps} isOpen={false} />)
      
      expect(screen.queryByText('Connect Your Accounts')).not.toBeInTheDocument()
    })

    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const closeButton = screen.getByRole('button', { name: '✕' })
      await user.click(closeButton)
      
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
    })

    it('has proper modal overlay styling', () => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const overlay = screen.getByText('Connect Your Accounts').closest('.fixed')
      expect(overlay).toHaveClass('fixed', 'inset-0', 'bg-black/50', 'z-50')
    })
  })

  describe('Platform Display', () => {
    beforeEach(() => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
    })

    it('displays YouTube platform option', () => {
      expect(screen.getByText('YouTube')).toBeInTheDocument()
      expect(screen.getByText(/Connect your YouTube channel/)).toBeInTheDocument()
    })

    it('displays Instagram platform option', () => {
      expect(screen.getByText('Instagram')).toBeInTheDocument()
      expect(screen.getByText(/Link your Instagram Business account/)).toBeInTheDocument()
    })

    it('displays Twitter platform option', () => {
      expect(screen.getByText('X (Twitter)')).toBeInTheDocument()
      expect(screen.getByText(/Coming soon - Monitor tweets/)).toBeInTheDocument()
    })

    it('displays TikTok platform option', () => {
      expect(screen.getByText('TikTok')).toBeInTheDocument()
      expect(screen.getByText(/Coming soon - Track viral content/)).toBeInTheDocument()
    })

    it('shows connect buttons for available platforms', () => {
      const connectButtons = screen.getAllByRole('button', { name: 'Connect' })
      expect(connectButtons).toHaveLength(2) // YouTube and Instagram
    })

    it('shows coming soon buttons for unavailable platforms', () => {
      const comingSoonButtons = screen.getAllByRole('button', { name: 'Coming Soon' })
      expect(comingSoonButtons).toHaveLength(2) // Twitter and TikTok
    })

    it('disables unavailable platform buttons', () => {
      const comingSoonButtons = screen.getAllByRole('button', { name: 'Coming Soon' })
      comingSoonButtons.forEach(button => {
        expect(button).toBeDisabled()
      })
    })

    it('applies opacity to unavailable platforms', () => {
      const twitterCard = screen.getByText('X (Twitter)').closest('div')?.closest('div')
      const tiktokCard = screen.getByText('TikTok').closest('div')?.closest('div')
      
      expect(twitterCard).toHaveClass('opacity-60')
      expect(tiktokCard).toHaveClass('opacity-60')
    })
  })

  describe('Connection Flow', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('handles YouTube connection', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const youtubeConnectButton = screen.getByRole('button', { name: 'Connect' })
      await user.click(youtubeConnectButton)
      
      // Should show connecting state
      expect(screen.getByText('Connecting...')).toBeInTheDocument()
      expect(youtubeConnectButton).toBeDisabled()
    })

    it('handles Instagram connection', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const connectButtons = screen.getAllByRole('button', { name: 'Connect' })
      const instagramConnectButton = connectButtons[1] // Second connect button is Instagram
      
      await user.click(instagramConnectButton)
      
      // Should show connecting state
      expect(screen.getByText('Connecting...')).toBeInTheDocument()
      expect(instagramConnectButton).toBeDisabled()
    })

    it('completes connection after timeout and closes modal', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const connectButton = screen.getByRole('button', { name: 'Connect' })
      await user.click(connectButton)
      
      // Fast-forward time
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
      })
    })

    it('prevents multiple simultaneous connections', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const connectButtons = screen.getAllByRole('button', { name: 'Connect' })
      const [youtubeButton, instagramButton] = connectButtons
      
      // Click YouTube first
      await user.click(youtubeButton)
      expect(screen.getByText('Connecting...')).toBeInTheDocument()
      
      // Instagram button should be disabled during YouTube connection
      expect(instagramButton).toBeDisabled()
    })

    it('resets connecting state after connection completes', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const connectButton = screen.getByRole('button', { name: 'Connect' })
      await user.click(connectButton)
      
      // Should show connecting
      expect(screen.getByText('Connecting...')).toBeInTheDocument()
      
      // Fast-forward time
      jest.advanceTimersByTime(2000)
      
      await waitFor(() => {
        expect(defaultProps.onClose).toHaveBeenCalled()
      })
    })
  })

  describe('Security Notice', () => {
    it('displays OAuth security information', () => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      expect(screen.getByText(/We use secure OAuth 2.0 authentication/)).toBeInTheDocument()
      expect(screen.getByText(/Your account credentials are never stored/)).toBeInTheDocument()
    })

    it('positions security notice at the bottom', () => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const securityNotice = screen.getByText(/We use secure OAuth 2.0 authentication/).closest('div')
      expect(securityNotice).toHaveClass('p-6', 'border-t', 'bg-muted/30')
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
    })

    it('has proper heading hierarchy', () => {
      expect(screen.getByRole('heading', { level: 2, name: 'Connect Your Accounts' })).toBeInTheDocument()
      expect(screen.getByRole('heading', { level: 3, name: 'YouTube' })).toBeInTheDocument()
      expect(screen.getByRole('heading', { level: 3, name: 'Instagram' })).toBeInTheDocument()
    })

    it('provides descriptive button labels', () => {
      const connectButtons = screen.getAllByRole('button', { name: 'Connect' })
      const comingSoonButtons = screen.getAllByRole('button', { name: 'Coming Soon' })
      
      expect(connectButtons.length).toBeGreaterThan(0)
      expect(comingSoonButtons.length).toBeGreaterThan(0)
    })

    it('has proper focus management', async () => {
      const user = userEvent.setup()
      
      const closeButton = screen.getByRole('button', { name: '✕' })
      await user.tab()
      
      // Should be able to tab through interactive elements
      expect(document.activeElement).toBeDefined()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      
      // Should be able to activate buttons with Enter
      const connectButton = screen.getByRole('button', { name: 'Connect' })
      connectButton.focus()
      await user.keyboard('{Enter}')
      
      expect(screen.getByText('Connecting...')).toBeInTheDocument()
    })
  })

  describe('Visual Elements', () => {
    beforeEach(() => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
    })

    it('displays platform icons', () => {
      // Icons are represented as SVG elements with specific classes
      expect(screen.getByText('YouTube').closest('div')?.querySelector('svg')).toBeInTheDocument()
      expect(screen.getByText('Instagram').closest('div')?.querySelector('svg')).toBeInTheDocument()
    })

    it('applies correct platform colors', () => {
      const youtubeIcon = screen.getByText('YouTube').closest('div')?.querySelector('.text-red-500')
      const instagramIcon = screen.getByText('Instagram').closest('div')?.querySelector('.text-pink-500')
      
      expect(youtubeIcon).toBeInTheDocument()
      expect(instagramIcon).toBeInTheDocument()
    })

    it('has responsive design classes', () => {
      const modal = screen.getByText('Connect Your Accounts').closest('.max-w-2xl')
      expect(modal).toHaveClass('w-full', 'max-h-[90vh]', 'overflow-y-auto')
    })
  })

  describe('Error Handling', () => {
    it('handles connection errors gracefully', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
      
      // Mock console.error to avoid error output in tests
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {})
      
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const connectButton = screen.getByRole('button', { name: 'Connect' })
      await user.click(connectButton)
      
      // Component should handle the connection attempt
      expect(screen.getByText('Connecting...')).toBeInTheDocument()
      
      consoleError.mockRestore()
    })
  })

  describe('Modal Content Overflow', () => {
    it('handles content overflow with scrolling', () => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const modalContent = screen.getByText('Connect Your Accounts').closest('.max-h-[90vh]')
      expect(modalContent).toHaveClass('overflow-y-auto')
    })

    it('maintains proper spacing between sections', () => {
      renderWithProviders(<ConnectionModal {...defaultProps} />)
      
      const platformsContainer = screen.getByText('YouTube').closest('.space-y-4')
      expect(platformsContainer).toBeInTheDocument()
    })
  })
})