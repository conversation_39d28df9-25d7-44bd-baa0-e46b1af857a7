class ApiClient {
  private baseURL: string;
  private authToken?: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
  }

  setAuthToken(token: string) {
    this.authToken = token;
  }

  private async request(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    } else {
      // Use development token for local development
      const devToken = localStorage?.getItem('access_token') || 'dev-token';
      headers.Authorization = `Bearer ${devToken}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response;
  }

  async get(endpoint: string): Promise<{ data: any }> {
    const response = await this.request(endpoint, { method: 'GET' });
    const data = await response.json();
    return { data };
  }

  async post(endpoint: string, body: any = {}): Promise<{ data: any }> {
    const response = await this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
    const data = await response.json();
    return { data };
  }

  async put(endpoint: string, body: any = {}): Promise<{ data: any }> {
    const response = await this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(body),
    });
    const data = await response.json();
    return { data };
  }

  async delete(endpoint: string): Promise<{ data: any }> {
    const response = await this.request(endpoint, { method: 'DELETE' });
    const data = await response.json();
    return { data };
  }

  // Platform-specific methods
  async connectPlatform(platform: string, authCode: string): Promise<void> {
    await this.post(`/connect/${platform}`, { authCode });
  }

  async disconnectPlatform(platform: string): Promise<void> {
    await this.delete(`/connect/${platform}`);
  }

  async chatWithAgent(message: string): Promise<ReadableStream> {
    const response = await this.request('/chat', {
      method: 'POST',
      body: JSON.stringify({ message }),
    });
    
    if (!response.body) {
      throw new Error('No response body');
    }
    
    return response.body;
  }

  async generateContentPlan(params: any): Promise<any> {
    const response = await this.post('/planner/generate', params);
    return response.data;
  }
}

export const apiClient = new ApiClient();