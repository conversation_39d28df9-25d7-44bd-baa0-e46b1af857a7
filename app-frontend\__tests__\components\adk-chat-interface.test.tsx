/**
 * Integration tests for ADK Chat Interface
 * 
 * Requirements covered: 2.2, 2.4, 5.3
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ADKChatInterface } from '@/components/chat/adk-chat-interface';
import { EnhancedChatMessage, ChatError } from '@/types/adk';

// Mock the hooks
jest.mock('@/hooks/use-adk-chat', () => ({
  useADKChat: jest.fn()
}));

import { useADKChat } from '@/hooks/use-adk-chat';

const mockUseADKChat = useADKChat as jest.MockedFunction<typeof useADKChat>;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('ADKChatInterface', () => {
  let mockChatHook: {
    messages: EnhancedChatMessage[];
    isLoading: boolean;
    isStreaming: boolean;
    isReconnecting: boolean;
    error: ChatError | null;
    currentAgent: string;
    streamingMessage: Partial<EnhancedChatMessage> | null;
    sendMessage: jest.Mock;
    interruptStreaming: jest.Mock;
    clearError: jest.Mock;
    retryLastMessage: jest.Mock;
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('test-token');

    mockChatHook = {
      messages: [],
      isLoading: false,
      isStreaming: false,
      isReconnecting: false,
      error: null,
      currentAgent: 'test-agent',
      streamingMessage: null,
      sendMessage: jest.fn(),
      interruptStreaming: jest.Mock,
      clearError: jest.fn(),
      retryLastMessage: jest.fn()
    };

    mockUseADKChat.mockReturnValue(mockChatHook);
  });

  const defaultProps = {
    userId: 'test-user',
    sessionId: 'test-session',
    agentName: 'test-agent'
  };

  it('should render welcome message when no messages', () => {
    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText('Welcome to ADK Chat')).toBeInTheDocument();
    expect(screen.getByText(/I'm your AI assistant powered by Google's Agent Development Kit/)).toBeInTheDocument();
  });

  it('should render agent info header', () => {
    render(
      <ADKChatInterface {...defaultProps} showAgentInfo={true} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText('test-agent')).toBeInTheDocument();
    expect(screen.getByText('Real-time streaming enabled')).toBeInTheDocument();
  });

  it('should render messages correctly', () => {
    const messages: EnhancedChatMessage[] = [
      {
        id: '1',
        session_id: 'test-session',
        role: 'user',
        content: 'Hello',
        user_id: 'test-user',
        timestamp: new Date().toISOString(),
        agent_name: 'test-agent'
      },
      {
        id: '2',
        session_id: 'test-session',
        role: 'model',
        content: 'Hello back!',
        user_id: 'test-user',
        timestamp: new Date().toISOString(),
        agent_name: 'test-agent'
      }
    ];

    mockChatHook.messages = messages;
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText('Hello')).toBeInTheDocument();
    expect(screen.getByText('Hello back!')).toBeInTheDocument();
  });

  it('should handle message input and send', async () => {
    mockChatHook.sendMessage.mockResolvedValue(undefined);
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    const input = screen.getByPlaceholderText('Ask test-agent anything...');
    const sendButton = screen.getByRole('button', { name: /send/i });

    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(mockChatHook.sendMessage).toHaveBeenCalledWith('Test message');
    });
  });

  it('should handle Enter key to send message', async () => {
    mockChatHook.sendMessage.mockResolvedValue(undefined);
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    const input = screen.getByPlaceholderText('Ask test-agent anything...');

    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.keyDown(input, { key: 'Enter', shiftKey: false });

    await waitFor(() => {
      expect(mockChatHook.sendMessage).toHaveBeenCalledWith('Test message');
    });
  });

  it('should not send empty messages', () => {
    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.click(sendButton);

    expect(mockChatHook.sendMessage).not.toHaveBeenCalled();
  });

  it('should disable input during streaming', () => {
    mockChatHook.isStreaming = true;
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    const input = screen.getByPlaceholderText('Streaming in progress...');
    const sendButton = screen.getByRole('button', { name: /send/i });

    expect(input).toBeDisabled();
    expect(sendButton).toBeDisabled();
  });

  it('should show streaming indicator when streaming', () => {
    mockChatHook.isStreaming = true;
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText(/test-agent is thinking/)).toBeInTheDocument();
  });

  it('should show streaming message', () => {
    const streamingMessage: Partial<EnhancedChatMessage> = {
      id: 'streaming-1',
      content: 'Partial response...',
      role: 'model',
      agent_name: 'test-agent'
    };

    mockChatHook.streamingMessage = streamingMessage;
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText('Partial response...')).toBeInTheDocument();
  });

  it('should show connection status when disconnected', () => {
    const error: ChatError = {
      error: 'Connection lost',
      error_code: 'STREAM_ERROR'
    };

    mockChatHook.error = error;
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} showConnectionStatus={true} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText('Connection lost')).toBeInTheDocument();
  });

  it('should show error display with retry option', () => {
    const error: ChatError = {
      error: 'Something went wrong',
      error_code: 'GENERAL_ERROR'
    };

    mockChatHook.error = error;
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText('Error: Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('Code: GENERAL_ERROR')).toBeInTheDocument();
    
    const retryButton = screen.getByRole('button', { name: /retry/i });
    fireEvent.click(retryButton);
    
    expect(mockChatHook.retryLastMessage).toHaveBeenCalled();
  });

  it('should handle error dismissal', () => {
    const error: ChatError = {
      error: 'Something went wrong',
      error_code: 'GENERAL_ERROR'
    };

    mockChatHook.error = error;
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    const dismissButton = screen.getByRole('button', { name: /dismiss/i });
    fireEvent.click(dismissButton);
    
    expect(mockChatHook.clearError).toHaveBeenCalled();
  });

  it('should show status bar with session info', () => {
    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByText('Agent: test-agent')).toBeInTheDocument();
    expect(screen.getByText('Messages: 0')).toBeInTheDocument();
    expect(screen.getByText('Streaming enabled')).toBeInTheDocument();
    expect(screen.getByText(/Session: test-ses/)).toBeInTheDocument();
  });

  it('should handle quick action buttons', () => {
    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    const contentPlanButton = screen.getByText('Content Planning');
    fireEvent.click(contentPlanButton);

    const input = screen.getByDisplayValue('Help me create a content plan');
    expect(input).toBeInTheDocument();
  });

  it('should call onMessageSent callback', async () => {
    const onMessageSent = jest.fn();
    mockChatHook.sendMessage.mockResolvedValue(undefined);
    mockUseADKChat.mockReturnValue(mockChatHook);

    render(
      <ADKChatInterface {...defaultProps} onMessageSent={onMessageSent} />,
      { wrapper: createWrapper() }
    );

    const input = screen.getByPlaceholderText('Ask test-agent anything...');
    const sendButton = screen.getByRole('button', { name: /send/i });

    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(onMessageSent).toHaveBeenCalledWith(
        expect.objectContaining({
          content: 'Test message',
          role: 'user'
        })
      );
    });
  });

  it('should adjust textarea height on input', () => {
    render(
      <ADKChatInterface {...defaultProps} />,
      { wrapper: createWrapper() }
    );

    const textarea = screen.getByPlaceholderText('Ask test-agent anything...');
    
    // Mock scrollHeight
    Object.defineProperty(textarea, 'scrollHeight', {
      value: 100,
      writable: true
    });

    fireEvent.change(textarea, { target: { value: 'Multi\nline\nmessage' } });

    // The height should be adjusted (mocked behavior)
    expect(textarea.style.height).toBe('100px');
  });
});