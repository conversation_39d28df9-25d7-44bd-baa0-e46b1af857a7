"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Youtube, Instagram, Twitter, TrendingUp } from "lucide-react";

interface ConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ConnectionModal({ isOpen, onClose }: ConnectionModalProps) {
  const [connecting, setConnecting] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleConnect = async (platform: string) => {
    setConnecting(platform);
    // TODO: Implement actual OAuth flow
    setTimeout(() => {
      setConnecting(null);
      onClose();
    }, 2000);
  };

  const platforms = [
    {
      id: 'youtube',
      name: 'YouTube',
      icon: Youtube,
      color: 'text-red-500',
      available: true,
      description: 'Connect your YouTube channel to analyze video performance and audience engagement.',
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: Instagram,
      color: 'text-pink-500',
      available: true,
      description: 'Link your Instagram Business account to track posts, stories, and follower growth.',
    },
    {
      id: 'twitter',
      name: 'X (Twitter)',
      icon: Twitter,
      color: 'text-blue-500',
      available: false,
      description: 'Coming soon - Monitor tweets, engagement, and follower analytics.',
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      icon: TrendingUp,
      color: 'text-black',
      available: false,
      description: 'Coming soon - Track viral content and audience demographics.',
    },
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Connect Your Accounts</h2>
              <p className="text-sm text-muted-foreground mt-1">
                Link your social media accounts to get personalized insights and analytics
              </p>
            </div>
            <Button variant="ghost" onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>
        
        <div className="p-6 space-y-4">
          {platforms.map((platform) => {
            const Icon = platform.icon;
            const isConnecting = connecting === platform.id;
            
            return (
              <Card key={platform.id} className={!platform.available ? 'opacity-60' : ''}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 rounded-lg bg-muted flex items-center justify-center`}>
                        <Icon className={`w-6 h-6 ${platform.color}`} />
                      </div>
                      <div>
                        <h3 className="font-medium">{platform.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {platform.description}
                        </p>
                      </div>
                    </div>
                    
                    <Button
                      onClick={() => handleConnect(platform.id)}
                      disabled={!platform.available || isConnecting}
                      variant={platform.available ? "default" : "secondary"}
                    >
                      {isConnecting ? (
                        "Connecting..."
                      ) : platform.available ? (
                        "Connect"
                      ) : (
                        "Coming Soon"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        <div className="p-6 border-t bg-muted/30">
          <p className="text-xs text-muted-foreground">
            We use secure OAuth 2.0 authentication. Your account credentials are never stored on our servers.
          </p>
        </div>
      </div>
    </div>
  );
}