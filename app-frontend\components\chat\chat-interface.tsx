"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Send, Bot, User, Zap } from "lucide-react";
import { useChatHistory } from "@/hooks/use-chat-history";
import { useSendMessage } from "@/hooks/use-send-message";
import { ADKChatInterface } from "./adk-chat-interface";
import { EnhancedChatMessage, ChatError } from "@/types/adk";

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: {
    platforms?: string[];
    insights?: any[];
  };
}

export function ChatInterface() {
  const [input, setInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [useADK, setUseADK] = useState(true); // Feature flag for ADK integration
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { messages = [], isLoading } = useChatHistory({
    userId: typeof window !== 'undefined' ? localStorage.getItem('user_id') || 'dev-user' : 'dev-user',
    agentName: 'content_planner'
  });
  const sendMessageMutation = useSendMessage();

  // Get user ID from localStorage or use default for development
  const userId = typeof window !== 'undefined' 
    ? localStorage.getItem('user_id') || 'dev-user' 
    : 'dev-user';
  
  const sessionId = typeof window !== 'undefined' 
    ? localStorage.getItem('chat_session_id') || undefined 
    : undefined;

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const handleSend = async () => {
    if (!input.trim() || sendMessageMutation.isPending) return;

    const messageText = input.trim();
    setInput("");
    setIsTyping(true);

    try {
      await sendMessageMutation.mutateAsync(messageText);
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleADKMessageSent = (message: EnhancedChatMessage) => {
    console.log('ADK message sent:', message);
  };

  const handleADKMessageReceived = (message: EnhancedChatMessage) => {
    console.log('ADK message received:', message);
  };

  const handleADKError = (error: ChatError) => {
    console.error('ADK error:', error);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Use ADK interface if enabled
  if (useADK) {
    return (
      <div className="space-y-4">
        {/* ADK Toggle */}
        <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium">ADK Enhanced Chat</span>
            <Badge variant="secondary" className="h-5 px-2 text-xs">
              Real-time streaming
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setUseADK(false)}
            className="h-7 px-2 text-xs"
          >
            Use Legacy Chat
          </Button>
        </div>

        <ADKChatInterface
          userId={userId}
          sessionId={sessionId}
          agentName="content_planner"
          enableStreaming={true}
          showAgentInfo={true}
          showConnectionStatus={true}
          onMessageSent={handleADKMessageSent}
          onMessageReceived={handleADKMessageReceived}
          onError={handleADKError}
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-12rem)]">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto space-y-4 p-4 bg-muted/20 rounded-lg">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              <Bot className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Welcome to your Social Media Manager</h3>
              <p className="text-muted-foreground max-w-md">
                I'm here to help you analyze your social media performance, create content plans, 
                and grow your online presence. Start by asking me about your accounts or content strategy.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 max-w-md">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setInput("Analyze my YouTube performance")}
              >
                Analyze YouTube
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setInput("How's my Instagram engagement?")}
              >
                Check Instagram
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setInput("Create a content plan for next week")}
              >
                Content Plan
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setUseADK(true)}
                className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
              >
                <Zap className="w-3 h-3 mr-1" />
                Try ADK Chat
              </Button>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role === 'assistant' && (
                  <Avatar className="w-8 h-8 mt-1">
                    <AvatarFallback>
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <Card className={`max-w-[80%] ${
                  message.role === 'user' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-card'
                }`}>
                  <CardContent className="p-3">
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    <p className="text-xs opacity-70 mt-2">
                      {message.timestamp.toLocaleTimeString()}
                    </p>
                  </CardContent>
                </Card>

                {message.role === 'user' && (
                  <Avatar className="w-8 h-8 mt-1">
                    <AvatarFallback>
                      <User className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            
            {isTyping && (
              <div className="flex gap-3 justify-start">
                <Avatar className="w-8 h-8 mt-1">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <Card className="bg-card">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-1">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                      <span className="text-xs text-muted-foreground ml-2">Analyzing...</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="flex gap-2 p-4 bg-background border-t">
        <div className="flex-1 flex gap-2">
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask me about your social media performance, content ideas, or strategy..."
            className="flex-1 min-h-[2.5rem] max-h-32 p-3 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground resize-none focus:outline-none focus:ring-2 focus:ring-ring"
            rows={1}
            style={{
              height: 'auto',
              minHeight: '2.5rem',
            }}
            onInput={(e) => {
              const target = e.target as HTMLTextAreaElement;
              target.style.height = 'auto';
              target.style.height = Math.min(target.scrollHeight, 128) + 'px';
            }}
          />
          <Button
            onClick={handleSend}
            disabled={!input.trim() || sendMessageMutation.isPending}
            size="icon"
            className="self-end"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}