{"projects": {"default": "your-project-id"}, "targets": {}, "etags": {}, "dataconnectEmulatorConfig": {}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "app-frontend/out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/**", "run": {"serviceId": "social-media-backend", "region": "us-central1"}}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "/api/**", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}]}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}], "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}