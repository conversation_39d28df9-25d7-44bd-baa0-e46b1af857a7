"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Heart, 
  Eye, 
  MoreHorizontal,
  Youtube,
  Instagram,
  Twitter,
  Sync
} from "lucide-react";
import { AccountData } from "@/hooks/use-connected-accounts";

interface PlatformTileProps {
  account: AccountData;
  timeframe: string;
}

export function PlatformTile({ account, timeframe }: PlatformTileProps) {
  const platformIcons = {
    youtube: Youtube,
    instagram: Instagram,
    twitter: Twitter,
  };

  const platformColors = {
    youtube: "text-red-500 bg-red-50 border-red-200",
    instagram: "text-pink-500 bg-pink-50 border-pink-200",
    twitter: "text-blue-500 bg-blue-50 border-blue-200",
  };

  const Icon = platformIcons[account.platform as keyof typeof platformIcons];
  const colorClasses = platformColors[account.platform as keyof typeof platformColors];

  // Mock data for demonstration - replace with real metrics
  const mockMetrics = {
    youtube: {
      views: 125000,
      watchTime: 2450,
      subscribers: account.metrics?.followers || 15420,
      engagement: account.metrics?.engagement || 4.2,
      growth: account.metrics?.growth?.followers || 7.8,
      posts: 12,
      avgViews: 10400
    },
    instagram: {
      impressions: 89000,
      reach: 67000,
      followers: account.metrics?.followers || 8750,
      engagement: account.metrics?.engagement || 3.1,
      growth: account.metrics?.growth?.followers || 5.2,
      posts: 18,
      avgLikes: 425
    },
    twitter: {
      impressions: 45000,
      engagements: 1200,
      followers: account.metrics?.followers || 3400,
      engagement: account.metrics?.engagement || 2.7,
      growth: account.metrics?.growth?.followers || 2.1,
      posts: 24,
      avgRetweets: 15
    }
  };

  const metrics = mockMetrics[account.platform as keyof typeof mockMetrics] || mockMetrics.youtube;

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getGrowthColor = (growth: number): string => {
    if (growth > 5) return 'text-green-600';
    if (growth > 0) return 'text-green-500';
    if (growth === 0) return 'text-gray-500';
    return 'text-red-500';
  };

  const getMetricLabel = (): string => {
    switch (account.platform) {
      case 'youtube': return 'Subscribers';
      case 'instagram': return 'Followers';
      case 'twitter': return 'Followers';
      default: return 'Followers';
    }
  };

  const getSecondaryMetric = () => {
    switch (account.platform) {
      case 'youtube': 
        return { label: 'Views', value: formatNumber(metrics.views), icon: Eye };
      case 'instagram': 
        return { label: 'Reach', value: formatNumber(metrics.reach || metrics.impressions), icon: Eye };
      case 'twitter': 
        return { label: 'Impressions', value: formatNumber(metrics.impressions), icon: Eye };
      default: 
        return { label: 'Views', value: formatNumber(metrics.views || 0), icon: Eye };
    }
  };

  const secondaryMetric = getSecondaryMetric();
  const SecondaryIcon = secondaryMetric.icon;

  return (
    <Card className={`${colorClasses} transition-all hover:shadow-md`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="w-12 h-12">
              <AvatarImage src={account.avatar} alt={account.handle} />
              <AvatarFallback className={colorClasses}>
                {Icon && <Icon className="w-6 h-6" />}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{account.handle}</CardTitle>
              <Badge variant="secondary" className="text-xs capitalize">
                {account.platform}
              </Badge>
            </div>
          </div>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Main Metrics Row */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{getMetricLabel()}</span>
            </div>
            <p className="text-2xl font-bold">{formatNumber(metrics.followers)}</p>
            <div className="flex items-center gap-1">
              {metrics.growth >= 0 ? (
                <TrendingUp className="w-3 h-3 text-green-500" />
              ) : (
                <TrendingDown className="w-3 h-3 text-red-500" />
              )}
              <span className={`text-xs ${getGrowthColor(metrics.growth)}`}>
                {metrics.growth >= 0 ? '+' : ''}{metrics.growth}%
              </span>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <SecondaryIcon className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{secondaryMetric.label}</span>
            </div>
            <p className="text-2xl font-bold">{secondaryMetric.value}</p>
            <div className="flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-500" />
              <span className="text-xs text-green-500">+12%</span>
            </div>
          </div>
        </div>

        {/* Engagement Rate */}
        <div className="bg-white/50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Heart className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Engagement Rate</span>
            </div>
            <span className="text-sm font-medium">{metrics.engagement}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-current h-2 rounded-full transition-all" 
              style={{ width: `${Math.min(metrics.engagement * 10, 100)}%` }}
            />
          </div>
        </div>

        {/* Additional Metrics */}
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="bg-white/50 rounded-lg p-2">
            <p className="text-muted-foreground">Posts ({timeframe})</p>
            <p className="font-semibold">{metrics.posts}</p>
          </div>
          <div className="bg-white/50 rounded-lg p-2">
            <p className="text-muted-foreground">
              {account.platform === 'youtube' ? 'Avg Views' : 
               account.platform === 'instagram' ? 'Avg Likes' : 'Avg Retweets'}
            </p>
            <p className="font-semibold">
              {account.platform === 'youtube' ? formatNumber(metrics.avgViews || 0) :
               account.platform === 'instagram' ? formatNumber(metrics.avgLikes || 0) :
               formatNumber(metrics.avgRetweets || 0)}
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button variant="outline" size="sm" className="flex-1">
            <Sync className="w-4 h-4 mr-2" />
            Sync Data
          </Button>
          <Button size="sm" className="flex-1">
            View Details
          </Button>
        </div>

        {/* Last Sync */}
        <p className="text-xs text-muted-foreground text-center">
          Last updated: {new Date(account.lastSync).toLocaleDateString()}
        </p>
      </CardContent>
    </Card>
  );
}