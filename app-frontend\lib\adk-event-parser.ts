/**
 * ADK Event Parser Utilities
 * 
 * Utilities for parsing ADK Event objects and extracting function calls,
 * function responses, and tool execution information.
 * 
 * Requirements covered: 10.1, 10.2, 4.5
 */

import { 
  ADKEvent, 
  ADKContentPart, 
  FunctionCall, 
  FunctionResponse, 
  ToolExecution,
  EnhancedChatMessage 
} from '@/types/adk';

/**
 * Extract function calls from ADK Event content parts
 */
export function extractFunctionCalls(event: ADKEvent): FunctionCall[] {
  if (!event.content?.parts) return [];

  const functionCalls: FunctionCall[] = [];

  event.content.parts.forEach((part: ADKContentPart, index: number) => {
    if (part.function_call) {
      functionCalls.push({
        id: part.function_call.id || `call_${index}`,
        name: part.function_call.name || 'unknown_function',
        arguments: part.function_call.arguments || {}
      });
    }
  });

  return functionCalls;
}

/**
 * Extract function responses from ADK Event content parts
 */
export function extractFunctionResponses(event: ADKEvent): FunctionResponse[] {
  if (!event.content?.parts) return [];

  const functionResponses: FunctionResponse[] = [];

  event.content.parts.forEach((part: ADKContentPart, index: number) => {
    if (part.function_response) {
      functionResponses.push({
        id: part.function_response.id || `response_${index}`,
        name: part.function_response.name || 'unknown_function',
        content: part.function_response.content || '',
        success: !part.function_response.error,
        error: part.function_response.error
      });
    }
  });

  return functionResponses;
}

/**
 * Extract text content from ADK Event content parts
 */
export function extractTextContent(event: ADKEvent): string {
  if (!event.content?.parts) return '';

  return event.content.parts
    .filter((part: ADKContentPart) => part.text)
    .map((part: ADKContentPart) => part.text)
    .join('');
}

/**
 * Extract binary data from ADK Event content parts
 */
export function extractBinaryData(event: ADKEvent): Record<string, any>[] {
  if (!event.content?.parts) return [];

  return event.content.parts
    .filter((part: ADKContentPart) => part.inline_data)
    .map((part: ADKContentPart) => part.inline_data!)
    .filter(Boolean);
}

/**
 * Create tool execution objects from function calls and responses
 */
export function createToolExecutions(
  functionCalls: FunctionCall[],
  functionResponses: FunctionResponse[],
  event: ADKEvent
): ToolExecution[] {
  const executions: ToolExecution[] = [];

  functionCalls.forEach((call) => {
    const response = functionResponses.find(
      (resp) => resp.id === call.id || resp.name === call.name
    );

    const execution: ToolExecution = {
      id: call.id || `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: call.name,
      status: response 
        ? (response.success ? 'completed' : 'failed')
        : event.long_running_tool_ids?.includes(call.id || call.name)
          ? 'running'
          : 'pending',
      input: call.arguments,
      output: response?.content,
      error: response?.error,
      duration_ms: undefined // This would need to be calculated from timing data
    };

    executions.push(execution);
  });

  return executions;
}

/**
 * Transform ADK Event to EnhancedChatMessage
 */
export function transformEventToMessage(
  event: ADKEvent,
  sessionId: string,
  userId: string,
  agentName?: string
): EnhancedChatMessage {
  const functionCalls = extractFunctionCalls(event);
  const functionResponses = extractFunctionResponses(event);
  const toolExecutions = createToolExecutions(functionCalls, functionResponses, event);
  const textContent = extractTextContent(event);
  const binaryData = extractBinaryData(event);

  return {
    id: event.invocation_id || `msg_${Date.now()}`,
    session_id: sessionId,
    role: event.content?.role || 'model',
    content: textContent,
    user_id: userId,
    timestamp: new Date().toISOString(),
    agent_name: agentName || event.author,
    adk_invocation_id: event.invocation_id,
    function_calls: functionCalls.length > 0 ? functionCalls : undefined,
    interrupted: event.interrupted,
    metadata: {
      function_responses: functionResponses.length > 0 ? functionResponses : undefined,
      tool_executions: toolExecutions.length > 0 ? toolExecutions : undefined,
      binary_data: binaryData.length > 0 ? binaryData : undefined,
      long_running_tool_ids: event.long_running_tool_ids,
      turn_complete: event.turn_complete,
      raw_event: event
    }
  };
}

/**
 * Check if an event contains function calls
 */
export function hasToolUsage(event: ADKEvent): boolean {
  if (!event.content?.parts) return false;

  return event.content.parts.some(
    (part: ADKContentPart) => part.function_call || part.function_response
  );
}

/**
 * Get tool usage summary from an event
 */
export function getToolUsageSummary(event: ADKEvent): {
  functionCallCount: number;
  functionResponseCount: number;
  longRunningToolCount: number;
  hasErrors: boolean;
} {
  const functionCalls = extractFunctionCalls(event);
  const functionResponses = extractFunctionResponses(event);

  return {
    functionCallCount: functionCalls.length,
    functionResponseCount: functionResponses.length,
    longRunningToolCount: event.long_running_tool_ids?.length || 0,
    hasErrors: functionResponses.some(response => !response.success)
  };
}

/**
 * Parse streaming chunk data from SSE event
 */
export function parseStreamingChunk(eventData: string): ADKEvent | null {
  try {
    // Remove "data: " prefix if present
    const cleanData = eventData.startsWith('data: ') 
      ? eventData.substring(6) 
      : eventData;

    // Skip empty lines or non-JSON data
    if (!cleanData.trim() || cleanData.trim() === '[DONE]') {
      return null;
    }

    const parsed = JSON.parse(cleanData);
    
    // Validate that it's an ADK Event
    if (typeof parsed === 'object' && 
        typeof parsed.interrupted === 'boolean' && 
        typeof parsed.turn_complete === 'boolean') {
      return parsed as ADKEvent;
    }

    return null;
  } catch (error) {
    console.warn('Failed to parse streaming chunk:', error);
    return null;
  }
}

/**
 * Merge multiple events into a single message (for streaming)
 */
export function mergeStreamingEvents(
  events: ADKEvent[],
  sessionId: string,
  userId: string,
  agentName?: string
): EnhancedChatMessage {
  if (events.length === 0) {
    throw new Error('Cannot merge empty events array');
  }

  // Use the last event as the base (most complete)
  const lastEvent = events[events.length - 1];
  
  // Combine text content from all events
  const combinedContent = events
    .map(event => extractTextContent(event))
    .join('');

  // Collect all function calls and responses
  const allFunctionCalls = events.flatMap(event => extractFunctionCalls(event));
  const allFunctionResponses = events.flatMap(event => extractFunctionResponses(event));
  const allBinaryData = events.flatMap(event => extractBinaryData(event));

  // Create tool executions from combined data
  const toolExecutions = createToolExecutions(allFunctionCalls, allFunctionResponses, lastEvent);

  return {
    id: lastEvent.invocation_id || `msg_${Date.now()}`,
    session_id: sessionId,
    role: lastEvent.content?.role || 'model',
    content: combinedContent,
    user_id: userId,
    timestamp: new Date().toISOString(),
    agent_name: agentName || lastEvent.author,
    adk_invocation_id: lastEvent.invocation_id,
    function_calls: allFunctionCalls.length > 0 ? allFunctionCalls : undefined,
    interrupted: lastEvent.interrupted,
    metadata: {
      function_responses: allFunctionResponses.length > 0 ? allFunctionResponses : undefined,
      tool_executions: toolExecutions.length > 0 ? toolExecutions : undefined,
      binary_data: allBinaryData.length > 0 ? allBinaryData : undefined,
      long_running_tool_ids: lastEvent.long_running_tool_ids,
      turn_complete: lastEvent.turn_complete,
      event_count: events.length,
      raw_events: events
    }
  };
}

/**
 * Validate ADK Event structure
 */
export function validateADKEvent(obj: any): obj is ADKEvent {
  return (
    obj !== null &&
    obj !== undefined &&
    typeof obj === 'object' &&
    typeof obj.interrupted === 'boolean' &&
    typeof obj.turn_complete === 'boolean' &&
    (!obj.content || (
      typeof obj.content === 'object' &&
      Array.isArray(obj.content.parts)
    ))
  );
}

/**
 * Extract processing time from event metadata
 */
export function extractProcessingTime(event: ADKEvent): number | undefined {
  // This would depend on how ADK provides timing information
  // For now, return undefined as timing data structure is not specified
  return event.metadata?.processing_time_ms || 
         event.metadata?.duration_ms ||
         undefined;
}