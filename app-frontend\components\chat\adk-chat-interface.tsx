"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Send, Bo<PERSON>, Settings, RefreshCw, History } from "lucide-react";
import { useADKChat } from "@/hooks/use-adk-chat";
import { useAgentList } from "@/hooks/use-agent-list";
import { useAgentSelection } from "@/hooks/use-agent-selection";
import { EnhancedMessage } from "./enhanced-message";
import { StreamingIndicator, TypingIndicator, ConnectionStatus } from "./streaming-indicator";
import { SessionManager } from "./session-manager";
import { AgentSelector } from "./agent-selector";
import { EnhancedChatMessage, ChatError } from "@/types/adk";
import { SessionStorageData } from "@/hooks/use-session-persistence";
import { cn } from "@/lib/utils";

export interface ADKChatInterfaceProps {
  userId: string;
  sessionId?: string;
  agentName?: string;
  enableStreaming?: boolean;
  enableSessionPersistence?: boolean;
  enableHistoryRecovery?: boolean;
  showAgentInfo?: boolean;
  showConnectionStatus?: boolean;
  showSessionManager?: boolean;
  showAgentSelector?: boolean;
  onMessageSent?: (message: EnhancedChatMessage) => void;
  onMessageReceived?: (message: EnhancedChatMessage) => void;
  onError?: (error: ChatError) => void;
  onSessionRecovered?: (sessionId: string) => void;
  className?: string;
}

export function ADKChatInterface({
  userId,
  sessionId,
  agentName = 'content_planner',
  enableStreaming = true,
  enableSessionPersistence = true,
  enableHistoryRecovery = true,
  showAgentInfo = true,
  showConnectionStatus = true,
  showSessionManager = true,
  showAgentSelector = true,
  onMessageSent,
  onMessageReceived,
  onError,
  onSessionRecovered,
  className
}: ADKChatInterfaceProps) {
  const [input, setInput] = useState("");
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Agent list hook
  const agentList = useAgentList({
    autoRefresh: true,
    refreshInterval: 60000, // 1 minute
    onError: onError
  });

  // Agent selection hook
  const agentSelection = useAgentSelection({
    userId,
    currentAgent: agentName,
    currentSessionId: sessionId,
    preserveHistory: false,
    onAgentChange: (newAgentName, newSessionId) => {
      // This would trigger a re-render with new agent
      console.log('Agent changed:', newAgentName, newSessionId);
      // In a real implementation, you might need to update the parent component
      // or use a state management solution
    },
    onError: onError
  });

  const {
    messages,
    isLoading,
    isStreaming,
    isReconnecting,
    isRecoveringSession,
    error,
    currentAgent,
    sessionId: currentSessionId,
    streamingMessage,
    sendMessage,
    interruptStreaming,
    clearError,
    retryLastMessage,
    clearSession,
    refreshHistory
  } = useADKChat({
    userId,
    sessionId,
    agentName: agentSelection.selectedAgent,
    enableStreaming,
    enableSessionPersistence,
    enableHistoryRecovery,
    onMessageReceived,
    onError,
    onSessionRecovered
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, streamingMessage, isStreaming]);

  const handleSend = async () => {
    if (!input.trim() || isLoading || isStreaming) return;

    const messageText = input.trim();
    setInput("");
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    try {
      await sendMessage(messageText);
      
      // Create user message for callback
      const userMessage: EnhancedChatMessage = {
        id: `user_${Date.now()}`,
        session_id: sessionId || '',
        role: 'user',
        content: messageText,
        user_id: userId,
        timestamp: new Date().toISOString(),
        agent_name: currentAgent
      };
      
      onMessageSent?.(userMessage);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleRetryMessage = async () => {
    try {
      await retryLastMessage();
    } catch (error) {
      console.error('Failed to retry message:', error);
    }
  };

  const handleDeleteMessage = (messageId: string) => {
    // In a real implementation, this would call an API to delete the message
    console.log('Delete message:', messageId);
  };

  const handleSessionSelect = useCallback((sessionData: SessionStorageData) => {
    // This would trigger a session switch
    console.log('Session selected:', sessionData);
    // In a real implementation, you might need to reload the component with the new session
    window.location.reload(); // Simple approach for now
  }, []);

  const handleSessionClear = useCallback(() => {
    clearSession();
  }, [clearSession]);

  const adjustTextareaHeight = (textarea: HTMLTextAreaElement) => {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 128) + 'px';
  };

  const isConnected = !error || error.error_code !== 'STREAM_ERROR';

  return (
    <div className={cn("flex flex-col h-[calc(100vh-12rem)]", className)}>
      {/* Connection Status */}
      {showConnectionStatus && (
        <ConnectionStatus
          isConnected={isConnected}
          isReconnecting={isReconnecting}
          lastError={error}
          onReconnect={clearError}
        />
      )}

      {/* Agent Info Header */}
      {showAgentInfo && (
        <div className="flex items-center justify-between p-4 border-b bg-muted/20">
          <div className="flex items-center gap-3">
            {showAgentSelector ? (
              <AgentSelector
                availableAgents={agentList.agents}
                selectedAgent={agentSelection.selectedAgent}
                onAgentSelect={agentSelection.selectAgent}
                isLoading={agentList.isLoading}
                isChangingAgent={agentSelection.isChangingAgent}
                error={agentList.error || agentSelection.error}
                onRefresh={agentList.refresh}
                showStatus={true}
                showCapabilities={true}
                className="min-w-[200px]"
              />
            ) : (
              <>
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-primary/10 text-primary">
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium text-sm">{currentAgent}</div>
                  <div className="text-xs text-muted-foreground">
                    {enableStreaming ? 'Real-time streaming enabled' : 'Standard responses'}
                  </div>
                </div>
              </>
            )}
            
            {isStreaming && (
              <Badge variant="secondary" className="h-5 px-2 text-xs">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-1" />
                Active
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {showSessionManager && (
              <SessionManager
                userId={userId}
                currentAgentName={agentSelection.selectedAgent}
                currentSessionId={currentSessionId || undefined}
                onSessionSelect={handleSessionSelect}
                onSessionClear={handleSessionClear}
              />
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshHistory}
              className="h-8 w-8 p-0"
              title="Refresh chat history"
            >
              <History className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="h-8 w-8 p-0"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto space-y-4 p-4 bg-muted/10">
        {/* Session Recovery Indicator */}
        {isRecoveringSession && (
          <div className="flex items-center justify-center p-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <RefreshCw className="w-4 h-4 animate-spin" />
              Recovering session...
            </div>
          </div>
        )}

        {messages.length === 0 && !isRecoveringSession ? (
          <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              <Bot className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Welcome to ADK Chat</h3>
              <p className="text-muted-foreground max-w-md">
                I'm your AI assistant powered by Google's Agent Development Kit. 
                I can help with content planning, research, and much more with real-time streaming responses.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 max-w-md">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setInput("Help me create a content plan")}
              >
                Content Planning
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setInput("Research trending topics")}
              >
                Research Topics
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setInput("Analyze my social media performance")}
              >
                Analytics
              </Button>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <EnhancedMessage
                key={message.id}
                message={message}
                isStreaming={false}
                showTimestamp={true}
                showAgent={true}
                showFunctionCalls={true}
                onRetry={handleRetryMessage}
                onDelete={() => handleDeleteMessage(message.id)}
              />
            ))}
            
            {/* Streaming Message */}
            {streamingMessage && (
              <EnhancedMessage
                message={streamingMessage as EnhancedChatMessage}
                isStreaming={true}
                showTimestamp={true}
                showAgent={true}
                showFunctionCalls={true}
              />
            )}

            {/* Streaming Indicator */}
            <StreamingIndicator
              isStreaming={isStreaming}
              isConnected={isConnected}
              isReconnecting={isReconnecting}
              agentName={currentAgent}
              canInterrupt={true}
              onInterrupt={interruptStreaming}
              onReconnect={clearError}
            />

            {/* Typing Indicator for Loading */}
            <TypingIndicator
              show={isLoading && !isStreaming}
              agentName={currentAgent}
              message="is preparing response"
            />
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border-t border-red-200 dark:bg-red-950 dark:border-red-800">
          <div className="flex items-center justify-between">
            <div className="text-sm text-red-800 dark:text-red-200">
              <strong>Error:</strong> {error.error}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={retryLastMessage}
                className="h-7 px-2 text-xs"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Retry
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="h-7 px-2 text-xs"
              >
                Dismiss
              </Button>
            </div>
          </div>
          {error.error_code && (
            <div className="text-xs text-red-600 dark:text-red-400 mt-1">
              Code: {error.error_code}
            </div>
          )}
        </div>
      )}

      {/* Input Area */}
      <div className="flex gap-2 p-4 bg-background border-t">
        <div className="flex-1 flex gap-2">
          <textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => {
              setInput(e.target.value);
              adjustTextareaHeight(e.target);
            }}
            onKeyDown={handleKeyDown}
            placeholder={
              isStreaming 
                ? "Streaming in progress..." 
                : `Ask ${agentSelection.selectedAgent} anything...`
            }
            className="flex-1 min-h-[2.5rem] max-h-32 p-3 border rounded-lg bg-background text-foreground placeholder:text-muted-foreground resize-none focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50"
            rows={1}
            disabled={isLoading || isStreaming}
          />
          <Button
            onClick={handleSend}
            disabled={!input.trim() || isLoading || isStreaming}
            size="icon"
            className="self-end"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Status Bar */}
      <div className="px-4 py-2 bg-muted/30 border-t text-xs text-muted-foreground">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span>Agent: {agentSelection.selectedAgent}</span>
            <span>Messages: {messages.length}</span>
            {agentList.agents.length > 0 && (
              <span>Available: {agentList.agents.filter(a => a.available).length}/{agentList.agents.length}</span>
            )}
            {enableStreaming && (
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                Streaming enabled
              </span>
            )}
            {enableSessionPersistence && (
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                Persistence enabled
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {currentSessionId && (
              <span>Session: {currentSessionId.slice(-8)}</span>
            )}
            {(agentList.isLoading || agentSelection.isChangingAgent) && (
              <span className="flex items-center gap-1">
                <RefreshCw className="w-3 h-3 animate-spin" />
                {agentSelection.isChangingAgent ? 'Switching agent...' : 'Loading...'}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}