name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: gcr.io
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

jobs:
  # Frontend Tests and Build
  frontend-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./app-frontend
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './app-frontend/package-lock.json'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run type checking
      run: npm run type-check
    
    - name: Run linting
      run: npm run lint
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_API_URL: ${{ secrets.API_URL }}

  # Backend Tests and Build
  backend-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./app-agents
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: './app-agents/requirements.txt'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run linting
      run: |
        pip install flake8 black
        flake8 app/ --max-line-length=100
        black --check app/
    
    - name: Run tests
      run: pytest tests/ --cov=app --cov-report=xml
      env:
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        SECRET_KEY: test-secret-key
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./app-agents/coverage.xml
        flags: backend

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    needs: [frontend-test, backend-test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Push Docker Images
  build-images:
    runs-on: ubuntu-latest
    needs: [frontend-test, backend-test, security-scan]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    
    - name: Configure Docker to use gcloud as a credential helper
      run: gcloud auth configure-docker
    
    - name: Build Frontend Docker image
      run: |
        docker build -t $REGISTRY/$PROJECT_ID/social-media-frontend:$GITHUB_SHA ./app-frontend
        docker tag $REGISTRY/$PROJECT_ID/social-media-frontend:$GITHUB_SHA $REGISTRY/$PROJECT_ID/social-media-frontend:latest
    
    - name: Build Backend Docker image
      run: |
        docker build -t $REGISTRY/$PROJECT_ID/social-media-backend:$GITHUB_SHA ./app-agents
        docker tag $REGISTRY/$PROJECT_ID/social-media-backend:$GITHUB_SHA $REGISTRY/$PROJECT_ID/social-media-backend:latest
    
    - name: Push Docker images
      run: |
        docker push $REGISTRY/$PROJECT_ID/social-media-frontend:$GITHUB_SHA
        docker push $REGISTRY/$PROJECT_ID/social-media-frontend:latest
        docker push $REGISTRY/$PROJECT_ID/social-media-backend:$GITHUB_SHA
        docker push $REGISTRY/$PROJECT_ID/social-media-backend:latest

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    
    - name: Deploy Backend to Cloud Run (Staging)
      run: |
        gcloud run deploy social-media-backend-staging \
          --image $REGISTRY/$PROJECT_ID/social-media-backend:$GITHUB_SHA \
          --platform managed \
          --region us-central1 \
          --allow-unauthenticated \
          --set-env-vars="DEBUG=false,SECRET_KEY=${{ secrets.SECRET_KEY }}" \
          --memory=2Gi \
          --cpu=2 \
          --max-instances=10
    
    - name: Deploy Frontend to Cloud Run (Staging)
      run: |
        gcloud run deploy social-media-frontend-staging \
          --image $REGISTRY/$PROJECT_ID/social-media-frontend:$GITHUB_SHA \
          --platform managed \
          --region us-central1 \
          --allow-unauthenticated \
          --set-env-vars="NEXT_PUBLIC_API_URL=${{ secrets.STAGING_API_URL }}" \
          --memory=1Gi \
          --cpu=1 \
          --max-instances=5

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    
    - name: Deploy Backend to Cloud Run (Production)
      run: |
        gcloud run deploy social-media-backend \
          --image $REGISTRY/$PROJECT_ID/social-media-backend:$GITHUB_SHA \
          --platform managed \
          --region us-central1 \
          --allow-unauthenticated \
          --set-env-vars="DEBUG=false,SECRET_KEY=${{ secrets.SECRET_KEY }}" \
          --memory=4Gi \
          --cpu=2 \
          --max-instances=50 \
          --min-instances=1
    
    - name: Deploy Frontend to Cloud Run (Production)
      run: |
        gcloud run deploy social-media-frontend \
          --image $REGISTRY/$PROJECT_ID/social-media-frontend:$GITHUB_SHA \
          --platform managed \
          --region us-central1 \
          --allow-unauthenticated \
          --set-env-vars="NEXT_PUBLIC_API_URL=${{ secrets.PRODUCTION_API_URL }}" \
          --memory=2Gi \
          --cpu=1 \
          --max-instances=20 \
          --min-instances=1

  # Post-deployment tests
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install Playwright
      working-directory: ./app-frontend
      run: |
        npm ci
        npx playwright install
    
    - name: Run E2E tests
      working-directory: ./app-frontend
      run: npx playwright test
      env:
        BASE_URL: ${{ secrets.STAGING_URL }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-results
        path: ./app-frontend/test-results/