import { renderHook, act } from '@testing-library/react';
import { useSessionPersistence } from '@/hooks/use-session-persistence';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('useSessionPersistence', () => {
  const mockOptions = {
    userId: 'test-user',
    agentName: 'test-agent',
    storageType: 'localStorage' as const,
  };

  beforeEach(() => {
    localStorageMock.clear();
  });

  it('should initialize with no session', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    expect(result.current.sessionId).toBeNull();
    expect(result.current.hasStoredSession).toBe(false);
    expect(result.current.sessionData).toBeNull();
  });

  it('should save session data', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    act(() => {
      result.current.saveSession('test-session-123', { test: 'metadata' });
    });

    expect(result.current.sessionId).toBe('test-session-123');
    expect(result.current.hasStoredSession).toBe(true);
    expect(result.current.sessionData).toMatchObject({
      sessionId: 'test-session-123',
      userId: 'test-user',
      agentName: 'test-agent',
      messageCount: 0,
      metadata: { test: 'metadata' }
    });
  });

  it('should clear session data', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    // First save a session
    act(() => {
      result.current.saveSession('test-session-123');
    });

    expect(result.current.sessionId).toBe('test-session-123');

    // Then clear it
    act(() => {
      result.current.clearSession();
    });

    expect(result.current.sessionId).toBeNull();
    expect(result.current.hasStoredSession).toBe(false);
    expect(result.current.sessionData).toBeNull();
  });

  it('should recover session on mount', async () => {
    // Pre-populate localStorage with session data
    const sessionData = {
      sessionId: 'recovered-session',
      userId: 'test-user',
      agentName: 'test-agent',
      lastActivity: new Date().toISOString(),
      messageCount: 5,
    };

    localStorageMock.setItem(
      'adk_session_test-user_test-agent_recovered-session',
      JSON.stringify(sessionData)
    );

    localStorageMock.setItem(
      'adk_sessions_list_test-user',
      JSON.stringify([sessionData])
    );

    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    // Wait for recovery to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    expect(result.current.sessionId).toBe('recovered-session');
    expect(result.current.hasStoredSession).toBe(true);
  });

  it('should update activity', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    // Save initial session
    act(() => {
      result.current.saveSession('test-session-123');
    });

    const initialActivity = result.current.sessionData?.lastActivity;

    // Update activity
    act(() => {
      result.current.updateActivity();
    });

    const updatedActivity = result.current.sessionData?.lastActivity;
    expect(updatedActivity).not.toBe(initialActivity);
    expect(result.current.sessionData?.messageCount).toBe(1);
  });

  it('should get stored sessions list', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    // Save multiple sessions
    act(() => {
      result.current.saveSession('session-1');
    });

    act(() => {
      result.current.saveSession('session-2');
    });

    const storedSessions = result.current.getStoredSessions();
    expect(storedSessions).toHaveLength(2);
    expect(storedSessions[0].sessionId).toBe('session-2'); // Most recent first
    expect(storedSessions[1].sessionId).toBe('session-1');
  });

  it('should remove specific stored session', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    // Save multiple sessions
    act(() => {
      result.current.saveSession('session-1');
    });

    act(() => {
      result.current.saveSession('session-2');
    });

    expect(result.current.getStoredSessions()).toHaveLength(2);

    // Remove one session
    act(() => {
      result.current.removeStoredSession('session-1');
    });

    const remainingSessions = result.current.getStoredSessions();
    expect(remainingSessions).toHaveLength(1);
    expect(remainingSessions[0].sessionId).toBe('session-2');
  });

  it('should clear all sessions', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    // Save multiple sessions
    act(() => {
      result.current.saveSession('session-1');
    });

    act(() => {
      result.current.saveSession('session-2');
    });

    expect(result.current.getStoredSessions()).toHaveLength(2);

    // Clear all sessions
    act(() => {
      result.current.clearAllSessions();
    });

    expect(result.current.getStoredSessions()).toHaveLength(0);
    expect(result.current.sessionId).toBeNull();
  });

  it('should filter out expired sessions', () => {
    const { result } = renderHook(() => useSessionPersistence(mockOptions));

    // Create expired session data
    const expiredDate = new Date();
    expiredDate.setHours(expiredDate.getHours() - 25); // 25 hours ago

    const expiredSessionData = {
      sessionId: 'expired-session',
      userId: 'test-user',
      agentName: 'test-agent',
      lastActivity: expiredDate.toISOString(),
      messageCount: 1,
    };

    const validSessionData = {
      sessionId: 'valid-session',
      userId: 'test-user',
      agentName: 'test-agent',
      lastActivity: new Date().toISOString(),
      messageCount: 1,
    };

    // Manually add to localStorage
    localStorageMock.setItem(
      'adk_sessions_list_test-user',
      JSON.stringify([validSessionData, expiredSessionData])
    );

    const storedSessions = result.current.getStoredSessions();
    expect(storedSessions).toHaveLength(1);
    expect(storedSessions[0].sessionId).toBe('valid-session');
  });
});