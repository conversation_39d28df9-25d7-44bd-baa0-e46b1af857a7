"""
Test Feature Flags Implementation

This test module verifies the feature flag functionality for ADK integration,
including environment variable loading, user-specific flags, and agent filtering.

Requirements covered: 6.1, 6.2, 6.3
"""

import pytest
import os
import tempfile
import json
from unittest.mock import patch

from app.core.feature_flags import (
    FeatureFlagManager, ADKFeatureFlags, 
    is_adk_enabled, is_streaming_enabled, is_agent_enabled,
    is_debug_mode_enabled, get_feature_flag_manager,
    validate_feature_flags
)


class TestFeatureFlagManager:
    """Test feature flag manager functionality"""
    
    def test_default_configuration(self):
        """Test default feature flag configuration"""
        manager = FeatureFlagManager()
        
        # Test default values
        assert manager.is_enabled("adk_enabled") == True
        assert manager.is_enabled("adk_streaming_enabled") == True
        assert manager.is_enabled("debug_mode_enabled") == False
        assert manager.get_flag_value("rollout_percentage") == 100
    
    def test_environment_variable_loading(self):
        """Test loading feature flags from environment variables"""
        with patch.dict(os.environ, {
            "ADK_ENABLED": "false",
            "ADK_STREAMING_ENABLED": "true",
            "ADK_DEBUG_MODE": "true",
            "ADK_ROLLOUT_PERCENTAGE": "50",
            "ADK_DISABLED_AGENTS": "agent1,agent2,agent3",
            "ADK_BETA_AGENTS": "beta_agent1,beta_agent2"
        }):
            manager = FeatureFlagManager()
            
            assert manager.is_enabled("adk_enabled") == False
            assert manager.is_enabled("adk_streaming_enabled") == True
            assert manager.is_enabled("debug_mode_enabled") == True
            assert manager.get_flag_value("rollout_percentage") == 50
            assert manager.get_flag_value("disabled_agents") == ["agent1", "agent2", "agent3"]
            assert manager.get_flag_value("beta_agents") == ["beta_agent1", "beta_agent2"]
    
    def test_config_file_loading(self):
        """Test loading feature flags from configuration file"""
        config_data = {
            "adk_enabled": False,
            "debug_mode_enabled": True,
            "rollout_percentage": 25,
            "disabled_agents": ["test_agent"],
            "beta_user_ids": ["beta_user_1", "beta_user_2"]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_file = f.name
        
        try:
            manager = FeatureFlagManager(config_file)
            
            assert manager.is_enabled("adk_enabled") == False
            assert manager.is_enabled("debug_mode_enabled") == True
            assert manager.get_flag_value("rollout_percentage") == 25
            assert manager.get_flag_value("disabled_agents") == ["test_agent"]
            assert manager.get_flag_value("beta_user_ids") == ["beta_user_1", "beta_user_2"]
        finally:
            os.unlink(config_file)
    
    def test_agent_enablement(self):
        """Test agent-specific enablement logic"""
        manager = FeatureFlagManager()
        
        # Set up test configuration
        manager.set_flag("disabled_agents", ["disabled_agent"])
        manager.set_flag("beta_agents", ["beta_agent"])
        manager.set_flag("beta_user_ids", ["beta_user"])
        
        # Test normal agent
        assert manager.is_agent_enabled("normal_agent", "regular_user") == True
        
        # Test disabled agent
        assert manager.is_agent_enabled("disabled_agent", "regular_user") == False
        
        # Test beta agent with regular user
        assert manager.is_agent_enabled("beta_agent", "regular_user") == False
        
        # Test beta agent with beta user
        assert manager.is_agent_enabled("beta_agent", "beta_user") == True
    
    def test_rollout_percentage(self):
        """Test rollout percentage functionality"""
        manager = FeatureFlagManager()
        
        # Test 100% rollout
        manager.set_flag("rollout_percentage", 100)
        assert manager.is_enabled("adk_enabled", "any_user") == True
        
        # Test 0% rollout
        manager.set_flag("rollout_percentage", 0)
        assert manager.is_enabled("adk_enabled", "any_user") == False
        
        # Test 50% rollout (deterministic based on user hash)
        manager.set_flag("rollout_percentage", 50)
        
        # Test with specific users (results should be consistent)
        user1_enabled = manager.is_enabled("adk_enabled", "user1")
        user2_enabled = manager.is_enabled("adk_enabled", "user2")
        
        # Results should be consistent for same user
        assert manager.is_enabled("adk_enabled", "user1") == user1_enabled
        assert manager.is_enabled("adk_enabled", "user2") == user2_enabled
    
    def test_beta_user_functionality(self):
        """Test beta user functionality"""
        manager = FeatureFlagManager()
        manager.set_flag("beta_user_ids", ["beta1", "beta2"])
        
        assert manager.is_beta_user("beta1") == True
        assert manager.is_beta_user("beta2") == True
        assert manager.is_beta_user("regular_user") == False
        assert manager.is_beta_user(None) == False
    
    def test_runtime_flag_updates(self):
        """Test runtime flag updates"""
        manager = FeatureFlagManager()
        
        # Test setting valid flag
        assert manager.set_flag("debug_mode_enabled", True) == True
        assert manager.is_enabled("debug_mode_enabled") == True
        
        # Test setting invalid flag
        assert manager.set_flag("nonexistent_flag", True) == False
    
    def test_flag_validation(self):
        """Test feature flag validation"""
        # Test with valid configuration
        manager = FeatureFlagManager()
        manager.set_flag("rollout_percentage", 75)
        manager.set_flag("adk_streaming_timeout", 300)
        manager.set_flag("disabled_agents", ["agent1"])
        manager.set_flag("beta_agents", ["agent2"])
        
        # Should have no validation errors
        with patch('app.core.feature_flags.get_feature_flag_manager', return_value=manager):
            errors = validate_feature_flags()
            assert len(errors) == 0
        
        # Test with invalid configuration
        manager.set_flag("rollout_percentage", 150)  # Invalid
        manager.set_flag("adk_streaming_timeout", -1)  # Invalid
        manager.set_flag("disabled_agents", ["agent1"])
        manager.set_flag("beta_agents", ["agent1"])  # Conflict
        
        with patch('app.core.feature_flags.get_feature_flag_manager', return_value=manager):
            errors = validate_feature_flags()
            assert len(errors) > 0
            assert any("rollout_percentage" in error for error in errors)
            assert any("adk_streaming_timeout" in error for error in errors)
            assert any("both disabled and beta" in error for error in errors)


class TestConvenienceFunctions:
    """Test convenience functions"""
    
    def test_is_adk_enabled(self):
        """Test is_adk_enabled convenience function"""
        with patch('app.core.feature_flags.get_feature_flag_manager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.is_enabled.return_value = True
            
            result = is_adk_enabled("test_user")
            
            assert result == True
            mock_instance.is_enabled.assert_called_once_with("adk_enabled", "test_user")
    
    def test_is_streaming_enabled(self):
        """Test is_streaming_enabled convenience function"""
        with patch('app.core.feature_flags.get_feature_flag_manager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.is_enabled.side_effect = lambda flag, user: flag in ["adk_enabled", "adk_streaming_enabled"]
            
            result = is_streaming_enabled("test_user")
            
            assert result == True
            assert mock_instance.is_enabled.call_count == 2
    
    def test_is_agent_enabled(self):
        """Test is_agent_enabled convenience function"""
        with patch('app.core.feature_flags.get_feature_flag_manager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.is_agent_enabled.return_value = True
            
            result = is_agent_enabled("test_agent", "test_user")
            
            assert result == True
            mock_instance.is_agent_enabled.assert_called_once_with("test_agent", "test_user")
    
    def test_is_debug_mode_enabled(self):
        """Test is_debug_mode_enabled convenience function"""
        with patch('app.core.feature_flags.get_feature_flag_manager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.is_enabled.return_value = False
            
            result = is_debug_mode_enabled()
            
            assert result == False
            mock_instance.is_enabled.assert_called_once_with("debug_mode_enabled")


class TestADKFeatureFlags:
    """Test ADK feature flags model"""
    
    def test_default_values(self):
        """Test default feature flag values"""
        flags = ADKFeatureFlags()
        
        assert flags.adk_enabled == True
        assert flags.adk_streaming_enabled == True
        assert flags.debug_mode_enabled == False
        assert flags.rollout_percentage == 100
        assert flags.disabled_agents == []
        assert flags.beta_agents == []
    
    def test_custom_values(self):
        """Test custom feature flag values"""
        flags = ADKFeatureFlags(
            adk_enabled=False,
            debug_mode_enabled=True,
            rollout_percentage=50,
            disabled_agents=["agent1", "agent2"],
            beta_user_ids=["beta1"]
        )
        
        assert flags.adk_enabled == False
        assert flags.debug_mode_enabled == True
        assert flags.rollout_percentage == 50
        assert flags.disabled_agents == ["agent1", "agent2"]
        assert flags.beta_user_ids == ["beta1"]


class TestFeatureFlagDecorators:
    """Test feature flag decorators"""
    
    def test_require_feature_flag_decorator(self):
        """Test require_feature_flag decorator"""
        from app.core.feature_flags import require_feature_flag
        
        @require_feature_flag("test_flag", fallback_value="fallback")
        def test_function(user_id=None):
            return "success"
        
        with patch('app.core.feature_flags.get_feature_flag_manager') as mock_manager:
            mock_instance = mock_manager.return_value
            
            # Test when flag is enabled
            mock_instance.is_enabled.return_value = True
            result = test_function(user_id="test_user")
            assert result == "success"
            
            # Test when flag is disabled
            mock_instance.is_enabled.return_value = False
            result = test_function(user_id="test_user")
            assert result == "fallback"
    
    def test_require_agent_enabled_decorator(self):
        """Test require_agent_enabled decorator"""
        from app.core.feature_flags import require_agent_enabled
        
        @require_agent_enabled("test_agent", fallback_value="agent_disabled")
        def test_function(user_id=None):
            return "agent_enabled"
        
        with patch('app.core.feature_flags.get_feature_flag_manager') as mock_manager:
            mock_instance = mock_manager.return_value
            
            # Test when agent is enabled
            mock_instance.is_agent_enabled.return_value = True
            result = test_function(user_id="test_user")
            assert result == "agent_enabled"
            
            # Test when agent is disabled
            mock_instance.is_agent_enabled.return_value = False
            result = test_function(user_id="test_user")
            assert result == "agent_disabled"


if __name__ == "__main__":
    pytest.main([__file__])