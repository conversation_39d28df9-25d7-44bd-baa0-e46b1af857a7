"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  Bot, 
  User, 
  Copy, 
  RotateCcw, 
  Trash2, 
  ChevronDown, 
  ChevronRight,
  Zap,
  AlertTriangle,
  Clock
} from 'lucide-react';
import { EnhancedChatMessage } from '@/types/adk';
import { FunctionCallDisplay } from './function-call-display';
import { cn } from '@/lib/utils';

export interface EnhancedMessageProps {
  message: EnhancedChatMessage;
  isStreaming?: boolean;
  showTimestamp?: boolean;
  showAgent?: boolean;
  showFunctionCalls?: boolean;
  onRetry?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  className?: string;
}

export function EnhancedMessage({
  message,
  isStreaming = false,
  showTimestamp = true,
  showAgent = true,
  showFunctionCalls = true,
  onRetry,
  onDelete,
  onCopy,
  className
}: EnhancedMessageProps) {
  const [showFunctionDetails, setShowFunctionDetails] = useState(false);
  const [copied, setCopied] = useState(false);

  const isUser = message.role === 'user';
  const isAssistant = message.role === 'model';
  const hasError = message.metadata?.error;
  const isInterrupted = message.interrupted;
  const hasFunctionCalls = message.function_calls && message.function_calls.length > 0;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onCopy?.();
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className={cn(
      "flex gap-3",
      isUser ? "justify-end" : "justify-start",
      className
    )}>
      {/* Avatar for assistant messages */}
      {isAssistant && (
        <Avatar className="w-8 h-8 mt-1 flex-shrink-0">
          <AvatarFallback className={cn(
            hasError ? "bg-red-100 text-red-600" :
            isInterrupted ? "bg-orange-100 text-orange-600" :
            isStreaming ? "bg-blue-100 text-blue-600" :
            "bg-green-100 text-green-600"
          )}>
            <Bot className="w-4 h-4" />
          </AvatarFallback>
        </Avatar>
      )}

      {/* Message Content */}
      <div className={cn(
        "max-w-[80%] space-y-2",
        isUser && "order-2"
      )}>
        {/* Agent Name and Status */}
        {isAssistant && showAgent && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>{message.agent_name || 'Assistant'}</span>
            {isStreaming && (
              <Badge variant="secondary" className="h-5 px-2 text-xs">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-1" />
                Streaming
              </Badge>
            )}
            {isInterrupted && (
              <Badge variant="destructive" className="h-5 px-2 text-xs">
                <AlertTriangle className="w-3 h-3 mr-1" />
                Interrupted
              </Badge>
            )}
            {hasError && (
              <Badge variant="destructive" className="h-5 px-2 text-xs">
                Error
              </Badge>
            )}
          </div>
        )}

        {/* Main Message Card */}
        <Card className={cn(
          "transition-all duration-200",
          isUser 
            ? "bg-primary text-primary-foreground" 
            : hasError 
              ? "bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800"
              : isStreaming
                ? "bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800"
                : "bg-card",
          isStreaming && "animate-pulse"
        )}>
          <CardContent className="p-3">
            {/* Message Text */}
            <div className="space-y-2">
              <p className="text-sm whitespace-pre-wrap leading-relaxed">
                {message.content}
                {isStreaming && (
                  <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse" />
                )}
              </p>

              {/* Function Calls Section */}
              {showFunctionCalls && hasFunctionCalls && (
                <FunctionCallDisplay
                  functionCalls={message.function_calls!}
                  functionResponses={message.metadata?.function_responses}
                  toolExecutions={message.metadata?.tool_executions}
                  expanded={showFunctionDetails}
                  onToggleExpanded={() => setShowFunctionDetails(!showFunctionDetails)}
                  onCopy={onCopy}
                />
              )}

              {/* Error Details */}
              {hasError && message.metadata?.error_details && (
                <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/20 rounded text-xs">
                  <div className="font-semibold text-red-800 dark:text-red-200">
                    Error Details:
                  </div>
                  <div className="text-red-700 dark:text-red-300 mt-1">
                    {message.metadata.error_details}
                  </div>
                </div>
              )}
            </div>

            {/* Message Footer */}
            <div className="flex items-center justify-between mt-3 pt-2 border-t border-border/30">
              {/* Timestamp and Processing Time */}
              <div className="flex items-center gap-2 text-xs opacity-70">
                {showTimestamp && (
                  <span>{formatTimestamp(message.timestamp)}</span>
                )}
                {message.processing_time_ms && (
                  <>
                    <span>•</span>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{message.processing_time_ms}ms</span>
                    </div>
                  </>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                >
                  <Copy className="w-3 h-3" />
                </Button>

                {isAssistant && onRetry && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onRetry}
                    className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                  >
                    <RotateCcw className="w-3 h-3" />
                  </Button>
                )}

                {onDelete && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onDelete}
                    className="h-6 w-6 p-0 opacity-60 hover:opacity-100 text-red-500 hover:text-red-600"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Copy Confirmation */}
        {copied && (
          <div className="text-xs text-green-600 text-center">
            Copied to clipboard!
          </div>
        )}
      </div>

      {/* Avatar for user messages */}
      {isUser && (
        <Avatar className="w-8 h-8 mt-1 flex-shrink-0 order-3">
          <AvatarFallback className="bg-primary/10 text-primary">
            <User className="w-4 h-4" />
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}