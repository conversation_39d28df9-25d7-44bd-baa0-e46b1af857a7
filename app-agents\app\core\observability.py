"""
Observability Module

Simple observability utilities for logging and monitoring.
"""

import logging
import sys
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)


class Observability:
    """Simple observability class"""
    
    def __init__(self, name: str = "app"):
        self.logger = logging.getLogger(name)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, extra=kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, extra=kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, extra=kwargs)


# Global observability instance
observability = Observability("adk-integration")


def get_health_status() -> Dict[str, Any]:
    """
    Return a simple health status object for the application observability.

    This provides a lightweight health endpoint helper so higher-level modules
    can import and reuse a consistent health-check shape without depending on
    external services.
    """
    try:
        # Basic health information: logger exists and is usable
        logger_name = getattr(observability.logger, 'name', None)
        return {"status": "healthy", "logger_name": logger_name}
    except Exception as e:
        # If anything goes wrong here, return an error status with details
        return {"status": "error", "error": str(e)}