## Project Overview
The Agent Development Kit (ADK) is an open-source, code-first Python toolkit for building, evaluating, and deploying sophisticated AI agents, with a focus on integration with Google Cloud services and Gemini models . It emphasizes flexibility and fine-grained control over agent behavior, orchestration, and tool usage directly within Python code .

The main goals of ADK include simplifying the development of AI agents by providing a robust and flexible environment that makes the process feel more like traditional software development . It supports both conversational and non-conversational agents, capable of handling tasks ranging from simple to complex workflows .

The target audience for ADK includes developers building AI agents, particularly those interested in integrating with Google Cloud services and Gemini models .

## Architecture & Structure
### High-level architecture overview
ADK is built around several key primitives: `Agent`, `Tool`, `Callbacks`, `Session Management` (`Session` & `State`), `Memory`, `Artifact Management`, `Code Execution`, `Planning`, `Models`, `Event`, and `Runner` . The `Runner` acts as the central orchestrator for a single user invocation, processing events and committing changes . The execution logic within agents, tools, and callbacks performs computation and decision-making, yielding `Event` objects to the `Runner` .

### Key directories and their purposes
The `google/adk-docs` repository primarily contains the source for the documentation site .
*   `docs/agents/`: Contains documentation related to different types of agents, including LLM Agents, Workflow Agents, and Custom Agents .
*   `docs/tools/`: Provides information on various tool types and how agents use them .
*   `docs/get-started/`: Includes quickstart guides for setting up and running basic agents .
*   `examples/`: Contains code examples and snippets illustrating key functionalities .

### Main components and how they interact
*   **Agents**: The fundamental worker unit designed for specific tasks . They can be `LlmAgent` (LLM-powered), `Workflow Agents` (`SequentialAgent`, `ParallelAgent`, `LoopAgent` for predefined patterns), or `Custom Agents` (for arbitrary orchestration logic) .
*   **Tools**: Equip agents with capabilities beyond conversation, allowing interaction with external APIs, search, code execution, or other services .
*   **Session & State**: `Session` represents a single interaction, containing `Events` and temporary `State` . `State` is a key-value store within each `Session` for dynamic data .
*   **Callbacks**: Custom code snippets that run at specific points in an agent's process for checks, logging, or behavior modifications .
*   **Runner**: The engine that manages the execution flow, orchestrates agent interactions, and coordinates with backend services .

### Data flow and system design
Agents communicate primarily through a shared `Session State` (`ctx.session.state`) . One agent writes a value to `context.state['data_key']`, and a subsequent agent reads it . The `output_key` property on `LlmAgent` automatically saves the agent's final response to a specified state key . `Events` are the fundamental units of information flow, capturing user messages, agent replies, tool requests, tool results, state changes, and control signals .

## Development Setup
### Prerequisites and dependencies
For Python, it's recommended to create and activate a virtual environment using `venv` before installing the `google-adk` package via `pip` . For Java, `google-adk` and `google-adk-dev` packages can be added via Maven or Gradle dependencies . MCP requires Python 3.9+ or Java 17+ . Node.js and `npx` are also required for some community MCP servers .

### Installation steps
1.  **Clone the repository**: `<NAME_EMAIL>:google/adk-docs.git` and `cd adk-docs` .
2.  **Create and activate a virtual environment**: `python -m venv venv` and `source venv/bin/activate` .
3.  **Install dependencies**: `pip install -r requirements.txt` .

### Environment configuration
Environment variables are used to set up the LLM, either with a Google AI Studio API key (`GOOGLE_API_KEY`, `GOOGLE_GENAI_USE_VERTEXAI=FALSE`) or Google Cloud Vertex AI project details (`GOOGLE_CLOUD_PROJECT`, `GOOGLE_CLOUD_LOCATION`, `GOOGLE_GENAI_USE_VERTEXAI=TRUE`) .

### How to run the project locally
To run the local development server for the documentation, use `mkdocs serve` . This typically starts a server at `http://127.0.0.1:8000/` .
Agents can be run locally using:
*   `adk web` for a browser-based Dev UI .
*   `adk run` for terminal interaction .
*   `adk api_server` for a local FastAPI server .

## Code Organization
### Coding standards and conventions
The project adheres to Google's Open Source Community Guidelines .

### File naming patterns
For Python agents, a typical project structure includes a parent folder, an agent-specific folder (e.g., `multi_tool_agent/`), and `__init__.py` and `agent.py` files within it . For Java, the structure typically includes `project_folder/src/main/java/agents/multitool/MultiToolAgent.java` .

### Import/export patterns
The `__init__.py` file in Python agent directories typically imports the `agent` module, e.g., `from . import agent` .

### Component structure
ADK agents are built upon the `BaseAgent` class .
*   `LlmAgent`: Uses Large Language Models for reasoning and dynamic decision-making .
*   `Workflow Agents`: Control execution flow in predefined patterns (`SequentialAgent`, `ParallelAgent`, `LoopAgent`) .
*   `Custom Agents`: Extend `BaseAgent` directly for unique operational logic or integrations .
Sub-agents are typically passed into a custom agent's constructor and stored as instance attributes .

## Key Features & Implementation
### Main features and how they're implemented
*   **Rich Tool Ecosystem**: Supports built-in tools (Google Search, Code Execution, Vertex AI Search), custom Python functions, OpenAPI spec integration, third-party libraries (LangChain, CrewAI), and using other agents as tools . Tools are defined with descriptive names, JSON-serializable parameters with type hints, and docstrings explaining their purpose .
*   **Code-First Development**: Define agent logic, workflows, and state management directly in Python, enabling testability, versioning, and debugging .
*   **Flexible Orchestration**: Build multi-agent systems using predefined workflow agents or leverage `LlmAgent` for dynamic, LLM-driven routing . Custom agents allow for arbitrary orchestration logic by implementing `_run_async_impl` (Python) or `runAsyncImpl` (Java) .
*   **Context & State Management**: Mechanisms for managing conversational context (`Session`), short-term state (`State`), long-term memory (`MemoryService`), and binary data (`ArtifactService`) . `InvocationContext` provides access to session state, artifacts, and services .
*   **Callbacks for Control**: Hooks (`before/after_agent`, `before/after_model`, `before/after_tool`) to observe, customize, or intercept agent execution flow . Callbacks can be used for guardrails, dynamic state management, logging, caching, and more .
*   **Multi-Agent System Design**: Facilitates building applications composed of multiple, specialized agents arranged hierarchically . Agents can communicate via shared session state, LLM-driven delegation, or explicit invocation using `AgentTool` .

### Important algorithms or business logic
Custom agents allow for implementing conditional logic, complex state management, external integrations, and dynamic agent selection . For example, a `StoryFlowAgent` demonstrates conditional regeneration based on a tone check result .

### API endpoints (if applicable)
The `adk api_server` command can be used to expose a local FastAPI server for agents .

## Testing Strategy
### Testing frameworks used
The documentation mentions using `pytest` for programmatic evaluation .

### Test file organization
Evaluation can be done using individual `.test.json` files for unit testing or an `Evalset File` for integration tests and multi-turn conversations . Test files include user content, expected intermediate tool use trajectory, expected intermediate agent responses, and the final response .

### How to run tests
Evaluations can be run via:
*   Web-based UI (`adk web`) .
*   Programmatically (`pytest`) .
*   Command Line Interface (`adk eval`) .

### Testing best practices in this codebase
*   Evaluate both the final output and the agent's trajectory (sequence of steps)

# Development Partnership and How We Should Partner

We build production code together. I handle implementation details while you guide architecture and catch complexity early.

## Core Workflow: Research → Plan → Implement → Validate

**Start every feature with:** "Let me research the codebase and create a plan before implementing."

1. **Research** - Understand existing patterns and architecture
2. **Plan** - Propose approach and verify with you
3. **Implement** - Build with tests and error handling
4. **Validate** - ALWAYS run formatters, linters, and tests after implementation

## Code Organization

**Keep functions small and focused:**
- If you need comments to explain sections, split into functions
- Group related functionality into clear packages
- Prefer many small files over few large ones

## Architecture Principles

**This is always a feature branch:**
- Delete old code completely - no deprecation needed
- No "removed code" or "added this line" comments - just do it

**Prefer explicit over implicit:**
- Clear function names over clever abstractions
- Obvious data flow over hidden magic
- Direct dependencies over service locators

## Maximize Efficiency

**Parallel operations:** Run multiple searches, reads, and greps in single messages
**Multiple agents:** Split complex tasks - one for tests, one for implementation
**Batch similar work:** Group related file edits together

## Problem Solving

**When stuck:** Stop. The simple solution is usually correct.

**When uncertain:** "Let me ultrathink about this architecture."

**When choosing:** "I see approach A (simple) vs B (flexible). Which do you prefer?"

Your redirects prevent over-engineering. When uncertain about implementation, stop and ask for guidance.

## Testing Strategy

**Match testing approach to code complexity:**
- Complex business logic: Write tests first (TDD)
- Simple CRUD operations: Write code first, then tests
- Hot paths: Add benchmarks after implementation

**Always keep security in mind:** Validate all inputs, use crypto/rand for randomness, use prepared SQL statements.

**Performance rule:** Measure before optimizing. No guessing.

## Progress Tracking

- **Use Todo lists** for task management
- **Clear naming** in all code

Focus on maintainable solutions over clever abstractions.

---
Generated using [Sidekick Dev]({REPO_URL}), your coding agent sidekick.
