"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { AccountData } from "@/hooks/use-connected-accounts";

interface MetricsChartProps {
  title: string;
  accounts: AccountData[];
  metric: 'followers' | 'engagement' | 'reach';
  timeframe: string;
}

export function MetricsChart({ title, accounts, metric, timeframe }: MetricsChartProps) {
  // Generate mock time series data for demonstration
  const generateMockData = () => {
    const days = timeframe === '7d' ? 7 : timeframe === '30d' ? 30 : 90;
    const data = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      const dataPoint: any = {
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        fullDate: date.toISOString(),
      };

      // Generate realistic data for each connected platform
      accounts.forEach(account => {
        const baseValue = account.metrics?.[metric === 'followers' ? 'followers' : 'engagement'] || 0;
        const growth = account.metrics?.growth?.followers || 0;
        
        // Simulate gradual growth/decline with some randomness
        const progressFactor = (days - i) / days;
        const randomVariation = 0.9 + Math.random() * 0.2; // ±10% random variation
        
        let value;
        if (metric === 'followers') {
          // For followers, show cumulative growth
          const totalGrowth = (growth / 100) * progressFactor;
          value = Math.round(baseValue * (1 - totalGrowth + (totalGrowth * progressFactor)) * randomVariation);
        } else if (metric === 'engagement') {
          // For engagement, show rate fluctuation
          value = Math.max(0, baseValue * randomVariation);
        } else {
          // For reach, show variable daily reach
          value = Math.round(baseValue * 10 * randomVariation); // Scale up for reach
        }
        
        dataPoint[account.platform] = value;
      });

      data.push(dataPoint);
    }
    
    return data;
  };

  const data = generateMockData();

  const platformColors = {
    youtube: '#ff0000',
    instagram: '#e4405f',
    twitter: '#1da1f2',
  };

  const formatYAxis = (value: number) => {
    if (metric === 'engagement') {
      return `${value.toFixed(1)}%`;
    }
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  const formatTooltip = (value: number, name: string) => {
    const formattedValue = metric === 'engagement' 
      ? `${value.toFixed(1)}%` 
      : value.toLocaleString();
    return [formattedValue, name.charAt(0).toUpperCase() + name.slice(1)];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
        <p className="text-sm text-muted-foreground">
          Tracking {metric} over the last {timeframe === '7d' ? '7 days' : timeframe === '30d' ? '30 days' : '90 days'}
        </p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            {metric === 'followers' ? (
              <AreaChart data={data}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  axisLine={false}
                  tickLine={false}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  tickFormatter={formatYAxis}
                  axisLine={false}
                  tickLine={false}
                />
                <Tooltip 
                  formatter={formatTooltip}
                  labelStyle={{ color: '#374151' }}
                  contentStyle={{ 
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                {accounts.map((account) => (
                  <Area
                    key={account.id}
                    type="monotone"
                    dataKey={account.platform}
                    stroke={platformColors[account.platform as keyof typeof platformColors]}
                    fill={platformColors[account.platform as keyof typeof platformColors]}
                    fillOpacity={0.1}
                    strokeWidth={2}
                  />
                ))}
              </AreaChart>
            ) : (
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  axisLine={false}
                  tickLine={false}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  tickFormatter={formatYAxis}
                  axisLine={false}
                  tickLine={false}
                />
                <Tooltip 
                  formatter={formatTooltip}
                  labelStyle={{ color: '#374151' }}
                  contentStyle={{ 
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                {accounts.map((account) => (
                  <Line
                    key={account.id}
                    type="monotone"
                    dataKey={account.platform}
                    stroke={platformColors[account.platform as keyof typeof platformColors]}
                    strokeWidth={2}
                    dot={{ fill: platformColors[account.platform as keyof typeof platformColors], strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: platformColors[account.platform as keyof typeof platformColors], strokeWidth: 2 }}
                  />
                ))}
              </LineChart>
            )}
          </ResponsiveContainer>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center gap-6 mt-4">
          {accounts.map((account) => (
            <div key={account.id} className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: platformColors[account.platform as keyof typeof platformColors] }}
              />
              <span className="text-sm capitalize">{account.platform}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}