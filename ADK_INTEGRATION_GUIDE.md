# Google ADK Integration Guide

## Overview

This project now includes **Google ADK (Agent Development Kit)** integration to enhance the social media agents with advanced AI capabilities. ADK provides intelligent, conversational agents powered by Google's Gemini models.

## What's Been Added

### 🚀 **Core ADK Components**

1. **ADK Research Agent** (`app/agents/adk_research_agent.py`)
   - Full ADK LlmAgent implementation
   - Tool functions for content trends, competitor analysis, fact-checking
   - Session-managed intelligent conversations

2. **Enhanced Research Agent** (`app/agents/research_agent.py`) 
   - Hybrid approach: ADK intelligence + traditional search
   - Automatic fallback when ADK unavailable
   - Backward compatibility maintained

3. **Session Management Service** (`app/services/adk_session_service.py`)
   - Centralized ADK session lifecycle management
   - Automatic cleanup and timeout handling
   - Mock mode when ADK unavailable

4. **Configuration Service** (`app/services/adk_config_service.py`)
   - Environment setup and validation
   - Agent creation and configuration
   - Comprehensive status checking

5. **Updated Coordinator** (`app/agents/coordinator.py`)
   - Seamless ADK integration
   - Enhanced result formatting
   - Intelligent agent selection

### 📦 **Dependencies Added**

- `google-adk==1.12.0` - Latest Google ADK framework
- Compatible with existing Google Cloud services

## Setup Instructions

### 1. Installation

ADK is already installed via `requirements.txt`. If you need to install manually:

```bash
cd app-agents
pip install google-adk==1.12.0
```

### 2. Environment Configuration

Choose your preferred model provider:

#### Option A: Google AI Studio (Recommended for Development)

1. Get API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Update `.env` file:

```bash
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
```

#### Option B: Google Cloud Vertex AI (For Production)

1. Set up Google Cloud project
2. Enable Vertex AI API
3. Authenticate: `gcloud auth application-default login`
4. Update `.env` file:

```bash
GOOGLE_GENAI_USE_VERTEXAI=TRUE
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1
```

### 3. Verify Installation

```bash
cd app-agents
python -c "from app.services.adk_config_service import get_adk_config_service; print(get_adk_config_service().validate_configuration())"
```

## Architecture

### ADK Integration Pattern

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Query    │───▶│  Coordinator     │───▶│  Enhanced       │
│                 │    │  Agent           │    │  Research Agent │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                │                        ▼
                       ┌────────▼──────────┐    ┌─────────────────┐
                       │   Platform        │    │   ADK Research  │
                       │   Analyzers       │    │   Agent         │
                       │   (YouTube/IG)    │    │   (LlmAgent)    │
                       └───────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
                                                ┌─────────────────┐
                                                │  ADK Session    │
                                                │  Service        │
                                                └─────────────────┘
```

### Hybrid Intelligence

The integration provides **three levels of intelligence**:

1. **ADK-Enhanced**: Full AI agent with reasoning and tool use
2. **Traditional**: Existing search-based analysis
3. **Fallback**: Mock responses when services unavailable

## Usage Examples

### Basic Research Query

```python
from app.agents.research_agent import ResearchAgent

# Initialize with ADK enhancement (automatically enabled)
research_agent = ResearchAgent(use_adk=True)

# Perform intelligent research
result = await research_agent.search_trends(
    query="social media marketing 2024",
    platforms=["youtube", "instagram"]
)

# Result includes both ADK intelligence and traditional data
print(result['analysis_method'])  # "adk_enhanced"
print(result['adk_analysis']['agent_response'])  # AI insights
```

### Direct ADK Agent Usage

```python
from app.agents.adk_research_agent import ADKResearchAgent

# Use pure ADK agent
adk_agent = ADKResearchAgent()

result = await adk_agent.analyze(
    user_id="researcher_123",
    query="competitor analysis fitness niche",
    platforms=["youtube", "instagram"]
)

# Gets intelligent, conversational analysis
print(result['agent_response'])
```

### Configuration Management

```python
from app.services.adk_config_service import get_adk_config_service

config_service = get_adk_config_service()

# Check ADK status
status = config_service.validate_configuration()
print(f"ADK Ready: {status['adk_ready']}")
print(f"Provider: {status['model_provider']}")

# Get available models
models = config_service.get_available_models()
print(f"Available models: {models}")
```

### Session Management

```python
from app.services.adk_session_service import get_adk_session_service

session_service = get_adk_session_service()

# Get session statistics
stats = await session_service.get_session_stats()
print(f"Active sessions: {stats['active_sessions']}")

# Cleanup expired sessions
cleaned = await session_service.cleanup_expired_sessions()
print(f"Cleaned up {cleaned} expired sessions")
```

## Features and Benefits

### 🤖 **Intelligent Analysis**

- **Natural Language Understanding**: Agents understand complex research queries
- **Contextual Reasoning**: AI considers multiple factors in analysis
- **Dynamic Tool Use**: Agents decide which tools to use based on context

### 📊 **Enhanced Results**

- **Structured Insights**: Clear, actionable recommendations
- **Multi-source Integration**: Combines AI reasoning with real data
- **Rich Context**: Detailed analysis with supporting evidence

### 🛡️ **Robust Architecture**

- **Graceful Degradation**: Falls back gracefully when ADK unavailable
- **Session Management**: Efficient resource usage and cleanup
- **Error Handling**: Comprehensive error recovery

### 🔧 **Developer Experience**

- **Backward Compatible**: Existing code continues to work
- **Easy Configuration**: Simple environment setup
- **Comprehensive Testing**: Full test coverage for reliability

## Configuration Options

### ADK Agent Settings

```python
# Custom agent creation
from app.services.adk_config_service import get_adk_config_service

config_service = get_adk_config_service()

custom_agent = config_service.create_llm_agent(
    name="custom_research_agent",
    description="Specialized agent for market research",
    instruction="You are an expert market researcher...",
    tools=[custom_tool_function],
    model="gemini-1.5-pro"  # Choose your model
)
```

### Available Models

- `gemini-2.0-flash-exp` - Latest experimental (default)
- `gemini-1.5-pro` - Production ready
- `gemini-1.5-flash` - Fast and efficient  
- `gemini-pro` - Legacy stable

### Session Configuration

```python
# Custom session timeout
from app.services.adk_session_service import ADKSessionService

session_service = ADKSessionService()
session_service.session_timeout = timedelta(hours=2)  # Custom timeout
```

## Troubleshooting

### Common Issues

1. **"ADK not available" messages**
   - Check `google-adk` package is installed
   - Verify environment configuration
   - Check API key validity

2. **Authentication errors**
   - Verify API key or GCP authentication
   - Check network connectivity
   - Validate quota limits

3. **Session errors**
   - Check session service initialization
   - Verify agent configuration
   - Review logs for details

### Debugging

```python
# Check ADK status
from app.services.adk_config_service import get_adk_config_service

config = get_adk_config_service()
status = config.validate_configuration()

if status['issues']:
    print("Issues found:")
    for issue in status['issues']:
        print(f"- {issue}")
        
# Get configuration guide
guide = config.get_configuration_guide()
print("Setup guide:", guide['google_ai_studio'])
```

### Performance Monitoring

```python
# Monitor session usage
from app.services.adk_session_service import get_adk_session_service

session_service = get_adk_session_service()
stats = await session_service.get_session_stats()

print(f"""
ADK Session Statistics:
- Total Sessions: {stats['total_sessions']}
- Active Sessions: {stats['active_sessions']} 
- Expired Sessions: {stats['expired_sessions']}
- ADK Available: {stats['adk_available']}
""")
```

## Testing

### Running Tests

```bash
cd app-agents

# Test ADK configuration
python -m pytest tests/test_adk_config_service.py -v

# Test session management
python -m pytest tests/test_adk_session_service.py -v

# Test research integration
python -m pytest tests/test_adk_research_integration.py -v

# Run all ADK tests
python -m pytest tests/test_adk* -v
```

### Test Coverage

- ✅ Configuration service validation
- ✅ Session lifecycle management
- ✅ Agent creation and execution
- ✅ Error handling and fallbacks
- ✅ Integration with existing agents

## Migration Guide

### For Existing Code

**No changes required!** The integration is designed to be backward compatible.

Existing research agent usage continues to work:

```python
# This continues to work as before
research_agent = ResearchAgent()
result = await research_agent.search_trends("query", ["youtube"])

# But now you get enhanced results when ADK is available
if result.get('analysis_method') == 'adk_enhanced':
    print("Enhanced with AI intelligence!")
```

### Enabling ADK Enhancement

To explicitly enable ADK features:

```python
# Enable ADK enhancement
research_agent = ResearchAgent(use_adk=True)

# Or use direct ADK agent
from app.agents.adk_research_agent import ADKResearchAgent
adk_agent = ADKResearchAgent()
```

## Future Enhancements

### Planned Features

1. **Content Planning Agent**: ADK-powered content strategy generation
2. **Multi-Agent Workflows**: Complex agent orchestration
3. **Custom Tool Integration**: Domain-specific tool development
4. **Advanced Analytics**: AI-powered insights dashboard

### Extensibility

The ADK integration is designed for easy extension:

```python
# Create custom ADK agent
def custom_tool(param: str) -> dict:
    """Custom tool function"""
    return {"result": f"Processed: {param}"}

custom_agent = config_service.create_llm_agent(
    name="custom_agent",
    description="My custom agent",
    instruction="Custom instructions...",
    tools=[custom_tool]
)
```

## Support

### Documentation References

- [Google ADK Official Docs](https://google.github.io/adk-docs/)
- [Gemini Models Guide](https://ai.google.dev/models/gemini)
- [Google AI Studio](https://makersuite.google.com/)

### Getting Help

1. Check configuration with validation tools
2. Review logs in `app/core/logging.py`
3. Test with mock mode for debugging
4. Refer to comprehensive test examples

---

The ADK integration brings advanced AI capabilities to your social media agents while maintaining the reliability and performance of the existing system. Start with Google AI Studio for development, then scale to Vertex AI for production!