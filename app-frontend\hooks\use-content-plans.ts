"use client";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@/lib/api-client";

export interface ContentPlan {
  id: string;
  userId: string;
  title: string;
  timeframeStart: Date;
  timeframeEnd: Date;
  platforms: string[];
  totalPosts: number;
  status: 'active' | 'draft' | 'completed';
  createdAt: Date;
  posts: PostBrief[];
}

export interface PostBrief {
  id: string;
  title: string;
  hook: string;
  outline: string;
  cta: string;
  bestTime: Date;
  platform: string;
  contentType: string;
  hashtags: string[];
  status: 'scheduled' | 'draft' | 'published' | 'idea';
}

export function useContentPlans() {
  return useQuery({
    queryKey: ["content-plans"],
    queryFn: async (): Promise<ContentPlan[]> => {
      try {
        const response = await apiClient.get("/planner/plans");
        return response.data.map((plan: any) => ({
          ...plan,
          timeframeStart: new Date(plan.timeframeStart),
          timeframeEnd: new Date(plan.timeframeEnd),
          createdAt: new Date(plan.createdAt),
          posts: plan.posts.map((post: any) => ({
            ...post,
            bestTime: new Date(post.bestTime)
          }))
        }));
      } catch (error) {
        // Return mock data for development
        return [
          {
            id: "plan_1",
            userId: "user_1",
            title: "Q4 Content Strategy",
            timeframeStart: new Date(),
            timeframeEnd: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
            platforms: ["youtube", "instagram", "twitter"],
            totalPosts: 12,
            status: "active" as const,
            createdAt: new Date(),
            posts: [
              {
                id: "post_1",
                title: "How to Build AI Apps in 2024",
                hook: "The future of app development is here!",
                outline: "Introduction → Main concepts → Practical examples → Resources",
                cta: "Subscribe for more AI tutorials!",
                bestTime: new Date(),
                platform: "youtube",
                contentType: "tutorial",
                hashtags: ["#AI", "#WebDev", "#Tutorial"],
                status: "scheduled" as const
              }
            ]
          }
        ];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}