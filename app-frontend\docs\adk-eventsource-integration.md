# ADK EventSource Integration

This document describes the implementation of EventSource integration for real-time streaming with ADK agents.

## Overview

The ADK EventSource integration provides real-time streaming responses from ADK agents using Server-Sent Events (SSE). This implementation satisfies the requirements for:

- Real-time streaming message display (Requirement 2.2)
- Connection error handling and automatic reconnection (Requirement 2.4)
- Graceful error handling and reliability (Requirement 5.3)

## Architecture

### Components

1. **useADKStreaming Hook** (`hooks/use-adk-streaming.ts`)
   - Manages EventSource connections
   - Handles streaming chunks and completion
   - Provides automatic reconnection logic
   - Manages connection state and errors

2. **useADKChat Hook** (`hooks/use-adk-chat.ts`)
   - Integrates streaming with chat functionality
   - Manages message state and query cache
   - Handles both streaming and non-streaming modes
   - Provides retry and error recovery

3. **ADKChatInterface Component** (`components/chat/adk-chat-interface.tsx`)
   - Main chat interface with ADK integration
   - Real-time message display
   - Streaming indicators and status
   - Error handling UI

4. **Supporting Components**
   - `StreamingIndicator` - Shows streaming status and controls
   - `EnhancedMessage` - Displays messages with streaming support
   - `ConnectionStatus` - Shows connection state and errors

### Data Flow

```mermaid
sequenceDiagram
    participant User
    participant ChatInterface
    participant useADKChat
    participant useADKStreaming
    participant EventSource
    participant ADKServer

    User->>ChatInterface: Send message
    ChatInterface->>useADKChat: sendMessage()
    useADKChat->>useADKStreaming: startStreaming()
    useADKStreaming->>ADKServer: POST /chat/send-message-stream
    useADKStreaming->>EventSource: Create connection
    EventSource->>ADKServer: Connect to SSE endpoint
    
    loop Streaming chunks
        ADKServer->>EventSource: Send chunk
        EventSource->>useADKStreaming: onmessage
        useADKStreaming->>useADKChat: onChunk callback
        useADKChat->>ChatInterface: Update streaming message
        ChatInterface->>User: Display partial response
    end
    
    ADKServer->>EventSource: Send completion
    EventSource->>useADKStreaming: onmessage (done: true)
    useADKStreaming->>useADKChat: onComplete callback
    useADKChat->>ChatInterface: Finalize message
```

## Key Features

### Real-time Streaming

- Uses EventSource API for SSE connections
- Displays partial responses as they arrive
- Smooth typing animation during streaming
- Automatic scroll to bottom for new content

### Connection Management

- Automatic connection establishment
- Connection state monitoring
- Graceful handling of connection loss
- Automatic reconnection with exponential backoff

### Error Handling

- Comprehensive error categorization
- User-friendly error messages
- Retry mechanisms for failed operations
- Fallback to non-streaming mode when needed

### User Experience

- Typing indicators during message processing
- Streaming status indicators
- Interrupt capability for long responses
- Connection status display

## Usage

### Basic Usage

```tsx
import { ADKChatInterface } from '@/components/chat/adk-chat-interface';

function ChatPage() {
  return (
    <ADKChatInterface
      userId="user-123"
      sessionId="session-456"
      agentName="content_planner"
      enableStreaming={true}
      onMessageReceived={(message) => console.log('Received:', message)}
      onError={(error) => console.error('Chat error:', error)}
    />
  );
}
```

### Advanced Usage with Custom Handlers

```tsx
import { useADKChat } from '@/hooks/use-adk-chat';
import { EnhancedMessage } from '@/components/chat/enhanced-message';
import { StreamingIndicator } from '@/components/chat/streaming-indicator';

function CustomChatInterface() {
  const {
    messages,
    isStreaming,
    streamingMessage,
    sendMessage,
    interruptStreaming,
    error
  } = useADKChat({
    userId: 'user-123',
    sessionId: 'session-456',
    agentName: 'research_agent',
    enableStreaming: true,
    onMessageReceived: (message) => {
      // Custom message handling
      console.log('New message:', message);
    },
    onError: (error) => {
      // Custom error handling
      console.error('Streaming error:', error);
    }
  });

  return (
    <div>
      {messages.map(message => (
        <EnhancedMessage
          key={message.id}
          message={message}
          showFunctionCalls={true}
        />
      ))}
      
      {streamingMessage && (
        <EnhancedMessage
          message={streamingMessage}
          isStreaming={true}
        />
      )}
      
      <StreamingIndicator
        isStreaming={isStreaming}
        onInterrupt={interruptStreaming}
      />
      
      {/* Input component */}
    </div>
  );
}
```

## Configuration

### Environment Variables

```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

### Feature Flags

The integration supports feature flags for gradual rollout:

```tsx
const [useADK, setUseADK] = useState(true);

if (useADK) {
  return <ADKChatInterface {...props} />;
} else {
  return <LegacyChatInterface {...props} />;
}
```

## Error Handling

### Error Types

1. **Connection Errors** (`STREAM_ERROR`)
   - Network connectivity issues
   - Server unavailability
   - EventSource connection failures

2. **Message Errors** (`SEND_MESSAGE_ERROR`)
   - Failed to send message
   - Invalid message format
   - Authentication failures

3. **Parameter Errors** (`MISSING_PARAMS`)
   - Missing required parameters
   - Invalid user/session IDs

### Recovery Strategies

1. **Automatic Reconnection**
   - Exponential backoff (2s, 4s, 8s)
   - Maximum 3 attempts by default
   - User can manually retry

2. **Fallback Modes**
   - Switch to non-streaming HTTP requests
   - Graceful degradation of features
   - Preserve user messages in cache

3. **User Feedback**
   - Clear error messages
   - Retry buttons
   - Connection status indicators

## Testing

### Unit Tests

- Hook functionality testing
- Error scenario coverage
- Connection state management
- Message handling verification

### Integration Tests

- Component interaction testing
- EventSource mock testing
- Error recovery testing
- User interaction flows

### Example Test

```tsx
it('should handle streaming chunks correctly', async () => {
  const onChunk = jest.fn();
  const { result } = renderHook(() => 
    useADKStreaming({
      userId: 'test-user',
      sessionId: 'test-session',
      onChunk
    })
  );

  await act(async () => {
    await result.current.startStreaming('msg-1', 'Hello');
  });

  // Simulate streaming chunk
  const chunk = {
    content: 'Hello, ',
    done: false,
    message_id: 'msg-1'
  };

  act(() => {
    mockEventSource.simulateMessage(JSON.stringify(chunk));
  });

  expect(onChunk).toHaveBeenCalledWith(chunk);
});
```

## Performance Considerations

### Memory Management

- Automatic cleanup of EventSource connections
- Message cache size limits
- Streaming message state cleanup

### Network Optimization

- Connection pooling for HTTP requests
- Efficient chunk processing
- Minimal re-renders during streaming

### User Experience

- Smooth animations and transitions
- Responsive UI during streaming
- Minimal layout shifts

## Security

### Authentication

- Bearer token authentication
- Secure credential storage
- Token refresh handling

### Data Validation

- Input sanitization
- Message content validation
- Error message sanitization

## Deployment

### Development

```bash
npm run dev
```

### Production

```bash
npm run build
npm start
```

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## Monitoring

### Metrics

- Connection success/failure rates
- Message delivery times
- Error frequencies
- User engagement metrics

### Logging

- Connection events
- Error details
- Performance metrics
- User interactions

## Future Enhancements

1. **WebSocket Fallback**
   - For environments where EventSource is not supported
   - Better bidirectional communication

2. **Message Persistence**
   - Local storage for offline support
   - Message synchronization

3. **Advanced Features**
   - Message reactions
   - File attachments
   - Voice messages

4. **Performance Optimizations**
   - Virtual scrolling for large message lists
   - Message compression
   - Lazy loading of message history