"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Heart, 
  Eye, 
  Calendar,
  Youtube,
  Instagram,
  Twitter,
  BarChart3,
  RefreshCw
} from "lucide-react";
import { useConnectedAccounts } from "@/hooks/use-connected-accounts";
import { usePlatformMetrics } from "@/hooks/use-platform-metrics";
import { PlatformTile } from "./platform-tile";
import { HealthScore } from "./health-score";
import { MetricsChart } from "./metrics-chart";

interface ProfileDashboardProps {
  timeframe?: string;
}

export function ProfileDashboard({ timeframe = "30d" }: ProfileDashboardProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);
  const { data: accounts, isLoading: accountsLoading, refetch: refetchAccounts } = useConnectedAccounts();
  const { data: metrics, isLoading: metricsLoading } = usePlatformMetrics(selectedTimeframe);

  const platformIcons = {
    youtube: Youtube,
    instagram: Instagram,
    twitter: Twitter,
  };

  const platformColors = {
    youtube: "text-red-500 bg-red-50",
    instagram: "text-pink-500 bg-pink-50",
    twitter: "text-blue-500 bg-blue-50",
  };

  if (accountsLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-muted rounded-lg" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-3 bg-muted rounded w-1/2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!accounts || accounts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
          <BarChart3 className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">No Connected Accounts</h3>
        <p className="text-muted-foreground mb-4 max-w-md">
          Connect your social media accounts to view analytics, insights, and performance metrics.
        </p>
        <Button onClick={() => window.location.href = '/connections'}>
          Connect Your First Account
        </Button>
      </div>
    );
  }

  // Calculate overall metrics
  const totalFollowers = accounts.reduce((sum, acc) => sum + (acc.metrics?.followers || 0), 0);
  const avgEngagement = accounts.reduce((sum, acc) => sum + (acc.metrics?.engagement || 0), 0) / accounts.length;
  const avgGrowth = accounts.reduce((sum, acc) => sum + (acc.metrics?.growth?.followers || 0), 0) / accounts.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of your social media performance across all platforms
          </p>
        </div>
        <div className="flex items-center gap-2">
          <select 
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border rounded-md bg-background"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <Button variant="outline" size="sm" onClick={() => refetchAccounts()}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overall Health Score */}
      <HealthScore 
        accounts={accounts}
        timeframe={selectedTimeframe}
      />

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Followers</p>
                <p className="text-2xl font-bold">{totalFollowers.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-500" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              {avgGrowth >= 0 ? (
                <TrendingUp className="w-4 h-4 text-green-500" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500" />
              )}
              <span className={`text-sm ${avgGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {avgGrowth >= 0 ? '+' : ''}{avgGrowth.toFixed(1)}%
              </span>
              <span className="text-sm text-muted-foreground">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg. Engagement</p>
                <p className="text-2xl font-bold">{avgEngagement.toFixed(1)}%</p>
              </div>
              <div className="w-12 h-12 bg-pink-50 rounded-lg flex items-center justify-center">
                <Heart className="w-6 h-6 text-pink-500" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <TrendingUp className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-500">+0.8%</span>
              <span className="text-sm text-muted-foreground">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Connected Platforms</p>
                <p className="text-2xl font-bold">{accounts.length}</p>
              </div>
              <div className="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-green-500" />
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <Badge variant="secondary" className="text-xs">
                {accounts.map(acc => acc.platform).join(', ')}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Platform Tiles */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Platform Performance</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {accounts.map((account) => (
            <PlatformTile
              key={account.id}
              account={account}
              timeframe={selectedTimeframe}
            />
          ))}
        </div>
      </div>

      {/* Analytics Charts */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Growth Trends</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <MetricsChart
            title="Follower Growth"
            accounts={accounts}
            metric="followers"
            timeframe={selectedTimeframe}
          />
          <MetricsChart
            title="Engagement Rate"
            accounts={accounts}
            metric="engagement"
            timeframe={selectedTimeframe}
          />
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {accounts.map((account) => (
              <div key={account.id} className="flex items-center gap-4 p-3 border rounded-lg">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={account.avatar} alt={account.handle} />
                  <AvatarFallback>
                    {(() => {
                      const Icon = platformIcons[account.platform as keyof typeof platformIcons];
                      return Icon ? <Icon className="w-5 h-5" /> : account.handle[0];
                    })()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="font-medium">{account.handle}</p>
                  <p className="text-sm text-muted-foreground">
                    Last synced: {new Date(account.lastSync).toLocaleDateString()}
                  </p>
                </div>
                <Badge variant="outline">
                  {account.platform}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}