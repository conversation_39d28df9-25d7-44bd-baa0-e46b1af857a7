# Social Media Manager - Frontend

Next.js frontend application for the Social Media Manager platform.

## 🛠️ Tech Stack

- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: TanStack Query (React Query)
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React

## 🚀 Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Setup
```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local

# Start development server
npm run dev
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

## 📁 Project Structure

```
app/                    # Next.js App Router pages
├── chat/              # Chat interface pages
├── profile/           # User profile and analytics
├── planner/           # Content planning pages
├── connections/       # Account connection pages
└── api/              # API route handlers

components/            # Reusable React components
├── ui/               # Base UI components (shadcn/ui)
├── layout/           # Layout components
├── chat/             # Chat-specific components
├── connections/      # Connection management
└── profile/          # Profile and analytics

hooks/                # Custom React hooks
├── use-connected-accounts.ts
├── use-chat-history.ts
└── use-send-message.ts

lib/                  # Utility libraries
├── api-client.ts     # API client configuration
└── utils.ts          # General utilities

types/                # TypeScript type definitions
```

## 🎨 UI Components

This project uses shadcn/ui for consistent, accessible UI components:

- **Button** - Primary action buttons
- **Card** - Content containers
- **Avatar** - User profile images
- **Badge** - Status indicators
- **Dialog** - Modal dialogs
- **Dropdown Menu** - Context menus

## 🔌 API Integration

The frontend communicates with the FastAPI backend through:

- **REST API** - Standard CRUD operations
- **Server-Sent Events** - Real-time chat streaming
- **React Query** - Data fetching and caching

## 📱 Responsive Design

- Mobile-first approach
- Tailwind CSS breakpoints
- Adaptive layouts for tablet and desktop

## 🧪 Testing

```bash
# Run component tests
npm run test

# Run E2E tests
npm run test:e2e

# Run tests in watch mode
npm run test:watch
```

## 🚀 Deployment

### Docker
```bash
docker build -t social-media-frontend .
docker run -p 3000:3000 social-media-frontend
```

### Vercel (Recommended)
```bash
npm install -g vercel
vercel --prod
```

### Google Cloud Run
See main README for Cloud Run deployment instructions.

## 🔧 Configuration

### Environment Variables
```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api

# OAuth Configuration
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id

# Feature Flags
NEXT_PUBLIC_ENABLE_TIKTOK=false
NEXT_PUBLIC_ENABLE_TWITTER=false
```

## 🎯 Key Features

### Chat Interface
- Real-time streaming responses
- Message history persistence
- Platform-specific insights
- Quick action buttons

### Account Management
- OAuth flow handling
- Connection status indicators
- Platform metrics display
- Sync controls

### Content Planning
- Calendar view interface
- Post brief management
- Export functionality
- Template system

## 🔍 Code Style

- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Type safety
- **Tailwind** - Utility-first CSS

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [TanStack Query](https://tanstack.com/query/latest)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [shadcn/ui](https://ui.shadcn.com/docs)