"""
Social Media Coordinator Agent - Streamlined ADK Implementation
Optimized implementation with clear workflow delegation and minimal complexity.
Focuses on your core use case: latest news research + social media content creation.
"""

from google.adk.agents import LlmAgent
from google.adk.tools import Agent<PERSON>ool
from typing import Dict, Any, List, Optional
import os
from datetime import datetime
import sys
import importlib.util

# Add app directory to path for proper imports
app_dir = os.path.join(os.path.dirname(__file__), '../app')
sys.path.insert(0, app_dir)

# Import function to load agent from specific folder
def load_agent_from_folder(folder_name: str) -> LlmAgent:
    """Load agent from specific folder using proper import mechanism."""
    agent_path = os.path.join(os.path.dirname(__file__), folder_name)
    agent_file = os.path.join(agent_path, 'agent.py')
    
    if not os.path.exists(agent_file):
        raise ImportError(f"Agent file not found: {agent_file}")
    
    spec = importlib.util.spec_from_file_location(f"{folder_name}_agent", agent_file)
    if spec is None or spec.loader is None:
        raise ImportError(f"Could not create spec for {folder_name}")
    
    agent_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(agent_module)
    
    return agent_module.root_agent

# Load essential agents only (streamlined approach)
try:
    print("📂 Loading streamlined agent set...")
    
    # Core platform specialists
    youtube_analyzer = load_agent_from_folder('youtube_analyzer')
    instagram_analyzer = load_agent_from_folder('instagram_analyzer')
    content_planner = load_agent_from_folder('content_planner')
    
    # News content agent (corrected name - this is your actual news agent)
    news_content_agent = load_agent_from_folder('news_content_agent')
    
    # Google Search agent (for direct research when needed)
    google_search_root_agent = load_agent_from_folder('google_search_root_agent')
    
    print("✅ Successfully loaded streamlined agent set")
    
except Exception as e:
    print(f"⚠️ Warning: Could not import agents, creating fallbacks: {e}")
    # Fallback agents
    youtube_analyzer = LlmAgent(name="youtube_analyzer", model="gemini-2.0-flash", description="YouTube specialist")
    instagram_analyzer = LlmAgent(name="instagram_analyzer", model="gemini-2.0-flash", description="Instagram specialist") 
    content_planner = LlmAgent(name="content_planner", model="gemini-2.0-flash", description="Content planning specialist")
    news_content_agent = LlmAgent(name="news_content_fallback", model="gemini-2.0-flash", description="News content fallback")
    google_search_root_agent = LlmAgent(name="google_search_fallback", model="gemini-2.0-flash", description="Google Search fallback")

# Import essential social media tools
try:
    # Try multiple possible paths for the social media tools
    possible_paths = [
        os.path.join(os.path.dirname(__file__), '../app/tools/social_media_tools.py'),  # Original path
        os.path.join(os.path.dirname(__file__), '../../app/tools/social_media_tools.py'),  # Alternative path
        os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app/tools/social_media_tools.py')  # From project root
    ]
    
    tools_path = None
    for path in possible_paths:
        if os.path.exists(path):
            tools_path = path
            break
    
    if tools_path:
        spec = importlib.util.spec_from_file_location("social_media_tools", tools_path)
        
        if spec is not None and spec.loader is not None:
            social_media_tools = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(social_media_tools)
            
            get_user_connected_platforms = social_media_tools.get_user_connected_platforms
            analyze_message_intent = social_media_tools.analyze_message_intent
            get_trending_topics = social_media_tools.get_trending_topics
            
            print("✅ Successfully imported social media tools")
        else:
            raise ImportError("Could not create spec for social media tools module")
    else:
        raise ImportError(f"Social media tools not found in any of the expected paths: {possible_paths}")
    
except Exception as e:
    print(f"⚠️ Warning: Could not import social media tools, using fallback: {e}")
    # Simplified fallback tools
    def get_user_connected_platforms(user_id: str) -> List[str]:
        return ["instagram", "youtube"]
    
    def analyze_message_intent(message: str) -> Dict[str, Any]:
        return {"status": "success", "intent": "content_creation", "platforms": ["instagram", "youtube"]}
    
    def get_trending_topics(platform: str = "all") -> Dict[str, Any]:
        return {"status": "success", "topics": ["AI trends", "social media tips", "content creation"]}

# Import platform-specific tools from individual agents
# Define fallback functions first to ensure they're always available
def analyze_instagram_performance(user_id: str, timeframe: str = "30d", account_handle: Optional[str] = None) -> Dict[str, Any]:
    return {"status": "fallback", "message": "Instagram analysis not available"}

def get_instagram_trends(niche: str = "general", content_type: str = "all") -> Dict[str, Any]:
    return {"status": "fallback", "message": "Instagram trends not available"}

def optimize_instagram_content(content_type: str, goal: str = "engagement") -> Dict[str, Any]:
    return {"status": "fallback", "message": "Instagram optimization not available"}

def analyze_youtube_performance(user_id: str, timeframe: str = "30d", channel_url: Optional[str] = None) -> Dict[str, Any]:
    return {"status": "fallback", "message": "YouTube analysis not available"}

def get_youtube_trends(niche: str = "general", region: str = "US") -> Dict[str, Any]:
    return {"status": "fallback", "message": "YouTube trends not available"}

def optimize_youtube_content(content_type: str, target_audience: str = "general") -> Dict[str, Any]:
    return {"status": "fallback", "message": "YouTube optimization not available"}

try:
    print("📦 Importing platform-specific tools...")
    
    # Import Instagram tools
    instagram_agent_path = os.path.join(os.path.dirname(__file__), 'instagram_analyzer/agent.py')
    spec = importlib.util.spec_from_file_location("instagram_agent", instagram_agent_path)
    if spec is not None and spec.loader is not None:
        instagram_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(instagram_module)
        
        # Extract Instagram tools (override fallbacks)
        analyze_instagram_performance = instagram_module.analyze_instagram_performance
        get_instagram_trends = instagram_module.get_instagram_trends
        optimize_instagram_content = instagram_module.optimize_instagram_content
        print("✅ Instagram tools imported")
    
    # Import YouTube tools
    youtube_agent_path = os.path.join(os.path.dirname(__file__), 'youtube_analyzer/agent.py')
    spec = importlib.util.spec_from_file_location("youtube_agent", youtube_agent_path)
    if spec is not None and spec.loader is not None:
        youtube_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(youtube_module)
        
        # Extract YouTube tools (override fallbacks)
        analyze_youtube_performance = youtube_module.analyze_youtube_performance
        get_youtube_trends = youtube_module.get_youtube_trends
        optimize_youtube_content = youtube_module.optimize_youtube_content
        print("✅ YouTube tools imported")
        
except Exception as e:
    print(f"⚠️ Warning: Could not import platform tools, using fallbacks: {e}")
    # Fallback functions are already defined above

# Simplified callback functions for essential functionality
def track_tool_usage(tool_context, tool, args):
    """Simple tool usage tracking."""
    tool_name = getattr(tool, 'name', 'unknown_tool')
    tool_context.state[f"temp:{tool_name}_last_used"] = datetime.now().isoformat()
    return None

# Create the streamlined main coordinator agent
root_agent = LlmAgent(
    name="social_media_coordinator",
    model="gemini-2.0-flash",
    description="""Streamlined social media coordinator optimized for your core workflow: 
    latest news research + social media content creation. Features specialized agents for 
    platform-specific optimization and a complete news-to-content workflow.""",
    
    instruction="""You are the Social Media Coordinator with FULL ACCESS to all platform tools and agents.

    **🎯 PRIMARY WORKFLOW (Your Main Use Case):**
    When users ask for latest news + social media content:
    
    1. **Acknowledge & Initiate**: "I'll research the latest [topic] news and create social media content for you..."
    
    2. **Execute Complete Workflow**: Use transfer_to_agent('news_content_coordinator') 
       - This runs the complete sequential pipeline:
       - Step 1: Research latest news using Google Search
       - Step 2: Create content strategy for both platforms  
       - Step 3: Generate Instagram content (captions, hashtags, visuals)
       - Step 4: Generate YouTube content (titles, scripts, thumbnails)
       - Step 5: Present complete package with recommendations
    
    3. **Present Results**: The workflow will provide ready-to-use content for both platforms

    **🔧 SPECIALIZED TASKS & TOOLS:**
    
    **Instagram Analysis & Optimization:**
    - analyze_instagram_performance(user_id, timeframe) - Get detailed Instagram metrics
    - get_instagram_trends(niche, content_type) - Find trending Instagram content
    - optimize_instagram_content(content_type, goal) - Get Instagram optimization tips
    - transfer_to_agent('instagram_analyzer') - For complex Instagram strategy
    
    **YouTube Analysis & Optimization:**
    - analyze_youtube_performance(user_id, timeframe, channel_url) - Get YouTube analytics
    - get_youtube_trends(niche, region) - Find trending YouTube topics
    - optimize_youtube_content(content_type, target_audience) - Get YouTube optimization
    - transfer_to_agent('youtube_analyzer') - For complex YouTube strategy
    
    **News Research & Content Creation:**
    - transfer_to_agent('news_content_coordinator') - Complete news-to-content workflow
    - Use Google Search AgentTool for direct research
    
    **General Social Media:**
    - get_user_connected_platforms(user_id) - Check user's connected platforms
    - analyze_message_intent(message) - Understand user's content goals
    - get_trending_topics(platform) - Get trending topics across platforms
    - transfer_to_agent('content_planner') - For strategic content planning

    **💬 COMMUNICATION STYLE:**
    - Be enthusiastic about helping with content creation
    - Explain which tools/agents you're using and why
    - Present results in organized, scannable format
    - Always include next steps and strategic recommendations
    - Use emojis and formatting for engagement

    **🎪 EXAMPLE INTERACTIONS:**
    
    User: "Latest Bigg Boss news for Instagram and YouTube"
    You: "I'll research the latest Bigg Boss news and create engaging content for both platforms! Let me use my complete workflow..."
    → transfer_to_agent('news_content_coordinator')
    → Present complete content package
    
    User: "Analyze my Instagram performance"
    You: "I'll analyze your Instagram performance using my specialized tools..."
    → analyze_instagram_performance(user_id="user123", timeframe="30d")
    → Present detailed analysis and recommendations
    
    User: "What's trending on YouTube for tech content?"
    You: "Let me check the latest YouTube trends for tech content..."
    → get_youtube_trends(niche="tech", region="US")
    → Present trending opportunities and content ideas

    **🚀 KEY STRENGTHS:**
    - Direct access to ALL platform-specific tools
    - Complete news-to-content workflow in one request
    - Platform-optimized content creation and analysis
    - Real-time news research with Google Search
    - Ready-to-post scripts and strategies
    - Professional content package delivery

    You have FULL CONNECTIVITY to all agents and their tools. Use them strategically to provide comprehensive social media solutions.""",
    
    # Complete agent hierarchy with all specialists
    sub_agents=[
        news_content_agent,   # Complete news-to-content workflow
        youtube_analyzer,     # Platform specialist
        instagram_analyzer,   # Platform specialist  
        content_planner      # Strategic planning
    ],
    
    # ALL TOOLS - Direct access to platform-specific functionality
    tools=[
        # Core social media tools
        get_user_connected_platforms,
        analyze_message_intent,
        get_trending_topics,
        
        # Instagram-specific tools (now directly accessible)
        analyze_instagram_performance,
        get_instagram_trends,
        optimize_instagram_content,
        
        # YouTube-specific tools (now directly accessible)
        analyze_youtube_performance,
        get_youtube_trends,
        optimize_youtube_content,
        
        # AgentTools for complex workflows
        AgentTool(agent=google_search_root_agent),  # Direct Google Search access
        AgentTool(agent=news_content_agent),        # News workflow as tool
        AgentTool(agent=youtube_analyzer),          # YouTube specialist as tool
        AgentTool(agent=instagram_analyzer)         # Instagram specialist as tool
    ],
    
    # Simple callback for usage tracking
    before_tool_callback=track_tool_usage
)



# Verify streamlined agent configuration
if __name__ == "__main__":
    print(f"✅ Streamlined Social Media Coordinator loaded successfully")
    print(f"   - Agent name: {root_agent.name}")
    print(f"   - Model: {root_agent.model}")
    print(f"   - Sub-agents: {len(root_agent.sub_agents)}")
    for i, sub_agent in enumerate(root_agent.sub_agents, 1):
        print(f"     {i}. {sub_agent.name}")
    print(f"   - Tools: {len(root_agent.tools)}")
    print(f"   - Primary workflow: news_content_coordinator (complete pipeline)")
    print(f"   - Platform tools: Instagram ({len([t for t in root_agent.tools if 'instagram' in str(t).lower()])}), YouTube ({len([t for t in root_agent.tools if 'youtube' in str(t).lower()])})")
    print(f"   - Optimized for: Latest news → Instagram & YouTube content")
    print(f"   - Description: {root_agent.description[:100]}...")