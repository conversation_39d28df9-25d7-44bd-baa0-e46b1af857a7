-- User Engagement Trends Over Time
-- Analyzes user engagement patterns and growth over the last 30 days

WITH daily_metrics AS (
  SELECT 
    date,
    user_id,
    total_followers,
    total_engagement,
    avg_engagement_rate,
    content_plans_created,
    posts_scheduled,
    chat_messages,
    LAG(total_followers) OVER (PARTITION BY user_id ORDER BY date) AS prev_followers,
    LAG(total_engagement) OVER (PARTITION BY user_id ORDER BY date) AS prev_engagement
  FROM `{project_id}.{dataset_id}.user_metrics_daily`
  WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
),

growth_metrics AS (
  SELECT
    date,
    user_id,
    total_followers,
    total_engagement,
    avg_engagement_rate,
    content_plans_created,
    posts_scheduled,
    chat_messages,
    CASE 
      WHEN prev_followers > 0 
      THEN ROUND(((total_followers - prev_followers) / prev_followers) * 100, 2)
      ELSE 0 
    END AS follower_growth_rate,
    CASE 
      WHEN prev_engagement > 0 
      THEN ROUND(((total_engagement - prev_engagement) / prev_engagement) * 100, 2)
      ELSE 0 
    END AS engagement_growth_rate
  FROM daily_metrics
)

SELECT
  date,
  user_id,
  total_followers,
  total_engagement,
  ROUND(avg_engagement_rate, 4) AS avg_engagement_rate,
  content_plans_created,
  posts_scheduled,
  chat_messages,
  follower_growth_rate,
  engagement_growth_rate,
  
  -- 7-day moving averages
  ROUND(AVG(avg_engagement_rate) OVER (
    PARTITION BY user_id 
    ORDER BY date 
    ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
  ), 4) AS engagement_rate_7d_avg,
  
  ROUND(AVG(follower_growth_rate) OVER (
    PARTITION BY user_id 
    ORDER BY date 
    ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
  ), 2) AS follower_growth_7d_avg,
  
  -- Activity scores
  (content_plans_created + posts_scheduled + (chat_messages / 10)) AS daily_activity_score

FROM growth_metrics
ORDER BY user_id, date DESC;