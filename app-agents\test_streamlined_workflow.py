#!/usr/bin/env python3
"""
Test Script for Streamlined Social Media Agent Workflow
Tests the complete news research → content creation pipeline.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_agent_loading():
    """Test that all agents load correctly."""
    print("🧪 TESTING AGENT LOADING")
    print("=" * 50)
    
    try:
        # Test main coordinator
        from agents.agent import root_agent
        print(f"✅ Main Coordinator: {root_agent.name}")
        print(f"   - Sub-agents: {len(root_agent.sub_agents)}")
        for sub_agent in root_agent.sub_agents:
            print(f"     • {sub_agent.name}")
        
        # Test news workflow agent specifically
        from agents.news_workflow_agent.agent import root_agent as workflow_agent
        print(f"✅ News Workflow: {workflow_agent.name}")
        print(f"   - Workflow steps: {len(workflow_agent.sub_agents)}")
        for i, step in enumerate(workflow_agent.sub_agents, 1):
            print(f"     {i}. {step.name}")
        
        # Test platform agents
        from agents.instagram_analyzer.agent import root_agent as instagram_agent
        from agents.youtube_analyzer.agent import root_agent as youtube_agent
        print(f"✅ Instagram Agent: {instagram_agent.name}")
        print(f"✅ YouTube Agent: {youtube_agent.name}")
        
        # Test Google Search agent
        from agents.google_search_root_agent.agent import root_agent as search_agent
        print(f"✅ Google Search Agent: {search_agent.name}")
        print(f"   - Tools: {len(search_agent.tools)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent loading failed: {e}")
        return False

def test_workflow_structure():
    """Test the workflow structure and agent relationships."""
    print("\n🏗️ TESTING WORKFLOW STRUCTURE")
    print("=" * 50)
    
    try:
        from agents.news_workflow_agent.agent import root_agent as workflow_agent
        
        expected_steps = [
            "news_researcher",
            "content_strategist", 
            "instagram_content_creator",
            "youtube_content_creator",
            "final_content_coordinator"
        ]
        
        actual_steps = [agent.name for agent in workflow_agent.sub_agents]
        
        print("Expected workflow steps:")
        for i, step in enumerate(expected_steps, 1):
            print(f"  {i}. {step}")
        
        print("\nActual workflow steps:")
        for i, step in enumerate(actual_steps, 1):
            print(f"  {i}. {step}")
        
        if actual_steps == expected_steps:
            print("✅ Workflow structure is correct!")
            return True
        else:
            print("❌ Workflow structure mismatch!")
            return False
            
    except Exception as e:
        print(f"❌ Workflow structure test failed: {e}")
        return False

def test_google_search_integration():
    """Test Google Search tool integration."""
    print("\n🔍 TESTING GOOGLE SEARCH INTEGRATION")
    print("=" * 50)
    
    try:
        from agents.google_search_root_agent.agent import root_agent as search_agent
        from google.adk.tools import google_search
        
        # Check if google_search tool is available
        has_google_search = any(
            tool == google_search or getattr(tool, '__name__', '') == 'google_search'
            for tool in search_agent.tools
        )
        
        if has_google_search:
            print("✅ Google Search tool is properly integrated")
            print("✅ Ready for real-time news research")
            return True
        else:
            print("❌ Google Search tool not found in search agent")
            return False
            
    except Exception as e:
        print(f"❌ Google Search integration test failed: {e}")
        return False

def test_agent_tools_integration():
    """Test AgentTool integration in main coordinator."""
    print("\n🔧 TESTING AGENT TOOLS INTEGRATION")
    print("=" * 50)
    
    try:
        from agents.agent import root_agent
        from google.adk.tools import AgentTool
        
        # Count AgentTools
        agent_tools = [tool for tool in root_agent.tools if isinstance(tool, AgentTool)]
        
        print(f"AgentTools found: {len(agent_tools)}")
        for tool in agent_tools:
            agent_name = getattr(tool.agent, 'name', 'unknown')
            print(f"  • AgentTool wrapping: {agent_name}")
        
        if len(agent_tools) > 0:
            print("✅ AgentTools properly integrated")
            return True
        else:
            print("❌ No AgentTools found")
            return False
            
    except Exception as e:
        print(f"❌ AgentTools integration test failed: {e}")
        return False

def demonstrate_workflow():
    """Demonstrate the complete workflow conceptually."""
    print("\n🎯 WORKFLOW DEMONSTRATION")
    print("=" * 50)
    
    print("User Request: 'Latest Bigg Boss news for Instagram and YouTube'")
    print()
    print("Coordinator Response:")
    print("'I'll research the latest Bigg Boss news and create content for both platforms!'")
    print()
    print("Workflow Execution:")
    print("1. 🔍 News Researcher")
    print("   - Uses Google Search to find latest Bigg Boss news")
    print("   - Gathers headlines, key facts, trending aspects")
    print("   - Saves research to state['research_data']")
    print()
    print("2. 📋 Content Strategist") 
    print("   - Analyzes research for social media potential")
    print("   - Creates platform-specific strategies")
    print("   - Saves strategy to state['content_strategy']")
    print()
    print("3. 📱 Instagram Content Creator")
    print("   - Creates Instagram post with engaging caption")
    print("   - Selects optimal hashtags and visual suggestions")
    print("   - Saves to state['instagram_content']")
    print()
    print("4. 🎥 YouTube Content Creator")
    print("   - Creates YouTube video title and script")
    print("   - Optimizes for SEO and engagement")
    print("   - Saves to state['youtube_content']")
    print()
    print("5. 📦 Final Coordinator")
    print("   - Compiles complete content package")
    print("   - Presents ready-to-use scripts")
    print("   - Provides strategic recommendations")
    print()
    print("✅ Complete package delivered with:")
    print("   • Latest news research summary")
    print("   • Ready-to-post Instagram content")
    print("   • Ready-to-produce YouTube content")
    print("   • Strategic posting recommendations")

def show_architecture_summary():
    """Show the final streamlined architecture."""
    print("\n🏛️ STREAMLINED ARCHITECTURE SUMMARY")
    print("=" * 50)
    
    print("📋 MAIN COORDINATOR")
    print("├── 🎯 News Content Workflow (PRIMARY)")
    print("│   ├── 1. News Researcher (Google Search)")
    print("│   ├── 2. Content Strategist")
    print("│   ├── 3. Instagram Content Creator")
    print("│   ├── 4. YouTube Content Creator")
    print("│   └── 5. Final Coordinator")
    print("├── 📱 Instagram Analyzer (Platform Specialist)")
    print("├── 🎥 YouTube Analyzer (Platform Specialist)")
    print("├── 📅 Content Planner (Strategic Planning)")
    print("└── 🔍 Google Search Agent (Direct Research)")
    print()
    print("🗑️ REMOVED REDUNDANCIES:")
    print("❌ Duplicate google_search_agent")
    print("❌ Redundant research_agent")
    print("❌ Complex news_content_agent")
    print("❌ Unnecessary research_analysis_agent")
    print("❌ Redundant research_tool function")
    print()
    print("✅ OPTIMIZATIONS:")
    print("• Single sequential workflow for main use case")
    print("• Clear state passing between workflow steps")
    print("• Platform specialists for specific tasks")
    print("• Minimal tool complexity")
    print("• Robust error handling")

async def main():
    """Run all tests and demonstrations."""
    print("🚀 STREAMLINED SOCIAL MEDIA AGENT SYSTEM TEST")
    print("=" * 60)
    
    # Run tests
    tests = [
        test_agent_loading,
        test_workflow_structure,
        test_google_search_integration,
        test_agent_tools_integration
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # Show demonstrations
    demonstrate_workflow()
    show_architecture_summary()
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Your streamlined agent system is ready!")
        print()
        print("🎯 READY FOR USE:")
        print("Ask: 'Latest [topic] news for Instagram and YouTube'")
        print("Get: Complete content package with ready scripts!")
    else:
        print("⚠️ Some tests failed. Check the output above.")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(main())