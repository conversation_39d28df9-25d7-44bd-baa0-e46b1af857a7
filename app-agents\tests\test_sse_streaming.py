"""
Test Server-Sent Events Streaming Implementation

This test module verifies the SSE streaming functionality for ADK integration,
including Event object parsing, transformation, and streaming response handling.

Requirements covered: 2.1, 2.3, 2.4
"""

import pytest
import json
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from typing import AsyncGenerator

from app.services.adk_service import ADKService, ADKServiceError
from app.services.event_transformer import ADKEventTransformer
from app.models.adk_models import (
    ADKRunAgentRequest, ADKMessage, ADKEvent, ADKEventContent, 
    ADKContentPart, StreamingChunk, MessageRole
)


class TestSSEStreaming:
    """Test SSE streaming functionality"""
    
    @pytest.fixture
    def adk_service(self):
        """Create ADK service for testing"""
        return ADKService(base_url="http://localhost:8001")
    
    @pytest.fixture
    def sample_adk_request(self):
        """Create sample ADK request"""
        return ADKRunAgentRequest(
            app_name="test_agent",
            user_id="test_user",
            session_id="test_session",
            new_message=ADKMessage.from_text("Hello, agent!"),
            streaming=True
        )
    
    @pytest.fixture
    def sample_adk_events(self):
        """Create sample ADK events for testing"""
        return [
            {
                "author": "test_agent",
                "invocation_id": "inv_123",
                "content": {
                    "role": "model",
                    "parts": [{"text": "Hello! "}]
                },
                "interrupted": False,
                "turn_complete": False
            },
            {
                "author": "test_agent", 
                "invocation_id": "inv_123",
                "content": {
                    "role": "model",
                    "parts": [{"text": "How can I help you today?"}]
                },
                "interrupted": False,
                "turn_complete": True
            }
        ]
    
    @pytest.mark.asyncio
    async def test_event_transformation_basic(self):
        """Test basic ADK Event to StreamingChunk transformation"""
        # Create sample ADK event
        adk_event = ADKEvent(
            author="test_agent",
            invocation_id="inv_123",
            content=ADKEventContent(
                role=MessageRole.MODEL,
                parts=[ADKContentPart(text="Hello, world!")]
            ),
            interrupted=False,
            turn_complete=False
        )
        
        # Transform event
        transformer = ADKEventTransformer()
        chunk = transformer.transform_event_to_chunk(adk_event)
        
        # Verify transformation
        assert chunk.content == "Hello, world!"
        assert chunk.done == False
        assert chunk.message_id == "inv_123"
        assert chunk.metadata["author"] == "test_agent"
        assert chunk.metadata["interrupted"] == False
        assert chunk.metadata["turn_complete"] == False
    
    @pytest.mark.asyncio
    async def test_event_transformation_with_function_calls(self):
        """Test transformation of events with function calls"""
        # Create event with function call
        adk_event = ADKEvent(
            author="test_agent",
            invocation_id="inv_456",
            content=ADKEventContent(
                role=MessageRole.MODEL,
                parts=[
                    ADKContentPart(text="Let me search for that information."),
                    ADKContentPart(function_call={
                        "name": "google_search",
                        "args": {"query": "test query"}
                    })
                ]
            ),
            interrupted=False,
            turn_complete=False
        )
        
        # Transform event
        transformer = ADKEventTransformer()
        chunk = transformer.transform_event_to_chunk(adk_event)
        
        # Verify transformation
        assert chunk.content == "Let me search for that information."
        assert len(chunk.metadata["function_calls"]) == 1
        assert chunk.metadata["function_calls"][0]["name"] == "google_search"
        assert chunk.metadata["has_function_calls"] == True
    
    @pytest.mark.asyncio
    async def test_event_transformation_interrupted(self):
        """Test transformation of interrupted events"""
        # Create interrupted event
        adk_event = ADKEvent(
            author="test_agent",
            invocation_id="inv_789",
            content=ADKEventContent(
                role=MessageRole.MODEL,
                parts=[ADKContentPart(text="This response was")]
            ),
            interrupted=True,
            turn_complete=True
        )
        
        # Transform event
        transformer = ADKEventTransformer()
        chunk = transformer.transform_event_to_chunk(adk_event)
        
        # Verify transformation
        assert "[Interrupted]" in chunk.content
        assert chunk.done == True
        assert chunk.metadata["interrupted"] == True
    
    @pytest.mark.asyncio
    async def test_sse_stream_parsing(self, adk_service, sample_adk_request, sample_adk_events):
        """Test SSE stream parsing and event processing"""
        
        # Mock SSE response lines
        sse_lines = [
            "data: " + json.dumps(sample_adk_events[0]),
            "",
            "data: " + json.dumps(sample_adk_events[1]),
            ""
        ]
        
        # Mock HTTP streaming response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.aiter_lines.return_value = sse_lines
        
        # Mock streaming client with proper async context manager
        mock_stream_context = AsyncMock()
        mock_stream_context.__aenter__.return_value = mock_response
        mock_stream_context.__aexit__.return_value = None
        
        mock_client = AsyncMock()
        mock_client.stream.return_value = mock_stream_context
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        # Patch httpx.AsyncClient
        with patch('httpx.AsyncClient', return_value=mock_client):
            
            # Test streaming
            chunks = []
            async for chunk in adk_service.stream_message(sample_adk_request):
                chunks.append(chunk)
            
            # Verify results
            assert len(chunks) == 2
            assert chunks[0].content == "Hello! "
            assert chunks[0].done == False
            assert chunks[1].content == "How can I help you today?"
            assert chunks[1].done == True
    
    @pytest.mark.asyncio
    async def test_sse_stream_error_handling(self, adk_service, sample_adk_request):
        """Test error handling in SSE streaming"""
        
        # Mock HTTP error response
        mock_response = AsyncMock()
        mock_response.status_code = 404
        mock_response.aread.return_value = b"Agent not found"
        
        mock_stream_context = AsyncMock()
        mock_stream_context.__aenter__.return_value = mock_response
        mock_stream_context.__aexit__.return_value = None
        
        mock_client = AsyncMock()
        mock_client.stream.return_value = mock_stream_context
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        # Patch httpx.AsyncClient
        with patch('httpx.AsyncClient', return_value=mock_client):
            
            # Test error handling
            with pytest.raises(ADKServiceError):
                chunks = []
                async for chunk in adk_service.stream_message(sample_adk_request):
                    chunks.append(chunk)
    
    @pytest.mark.asyncio
    async def test_sse_stream_malformed_json(self, adk_service, sample_adk_request):
        """Test handling of malformed JSON in SSE stream"""
        
        # Mock SSE response with malformed JSON
        sse_lines = [
            "data: {invalid json}",
            "",
            "data: " + json.dumps({
                "author": "test_agent",
                "content": {"role": "model", "parts": [{"text": "Valid response"}]},
                "turn_complete": True
            }),
            ""
        ]
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.aiter_lines.return_value = sse_lines
        
        mock_stream_context = AsyncMock()
        mock_stream_context.__aenter__.return_value = mock_response
        mock_stream_context.__aexit__.return_value = None
        
        mock_client = AsyncMock()
        mock_client.stream.return_value = mock_stream_context
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        # Patch httpx.AsyncClient
        with patch('httpx.AsyncClient', return_value=mock_client):
            
            # Test streaming with malformed JSON
            chunks = []
            async for chunk in adk_service.stream_message(sample_adk_request):
                chunks.append(chunk)
            
            # Should skip malformed JSON and process valid event
            assert len(chunks) == 1
            assert chunks[0].content == "Valid response"
            assert chunks[0].done == True
    
    @pytest.mark.asyncio
    async def test_sse_stream_empty_content(self, adk_service, sample_adk_request):
        """Test handling of events with empty content"""
        
        # Mock SSE response with empty content event
        sse_lines = [
            "data: " + json.dumps({
                "author": "test_agent",
                "invocation_id": "inv_empty",
                "content": None,
                "turn_complete": False
            }),
            "",
            "data: " + json.dumps({
                "author": "test_agent",
                "content": {"role": "model", "parts": [{"text": "Actual response"}]},
                "turn_complete": True
            }),
            ""
        ]
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.aiter_lines.return_value = sse_lines
        
        mock_stream_context = AsyncMock()
        mock_stream_context.__aenter__.return_value = mock_response
        mock_stream_context.__aexit__.return_value = None
        
        mock_client = AsyncMock()
        mock_client.stream.return_value = mock_stream_context
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        # Patch httpx.AsyncClient
        with patch('httpx.AsyncClient', return_value=mock_client):
            
            # Test streaming
            chunks = []
            async for chunk in adk_service.stream_message(sample_adk_request):
                chunks.append(chunk)
            
            # Should handle empty content gracefully
            assert len(chunks) == 2
            assert chunks[0].content == ""  # Empty content
            assert chunks[1].content == "Actual response"
    
    @pytest.mark.asyncio
    async def test_sse_stream_long_running_tools(self, adk_service, sample_adk_request):
        """Test handling of long-running tools in SSE stream"""
        
        # Mock SSE response with long-running tools
        sse_lines = [
            "data: " + json.dumps({
                "author": "test_agent",
                "content": {"role": "model", "parts": [{"text": "Starting search..."}]},
                "long_running_tool_ids": ["search_tool_123"],
                "turn_complete": False
            }),
            "",
            "data: " + json.dumps({
                "author": "test_agent",
                "content": {"role": "model", "parts": [{"text": "Search completed!"}]},
                "long_running_tool_ids": [],
                "turn_complete": True
            }),
            ""
        ]
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.aiter_lines.return_value = sse_lines
        
        mock_stream_context = AsyncMock()
        mock_stream_context.__aenter__.return_value = mock_response
        mock_stream_context.__aexit__.return_value = None
        
        mock_client = AsyncMock()
        mock_client.stream.return_value = mock_stream_context
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        # Patch httpx.AsyncClient
        with patch('httpx.AsyncClient', return_value=mock_client):
            
            # Test streaming
            chunks = []
            async for chunk in adk_service.stream_message(sample_adk_request):
                chunks.append(chunk)
            
            # Verify long-running tool handling
            assert len(chunks) == 2
            assert chunks[0].metadata["has_long_running_tools"] == True
            assert "search_tool_123" in chunks[0].metadata["long_running_tool_ids"]
            assert chunks[1].metadata["has_long_running_tools"] == False
    
    @pytest.mark.asyncio
    async def test_comprehensive_sse_event_handling(self, adk_service, sample_adk_request):
        """Test comprehensive SSE event handling including all event types"""
        
        # Mock comprehensive SSE response with various event types
        sse_lines = [
            "event: start",
            "data: " + json.dumps({
                "author": "test_agent",
                "invocation_id": "inv_comprehensive",
                "content": {"role": "model", "parts": [{"text": "Starting comprehensive test..."}]},
                "turn_complete": False,
                "interrupted": False
            }),
            "",
            "id: event_1",
            "data: " + json.dumps({
                "author": "test_agent",
                "invocation_id": "inv_comprehensive",
                "content": {
                    "role": "model", 
                    "parts": [
                        {"text": "Calling function: "},
                        {"function_call": {"name": "test_function", "args": {"param": "value"}}}
                    ]
                },
                "turn_complete": False,
                "interrupted": False
            }),
            "",
            "data: " + json.dumps({
                "author": "test_agent",
                "invocation_id": "inv_comprehensive",
                "content": {
                    "role": "model",
                    "parts": [
                        {"function_response": {"result": "Function executed successfully"}},
                        {"text": " Function completed!"}
                    ]
                },
                "turn_complete": True,
                "interrupted": False
            }),
            "",
            "event: complete"
        ]
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.aiter_lines.return_value = sse_lines
        
        mock_stream_context = AsyncMock()
        mock_stream_context.__aenter__.return_value = mock_response
        mock_stream_context.__aexit__.return_value = None
        
        mock_client = AsyncMock()
        mock_client.stream.return_value = mock_stream_context
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        # Patch httpx.AsyncClient
        with patch('httpx.AsyncClient', return_value=mock_client):
            
            # Test streaming
            chunks = []
            async for chunk in adk_service.stream_message(sample_adk_request):
                chunks.append(chunk)
            
            # Verify comprehensive event handling
            assert len(chunks) == 3
            
            # First chunk - basic text
            assert chunks[0].content == "Starting comprehensive test..."
            assert chunks[0].done == False
            assert chunks[0].metadata["turn_complete"] == False
            
            # Second chunk - function call
            assert "Calling function: " in chunks[1].content
            assert chunks[1].metadata["has_function_calls"] == True
            assert len(chunks[1].metadata["function_calls"]) == 1
            assert chunks[1].metadata["function_calls"][0]["name"] == "test_function"
            
            # Third chunk - function response and completion
            assert " Function completed!" in chunks[2].content
            assert chunks[2].metadata["has_function_responses"] == True
            assert chunks[2].done == True
            assert chunks[2].metadata["turn_complete"] == True
    
    @pytest.mark.asyncio
    async def test_sse_stream_interruption_handling(self, adk_service, sample_adk_request):
        """Test proper handling of interrupted SSE streams"""
        
        # Mock SSE response with interruption
        sse_lines = [
            "data: " + json.dumps({
                "author": "test_agent",
                "invocation_id": "inv_interrupted",
                "content": {"role": "model", "parts": [{"text": "This response will be"}]},
                "turn_complete": False,
                "interrupted": False
            }),
            "",
            "data: " + json.dumps({
                "author": "test_agent",
                "invocation_id": "inv_interrupted",
                "content": {"role": "model", "parts": [{"text": " interrupted"}]},
                "turn_complete": True,
                "interrupted": True
            }),
            ""
        ]
        
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.aiter_lines.return_value = sse_lines
        
        mock_stream_context = AsyncMock()
        mock_stream_context.__aenter__.return_value = mock_response
        mock_stream_context.__aexit__.return_value = None
        
        mock_client = AsyncMock()
        mock_client.stream.return_value = mock_stream_context
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        # Patch httpx.AsyncClient
        with patch('httpx.AsyncClient', return_value=mock_client):
            
            # Test streaming
            chunks = []
            async for chunk in adk_service.stream_message(sample_adk_request):
                chunks.append(chunk)
            
            # Verify interruption handling
            assert len(chunks) == 2
            assert chunks[0].content == "This response will be"
            assert chunks[0].metadata["interrupted"] == False
            assert chunks[1].content == " interrupted [Interrupted]"
            assert chunks[1].metadata["interrupted"] == True
            assert chunks[1].done == True


class TestEventTransformerEdgeCases:
    """Test edge cases in event transformation"""
    
    def test_transform_event_with_multiple_content_types(self):
        """Test transformation of events with multiple content types"""
        adk_event = ADKEvent(
            author="test_agent",
            invocation_id="inv_multi",
            content=ADKEventContent(
                role=MessageRole.MODEL,
                parts=[
                    ADKContentPart(text="Here's the result: "),
                    ADKContentPart(function_call={"name": "search", "args": {}}),
                    ADKContentPart(function_response={"result": "success"}),
                    ADKContentPart(text=" Done!")
                ]
            ),
            turn_complete=True
        )
        
        transformer = ADKEventTransformer()
        chunk = transformer.transform_event_to_chunk(adk_event)
        
        # Should concatenate text parts
        assert chunk.content == "Here's the result:  Done!"
        assert chunk.metadata["has_function_calls"] == True
        assert chunk.metadata["has_function_responses"] == True
        assert len(chunk.metadata["function_calls"]) == 1
        assert len(chunk.metadata["function_responses"]) == 1
    
    def test_transform_event_error_handling(self):
        """Test error handling in event transformation"""
        # Create malformed event (this should be handled gracefully)
        malformed_event = {"invalid": "structure"}
        
        transformer = ADKEventTransformer()
        
        # This should not raise an exception but return an error chunk
        try:
            # Convert dict to ADKEvent (this might fail)
            adk_event = ADKEvent(**malformed_event)
            chunk = transformer.transform_event_to_chunk(adk_event)
        except Exception:
            # If parsing fails, transformer should handle it gracefully
            # For this test, we'll create a minimal valid event
            adk_event = ADKEvent()
            chunk = transformer.transform_event_to_chunk(adk_event)
        
        # Should return a valid chunk even for malformed input
        assert isinstance(chunk, StreamingChunk)
        assert isinstance(chunk.content, str)
        assert isinstance(chunk.done, bool)
    
    def test_extract_content_types(self):
        """Test content type extraction"""
        adk_event = ADKEvent(
            content=ADKEventContent(
                role=MessageRole.MODEL,
                parts=[
                    ADKContentPart(text="Hello"),
                    ADKContentPart(function_call={"name": "test"}),
                    ADKContentPart(inline_data={"mime_type": "image/png"})
                ]
            )
        )
        
        transformer = ADKEventTransformer()
        content_types = transformer.extract_content_types(adk_event)
        
        assert "text" in content_types
        assert "function_call" in content_types
        assert "inline_data" in content_types
        assert len(content_types) == 3
    
    def test_tool_usage_summary(self):
        """Test tool usage summary extraction"""
        adk_event = ADKEvent(
            invocation_id="inv_tools",
            content=ADKEventContent(
                role=MessageRole.MODEL,
                parts=[
                    ADKContentPart(function_call={"name": "search_tool", "args": {}}),
                    ADKContentPart(function_response={"result": "success"}),
                    ADKContentPart(function_call={"name": "format_tool", "args": {}}),
                    ADKContentPart(function_response={"error": "failed"})
                ]
            ),
            long_running_tool_ids=["search_tool"]
        )
        
        transformer = ADKEventTransformer()
        summary = transformer.get_tool_usage_summary(adk_event)
        
        assert len(summary["tools_used"]) == 2
        assert "search_tool" in summary["tools_used"]
        assert "format_tool" in summary["tools_used"]
        assert summary["total_function_calls"] == 2
        assert summary["total_function_responses"] == 2
        assert summary["successful_responses"] == 1
        assert summary["failed_responses"] == 1
        assert summary["has_active_tools"] == True
        assert "search_tool" in summary["long_running_tools"]


if __name__ == "__main__":
    pytest.main([__file__])