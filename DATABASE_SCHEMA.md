# Firestore Database Schema

This document defines the complete database schema for the Social Media Manager application.

## Overview

The application uses Google Cloud Firestore as its primary NoSQL database. The schema is designed to support:
- Multi-platform social media account management
- AI-powered content planning and optimization
- Real-time chat interactions with AI agents
- Historical analytics and performance tracking
- User authentication and authorization

## Collections Structure

### 1. Users Collection (`users`)

Primary collection for user profiles and authentication data.

**Document ID**: User UUID
**Security**: Users can only read/write their own documents

```typescript
interface User {
  id: string;                    // User UUID (matches document ID)
  name: string;                  // Full display name
  email: string;                 // Email address (unique)
  bio?: string;                  // User bio/description
  avatar?: string;               // Profile image URL
  industry?: string;             // User industry category
  created_at: Timestamp;         // Account creation time
  last_active: Timestamp;        // Last login/activity time
  subscription_plan: 'free' | 'pro' | 'enterprise';
  preferences: {
    timezone?: string;
    notification_email?: boolean;
    notification_push?: boolean;
    default_posting_times?: string[];
    content_tone?: string;
  };
  status: 'active' | 'suspended' | 'deleted';
}
```

**Key Indexes**:
- `email` (ascending) - for login lookups
- `subscription_plan` + `created_at` (desc) - for subscription analytics
- `industry` + `last_active` (desc) - for user segmentation

### 2. Connected Accounts Collection (`connected_accounts`)

Stores social media platform connections for each user.

**Document ID**: Account UUID
**Security**: Users can only access accounts they own

```typescript
interface ConnectedAccount {
  id: string;                    // Account UUID (matches document ID)
  user_id: string;               // Reference to users collection
  platform: 'youtube' | 'instagram' | 'twitter';
  handle: string;                // Platform username/handle
  display_name: string;          // Platform display name
  avatar?: string;               // Platform profile image URL
  connected_at: Timestamp;       // Connection establishment time
  last_sync: Timestamp;          // Last data synchronization
  status: 'active' | 'error' | 'revoked';
  permissions: string[];         // Granted OAuth scopes
  metrics: {
    followers?: number;
    following?: number;
    posts_count?: number;
    engagement_rate?: number;
    last_post_date?: Timestamp;
  };
  account_info: {
    platform_id?: string;       // Platform-specific account ID
    verified?: boolean;          // Verification status
    business_account?: boolean;  // Business/creator account flag
    category?: string;           // Account category
  };
  oauth_token: string | null;    // Encrypted OAuth access token
  oauth_refresh_token: string | null; // Encrypted OAuth refresh token
}
```

**Key Indexes**:
- `user_id` + `platform` - for user's platform management
- `user_id` + `last_sync` (desc) - for sync status monitoring
- `user_id` + `status` - for active account filtering
- `platform` + `connected_at` (desc) - for platform adoption analytics

### 3. Content Plans Collection (`content_plans`)

Content planning and scheduling information.

**Document ID**: Plan UUID
**Security**: Users can only access plans they own

```typescript
interface ContentPlan {
  id: string;                    // Plan UUID (matches document ID)
  user_id: string;               // Reference to users collection
  title: string;                 // Plan title/name
  description?: string;          // Plan description
  timeframe_start: Timestamp;    // Plan start date
  timeframe_end: Timestamp;      // Plan end date
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  platforms: string[];           // Target platforms
  goals: string[];               // Plan objectives
  target_metrics: {
    total_posts?: number;
    target_engagement?: number;
    target_reach?: number;
    target_followers?: number;
  };
  created_at: Timestamp;         // Plan creation time
  updated_at: Timestamp;         // Last modification time
  post_count: number;            // Number of planned posts
  ai_generated: boolean;         // Whether plan was AI-generated
}
```

**Key Indexes**:
- `user_id` + `status` - for user's plan management
- `user_id` + `created_at` (desc) - for chronological listing
- `user_id` + `timeframe_start` - for timeline filtering
- `status` + `timeframe_end` - for plan completion tracking

#### 3.1. Posts Subcollection (`content_plans/{planId}/posts`)

Individual posts within content plans.

**Document ID**: Post UUID
**Security**: Inherited from parent plan ownership

```typescript
interface PlannedPost {
  id: string;                    // Post UUID (matches document ID)
  title: string;                 // Post title
  description?: string;          // Post description/caption
  content_type: 'post' | 'story' | 'video' | 'reel' | 'short';
  platform: string;             // Target platform
  account_id: string;            // Connected account ID
  scheduled_time: Timestamp;     // Scheduled posting time
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  tags: string[];                // Hashtags and mentions
  estimated_reach: number;       // AI-predicted reach
  content_pillars: string[];     // Content categorization
  ai_generated: boolean;         // AI-generated content flag
  performance_prediction: {
    expected_engagement?: number;
    expected_reach?: number;
    confidence_score?: number;
  };
  created_at: Timestamp;         // Post creation time
}
```

**Key Indexes**:
- `platform` + `scheduled_time` - for platform-specific scheduling
- `status` + `scheduled_time` - for publishing queue
- `content_type` + `created_at` (desc) - for content type analytics

### 4. Chat Messages Collection (`chat_messages`)

AI chat conversation history and context.

**Document ID**: Message UUID
**Security**: Users can only access their own messages

```typescript
interface ChatMessage {
  id: string;                    // Message UUID (matches document ID)
  user_id: string;               // Reference to users collection
  role: 'user' | 'assistant';    // Message sender role
  content: string;               // Message content
  timestamp: Timestamp;          // Message timestamp
  session_id?: string;           // Chat session grouping
  metadata: {
    agent_type?: string;         // Which AI agent responded
    processing_time?: number;    // Response generation time
    tokens_used?: number;        // LLM tokens consumed
    platform_context?: string;  // Platform-specific context
    analysis_results?: any;      // Attached analysis data
  };
  created_at: Timestamp;         // Message creation time
}
```

**Key Indexes**:
- `user_id` + `timestamp` (desc) - for chat history retrieval
- `user_id` + `session_id` + `timestamp` - for session management
- `role` + `timestamp` (desc) - for message type analytics

### 5. Analytics Data Collection (`analytics_data`)

Historical performance metrics and analytics.

**Document ID**: Connected Account ID
**Security**: Read-only for account owners

```typescript
interface AnalyticsData {
  account_id: string;            // Reference to connected_accounts
  platform: string;             // Platform type
  daily_metrics: DailyMetric[];  // Time series data
  summary: {
    total_followers: number;
    avg_engagement_rate: number;
    total_posts: number;
    growth_rate_30d: number;
    best_performing_post?: any;
    worst_performing_post?: any;
  };
  last_updated: Timestamp;       // Last data refresh
  data_range_days: number;       // Number of days of data
}

interface DailyMetric {
  date: Timestamp;               // Metric date
  followers: number;             // Follower count
  following?: number;            // Following count
  posts: number;                 // Posts published
  engagement: number;            // Total engagement
  reach?: number;                // Content reach
  impressions?: number;          // Content impressions
  saves?: number;                // Content saves
  shares?: number;               // Content shares
  comments?: number;             // Comments received
  likes?: number;                // Likes received
  platform_specific: any;       // Platform-specific metrics
}
```

**Key Indexes**:
- `account_id` - for account analytics lookup
- `platform` + `last_updated` (desc) - for platform analytics aggregation

### 6. User Sessions Subcollection (`users/{userId}/sessions`) (Optional)

User authentication and session management.

**Document ID**: Session UUID
**Security**: Users can only access their own sessions

```typescript
interface UserSession {
  id: string;                    // Session UUID
  created_at: Timestamp;         // Session start time
  last_active: Timestamp;        // Last activity time
  expires_at: Timestamp;         // Session expiration
  device_info?: {
    user_agent: string;
    ip_address: string;
    device_type: string;
  };
  status: 'active' | 'expired' | 'revoked';
}
```

## Data Relationships

```
Users (1) ──── Connected Accounts (N)
  │
  ├─── Content Plans (N)
  │      │
  │      └─── Posts (N)
  │
  ├─── Chat Messages (N)
  │
  └─── Sessions (N)

Connected Accounts (1) ──── Analytics Data (1)
```

## Security Rules Summary

1. **Authentication Required**: All operations require valid Firebase Authentication
2. **User Isolation**: Users can only access their own data
3. **Data Validation**: Server-side validation for required fields and data types
4. **Read-Only Analytics**: Analytics data is read-only for users (written by backend services)
5. **Cascading Permissions**: Subcollections inherit parent document permissions

## Performance Optimizations

1. **Compound Indexes**: Optimized for common query patterns
2. **Collection Group Queries**: Efficient cross-plan post queries
3. **Batch Operations**: All seed operations use Firestore batches
4. **Query Limits**: Reasonable pagination limits to prevent timeouts
5. **Data Denormalization**: Key metrics stored redundantly for performance

## Development & Testing

- **Sample Data**: Comprehensive sample data generation for development
- **Seed Scripts**: Automated database population for testing
- **Validation**: Client and server-side data validation
- **Backup Strategy**: Regular backups for production data

## Migration Support

The schema is designed to support:
- **Version Migration**: Field additions and modifications
- **Data Migration**: Batch updates for schema changes
- **Backward Compatibility**: Graceful handling of missing fields
- **Platform Expansion**: Easy addition of new social media platforms