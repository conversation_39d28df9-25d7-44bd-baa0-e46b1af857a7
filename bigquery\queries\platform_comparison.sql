-- Platform Performance Comparison
-- Compares performance metrics across different social media platforms

WITH platform_daily_stats AS (
  SELECT
    date,
    platform,
    COUNT(DISTINCT account_id) AS active_accounts,
    SUM(followers_count) AS total_followers,
    SUM(posts_count) AS total_posts,
    AVG(engagement_rate) AS avg_engagement_rate,
    SUM(reach) AS total_reach,
    SUM(impressions) AS total_impressions,
    SUM(likes) AS total_likes,
    SUM(comments) AS total_comments,
    SUM(shares) AS total_shares
  FROM `{project_id}.{dataset_id}.account_metrics_daily`
  WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
  GROUP BY date, platform
),

platform_growth AS (
  SELECT
    platform,
    date,
    total_followers,
    LAG(total_followers) OVER (PARTITION BY platform ORDER BY date) AS prev_followers,
    total_posts,
    LAG(total_posts) OVER (PARTITION BY platform ORDER BY date) AS prev_posts
  FROM platform_daily_stats
),

platform_metrics AS (
  SELECT
    platform,
    date,
    active_accounts,
    total_followers,
    total_posts,
    avg_engagement_rate,
    total_reach,
    total_impressions,
    total_likes,
    total_comments,
    total_shares,
    
    -- Growth calculations
    CASE 
      WHEN prev_followers > 0 
      THEN ROUND(((total_followers - prev_followers) / prev_followers) * 100, 2)
      ELSE 0 
    END AS follower_growth_rate,
    
    CASE 
      WHEN prev_posts > 0 
      THEN total_posts - prev_posts
      ELSE 0 
    END AS new_posts_count
    
  FROM platform_growth
)

SELECT
  platform,
  
  -- Account metrics
  AVG(active_accounts) AS avg_active_accounts,
  MAX(active_accounts) AS max_active_accounts,
  
  -- Follower metrics
  AVG(total_followers) AS avg_total_followers,
  MAX(total_followers) AS max_total_followers,
  AVG(follower_growth_rate) AS avg_follower_growth_rate,
  
  -- Content metrics
  AVG(total_posts) AS avg_total_posts,
  SUM(new_posts_count) AS total_new_posts_30d,
  AVG(new_posts_count) AS avg_daily_new_posts,
  
  -- Engagement metrics
  ROUND(AVG(avg_engagement_rate), 4) AS avg_engagement_rate,
  ROUND(STDDEV(avg_engagement_rate), 4) AS engagement_rate_stddev,
  
  -- Reach and impression metrics
  AVG(total_reach) AS avg_daily_reach,
  SUM(total_reach) AS total_reach_30d,
  AVG(total_impressions) AS avg_daily_impressions,
  SUM(total_impressions) AS total_impressions_30d,
  
  -- Interaction metrics
  SUM(total_likes) AS total_likes_30d,
  SUM(total_comments) AS total_comments_30d,
  SUM(total_shares) AS total_shares_30d,
  
  -- Platform efficiency metrics
  CASE 
    WHEN SUM(total_impressions) > 0 
    THEN ROUND((SUM(total_likes) + SUM(total_comments) + SUM(total_shares)) / SUM(total_impressions) * 100, 4)
    ELSE 0 
  END AS engagement_per_impression_rate,
  
  CASE 
    WHEN SUM(total_reach) > 0 
    THEN ROUND((SUM(total_likes) + SUM(total_comments) + SUM(total_shares)) / SUM(total_reach) * 100, 4)
    ELSE 0 
  END AS engagement_per_reach_rate,
  
  -- Performance rankings
  RANK() OVER (ORDER BY AVG(avg_engagement_rate) DESC) AS engagement_rate_rank,
  RANK() OVER (ORDER BY AVG(follower_growth_rate) DESC) AS growth_rate_rank,
  RANK() OVER (ORDER BY SUM(total_reach) DESC) AS reach_rank

FROM platform_metrics
GROUP BY platform
ORDER BY avg_engagement_rate DESC;