# Design Document

## Overview

This design document outlines the architecture for integrating Google ADK (Agent Development Kit) agents with the existing Next.js frontend chat interface. The solution leverages ADK's native FastAPI server capabilities to provide seamless communication between the frontend and ADK agents while maintaining the existing application structure.

The integration uses ADK's built-in endpoints (`/run_sse`, `/list-apps`, session management endpoints) and follows ADK's event-driven architecture with proper handling of `RunAgentRequest` objects and `Event` streaming responses.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        NextJS["Next.js Frontend<br/>Port 3000"]
        ChatUI["Chat Interface<br/>React Components"]
        EventSource["EventSource API<br/>SSE Client"]
    end
    
    subgraph "Backend Integration Layer"
        FastAPIBackend["Existing FastAPI Backend<br/>Port 8000"]
        ADKProxy["ADK Proxy Service<br/>Request/Response Translation"]
        ChatRouter["Enhanced Chat Router<br/>ADK Integration"]
    end
    
    subgraph "ADK Layer"
        ADKServer["ADK FastAPI Server<br/>Port 8001"]
        ADKEndpoints["ADK Endpoints<br/>/run_sse, /list-apps, /sessions"]
        AgentLoader["Agent Loader<br/>agents/ directory"]
        SessionService["Session Service<br/>Conversation state"]
    end
    
    subgraph "Agent Ecosystem"
        Agents["ADK Agents<br/>content_planner, research_agent, etc."]
        Tools["Agent Tools<br/>Google Search, Social Media APIs"]
        Runner["ADK Runner<br/>Agent execution"]
    end
    
    NextJS --> ChatUI
    ChatUI --> EventSource
    EventSource --> FastAPIBackend
    FastAPIBackend --> ADKProxy
    ADKProxy --> ChatRouter
    ChatRouter --> ADKServer
    
    ADKServer --> ADKEndpoints
    ADKEndpoints --> AgentLoader
    ADKEndpoints --> SessionService
    AgentLoader --> Agents
    Agents --> Tools
    Agents --> Runner
```

### Component Integration Strategy

The design follows a proxy pattern where the existing FastAPI backend acts as an intermediary between the frontend and the ADK server. This approach:

1. **Preserves existing architecture** - No changes to frontend API contracts
2. **Enables gradual migration** - Can be rolled out with feature flags
3. **Provides abstraction** - Frontend doesn't need to know about ADK specifics
4. **Allows customization** - Backend can add business logic, authentication, etc.

## Components and Interfaces

### 1. ADK FastAPI Server Integration

**Purpose**: Initialize and manage the ADK FastAPI server alongside the existing backend.

**Implementation**:
```python
# app/services/adk_service.py
from google.adk.cli.fast_api import get_fast_api_app
from google.adk.cli.utils.agent_loader import AgentLoader
import asyncio
import httpx

class ADKService:
    def __init__(self, agents_dir: str = "agents", port: int = 8001):
        self.agents_dir = agents_dir
        self.port = port
        self.base_url = f"http://localhost:{port}"
        self.client = httpx.AsyncClient()
        self.server_process = None
        
    async def start_adk_server(self):
        """Start ADK FastAPI server in a separate process"""
        # Use ADK's get_fast_api_app to create the server
        # Run on separate port to avoid conflicts
        
    async def list_agents(self) -> List[str]:
        """Get available agents from /list-apps endpoint"""
        response = await self.client.get(f"{self.base_url}/list-apps")
        return response.json()
        
    async def create_session(self, app_name: str, user_id: str) -> str:
        """Create new ADK session"""
        response = await self.client.post(
            f"{self.base_url}/apps/{app_name}/users/{user_id}/sessions"
        )
        return response.json()["id"]
```

**Key Features**:
- Runs ADK server on separate port (8001) to avoid conflicts
- Provides async client for communication with ADK endpoints
- Handles server lifecycle management
- Exposes simplified interface for backend integration

### 2. Enhanced Chat Router

**Purpose**: Extend the existing chat router to integrate with ADK while maintaining backward compatibility.

**Implementation**:
```python
# app/routers/chat_enhanced.py
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from app.services.adk_service import ADKService
from app.models.chat_models import ChatMessage, StreamingChunk
import json

router = APIRouter()
adk_service = ADKService()

@router.post("/send-message-stream")
async def send_message_stream(
    message: str,
    session_id: str,
    user_id: str,
    agent_name: str = "content_planner"
):
    """Stream chat response using ADK agents"""
    
    # Format ADK request
    adk_request = {
        "app_name": agent_name,
        "user_id": user_id,
        "session_id": session_id,
        "new_message": {
            "role": "user",
            "parts": [{"text": message}]
        },
        "streaming": True
    }
    
    async def event_stream():
        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST",
                f"{adk_service.base_url}/run_sse",
                json=adk_request,
                headers={"Accept": "text/event-stream"}
            ) as response:
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        event_data = line[6:]  # Remove "data: " prefix
                        try:
                            event = json.loads(event_data)
                            # Transform ADK Event to frontend format
                            chunk = transform_adk_event_to_chunk(event)
                            yield f"data: {chunk.model_dump_json()}\n\n"
                        except json.JSONDecodeError:
                            continue
    
    return StreamingResponse(event_stream(), media_type="text/event-stream")
```

**Key Features**:
- Maintains existing API contract for frontend
- Transforms ADK `Event` objects to frontend-expected format
- Handles SSE streaming from ADK to frontend
- Provides error handling and fallback mechanisms

### 3. ADK Event Transformation Layer

**Purpose**: Convert ADK `Event` objects to frontend-compatible message formats.

**Implementation**:
```python
# app/services/event_transformer.py
from app.models.chat_models import StreamingChunk, MessageRole
from typing import Dict, Any

class ADKEventTransformer:
    @staticmethod
    def transform_event_to_chunk(adk_event: Dict[str, Any]) -> StreamingChunk:
        """Transform ADK Event object to StreamingChunk"""
        
        # Extract text content from Event.content.parts
        content = ""
        if "content" in adk_event and adk_event["content"]:
            parts = adk_event["content"].get("parts", [])
            for part in parts:
                if "text" in part:
                    content += part["text"]
        
        # Check if turn is complete
        is_done = adk_event.get("turn_complete", False)
        
        # Handle interruptions
        if adk_event.get("interrupted", False):
            is_done = True
            content += " [Interrupted]"
        
        # Handle function calls (tool usage)
        function_calls = []
        if "content" in adk_event and adk_event["content"]:
            parts = adk_event["content"].get("parts", [])
            for part in parts:
                if "function_call" in part:
                    function_calls.append(part["function_call"])
        
        return StreamingChunk(
            content=content,
            done=is_done,
            message_id=adk_event.get("invocation_id", ""),
            metadata={
                "author": adk_event.get("author", "agent"),
                "function_calls": function_calls,
                "interrupted": adk_event.get("interrupted", False)
            }
        )
```

**Key Features**:
- Handles text extraction from ADK `Event.content.parts`
- Manages completion status through `turn_complete` field
- Processes function calls and tool usage
- Preserves metadata for debugging and display

### 4. Session Management Integration

**Purpose**: Integrate ADK's session management with the existing chat system.

**Implementation**:
```python
# app/services/session_manager.py
from app.services.adk_service import ADKService
from typing import Optional, Dict, Any

class SessionManager:
    def __init__(self, adk_service: ADKService):
        self.adk_service = adk_service
        self.session_cache: Dict[str, Dict[str, Any]] = {}
    
    async def get_or_create_session(
        self, 
        user_id: str, 
        agent_name: str,
        session_id: Optional[str] = None
    ) -> str:
        """Get existing session or create new one"""
        
        if session_id:
            # Try to retrieve existing session
            try:
                session_data = await self.adk_service.client.get(
                    f"{self.adk_service.base_url}/apps/{agent_name}/users/{user_id}/sessions/{session_id}"
                )
                if session_data.status_code == 200:
                    return session_id
            except Exception:
                pass  # Fall through to create new session
        
        # Create new session
        response = await self.adk_service.client.post(
            f"{self.adk_service.base_url}/apps/{agent_name}/users/{user_id}/sessions"
        )
        new_session = response.json()
        return new_session["id"]
    
    async def get_chat_history(
        self, 
        user_id: str, 
        agent_name: str, 
        session_id: str
    ) -> List[Dict[str, Any]]:
        """Retrieve chat history from ADK session"""
        response = await self.adk_service.client.get(
            f"{self.adk_service.base_url}/apps/{agent_name}/users/{user_id}/sessions/{session_id}"
        )
        session_data = response.json()
        return session_data.get("events", [])
```

**Key Features**:
- Manages ADK session lifecycle
- Provides session caching for performance
- Handles session retrieval and creation
- Integrates with ADK's session endpoints

## Data Models

### Enhanced Chat Models

```python
# app/models/chat_models.py
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

class StreamingChunk(BaseModel):
    content: str
    done: bool
    message_id: str
    metadata: Optional[Dict[str, Any]] = None

class ChatMessage(BaseModel):
    id: str
    role: MessageRole
    content: str
    session_id: str
    user_id: str
    agent_name: Optional[str] = None
    timestamp: datetime = datetime.now()
    metadata: Optional[Dict[str, Any]] = None

class SessionInfo(BaseModel):
    session_id: str
    user_id: str
    agent_name: str
    created_at: datetime
    last_activity: datetime
    message_count: int
    adk_session_id: Optional[str] = None  # Maps to ADK session

class ADKRunRequest(BaseModel):
    """ADK RunAgentRequest format"""
    app_name: str
    user_id: str
    session_id: str
    new_message: Dict[str, Any]  # types.Content format
    streaming: bool = True
    state_delta: Optional[Dict[str, Any]] = None
```

### ADK Event Processing Models

```python
# app/models/adk_models.py
from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class ADKEventContent(BaseModel):
    role: str
    parts: List[Dict[str, Any]]

class ADKEvent(BaseModel):
    author: Optional[str] = None
    invocation_id: Optional[str] = None
    content: Optional[ADKEventContent] = None
    interrupted: bool = False
    turn_complete: bool = False
    long_running_tool_ids: Optional[List[str]] = None
    
class ADKAgentInfo(BaseModel):
    name: str
    description: Optional[str] = None
    available: bool = True
```

## Error Handling

### Error Handling Strategy

```python
# app/core/error_handling.py
from fastapi import HTTPException
from typing import Optional
import logging

class ADKIntegrationError(Exception):
    """Base exception for ADK integration errors"""
    pass

class ADKServerUnavailableError(ADKIntegrationError):
    """ADK server is not available"""
    pass

class ADKAgentNotFoundError(ADKIntegrationError):
    """Requested agent not found"""
    pass

class ErrorHandler:
    @staticmethod
    async def handle_adk_error(error: Exception, fallback_message: str) -> str:
        """Handle ADK errors with appropriate fallbacks"""
        logging.error(f"ADK Error: {error}")
        
        if isinstance(error, ADKServerUnavailableError):
            return "I'm temporarily unavailable. Please try again in a moment."
        elif isinstance(error, ADKAgentNotFoundError):
            return "The requested agent is not available. Using default assistant."
        else:
            return fallback_message
    
    @staticmethod
    async def create_error_stream(error_message: str) -> str:
        """Create SSE error stream"""
        error_chunk = StreamingChunk(
            content=error_message,
            done=True,
            message_id="error",
            metadata={"error": True}
        )
        return f"data: {error_chunk.model_dump_json()}\n\n"
```

## Testing Strategy

### Unit Testing

```python
# tests/test_adk_integration.py
import pytest
from unittest.mock import AsyncMock, patch
from app.services.adk_service import ADKService
from app.services.event_transformer import ADKEventTransformer

class TestADKIntegration:
    @pytest.fixture
    def adk_service(self):
        return ADKService(agents_dir="test_agents", port=8002)
    
    @pytest.mark.asyncio
    async def test_event_transformation(self):
        """Test ADK Event to StreamingChunk transformation"""
        adk_event = {
            "author": "test_agent",
            "invocation_id": "test_123",
            "content": {
                "role": "model",
                "parts": [{"text": "Hello, world!"}]
            },
            "turn_complete": False
        }
        
        chunk = ADKEventTransformer.transform_event_to_chunk(adk_event)
        
        assert chunk.content == "Hello, world!"
        assert chunk.done == False
        assert chunk.message_id == "test_123"
        assert chunk.metadata["author"] == "test_agent"
    
    @pytest.mark.asyncio
    async def test_streaming_response(self, adk_service):
        """Test streaming response handling"""
        with patch('httpx.AsyncClient.stream') as mock_stream:
            # Mock SSE response
            mock_response = AsyncMock()
            mock_response.aiter_lines.return_value = [
                'data: {"content":{"parts":[{"text":"Hello"}]},"turn_complete":false}',
                'data: {"content":{"parts":[{"text":" world!"}]},"turn_complete":true}'
            ]
            mock_stream.return_value.__aenter__.return_value = mock_response
            
            # Test streaming
            chunks = []
            async for chunk in stream_adk_response(adk_service, "test_message"):
                chunks.append(chunk)
            
            assert len(chunks) == 2
            assert "Hello" in chunks[0]
            assert " world!" in chunks[1]
```

### Integration Testing

```python
# tests/test_chat_integration.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

class TestChatIntegration:
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_chat_endpoint_with_adk(self, client):
        """Test chat endpoint with ADK integration"""
        response = client.post(
            "/api/chat/send-message-stream",
            json={
                "message": "Hello, agent!",
                "session_id": "test_session",
                "user_id": "test_user",
                "agent_name": "content_planner"
            }
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
    
    def test_agent_discovery(self, client):
        """Test agent discovery endpoint"""
        response = client.get("/api/chat/agents")
        
        assert response.status_code == 200
        agents = response.json()
        assert isinstance(agents, list)
        assert len(agents) > 0
```

## Deployment Configuration

### Environment Configuration

```python
# app/core/config.py
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Existing settings...
    
    # ADK Integration Settings
    adk_enabled: bool = True
    adk_server_port: int = 8001
    adk_agents_dir: str = "agents"
    adk_session_service_uri: Optional[str] = None
    adk_artifact_service_uri: Optional[str] = None
    adk_memory_service_uri: Optional[str] = None
    
    # Feature Flags
    enable_adk_streaming: bool = True
    enable_adk_function_calls: bool = True
    enable_adk_debug_endpoints: bool = False
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### Docker Configuration

```dockerfile
# Dockerfile updates for ADK integration
FROM python:3.11-slim

# Install ADK dependencies
RUN pip install google-adk

# Copy agents directory
COPY agents/ /app/agents/

# Set environment variables
ENV ADK_ENABLED=true
ENV ADK_AGENTS_DIR=/app/agents
ENV ADK_SERVER_PORT=8001

# Expose both backend and ADK ports
EXPOSE 8000 8001

# Start both services
CMD ["python", "-m", "app.main"]
```

This design provides a comprehensive integration strategy that leverages ADK's native capabilities while maintaining compatibility with the existing frontend architecture. The proxy pattern allows for gradual migration and provides flexibility for future enhancements.