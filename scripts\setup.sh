#!/bin/bash

# Social Media Manager - Setup Script
# This script helps you set up the development environment quickly

set -e

echo "🚀 Setting up Social Media Manager Development Environment"
echo "=========================================================="

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version | cut -d 'v' -f 2 | cut -d '.' -f 1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.11+ from https://python.org/"
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d ' ' -f 2 | cut -d '.' -f 2)
if [ "$PYTHON_VERSION" -lt 11 ]; then
    echo "❌ Python 3.11+ is required. Current version: $(python3 --version)"
    exit 1
fi

# Check Docker (optional)
if command -v docker &> /dev/null; then
    echo "✅ Docker found - Docker setup will be available"
    DOCKER_AVAILABLE=true
else
    echo "⚠️  Docker not found - manual setup will be used"
    DOCKER_AVAILABLE=false
fi

echo "✅ Prerequisites check completed!"
echo ""

# Setup Frontend
echo "🎨 Setting up Frontend (Next.js)..."
cd app-frontend

if [ ! -f ".env.local" ]; then
    echo "📄 Creating frontend environment file..."
    cp .env.example .env.local
    echo "✅ Created .env.local from template"
    echo "⚠️  Please edit app-frontend/.env.local with your configuration"
fi

echo "📦 Installing frontend dependencies..."
npm install
echo "✅ Frontend dependencies installed!"

cd ..

# Setup Backend
echo "🔧 Setting up Backend (FastAPI)..."
cd app-agents

if [ ! -f ".env" ]; then
    echo "📄 Creating backend environment file..."
    cp .env.example .env
    echo "✅ Created .env from template"
    echo "⚠️  Please edit app-agents/.env with your API keys"
fi

# Create virtual environment
if [ ! -d "venv" ]; then
    echo "🐍 Creating Python virtual environment..."
    python3 -m venv venv
    echo "✅ Virtual environment created!"
fi

# Activate virtual environment and install dependencies
echo "📦 Installing backend dependencies..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
echo "✅ Backend dependencies installed!"

cd ..

# Create necessary directories
echo "📁 Creating additional directories..."
mkdir -p logs
mkdir -p uploads
mkdir -p downloads
echo "✅ Project directories created!"

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Configure your environment variables:"
echo "   - Edit app-frontend/.env.local"
echo "   - Edit app-agents/.env"
echo ""
echo "2. Start the development servers:"
echo ""

if [ "$DOCKER_AVAILABLE" = true ]; then
    echo "   Option A: Using Docker Compose (Recommended)"
    echo "   docker-compose up --build"
    echo ""
fi

echo "   Option B: Manual startup"
echo "   # Terminal 1 - Backend"
echo "   cd app-agents"
echo "   source venv/bin/activate"
echo "   uvicorn main:app --reload --host 0.0.0.0 --port 8000"
echo ""
echo "   # Terminal 2 - Frontend"
echo "   cd app-frontend"
echo "   npm run dev"
echo ""
echo "3. Access the application:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend API: http://localhost:8000"
echo "   - API Docs: http://localhost:8000/docs"
echo ""
echo "📚 For more information, see README.md"
echo ""
echo "Happy coding! 🚀"