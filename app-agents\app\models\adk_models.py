"""
ADK Data Models and Type Definitions

This module contains Pydantic models for ADK Event objects, RunAgentRequest format,
and response transformation schemas used for ADK API communication.

Requirements covered: 1.2, 10.1, 10.3
"""

from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class MessageRole(str, Enum):
    """Message roles for ADK communication"""
    USER = "user"
    MODEL = "model"
    SYSTEM = "system"


class ADKContentPart(BaseModel):
    """Individual content part within ADK Event content"""
    text: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None
    function_response: Optional[Dict[str, Any]] = None
    inline_data: Optional[Dict[str, Any]] = None  # For binary data (audio, images)
    
    @model_validator(mode='after')
    def validate_at_least_one_field(self):
        """Ensure at least one content type is present"""
        if not any([self.text, self.function_call, self.function_response, self.inline_data]):
            raise ValueError("At least one content field must be present")
        return self


class ADKEventContent(BaseModel):
    """Content structure for ADK Event objects"""
    role: MessageRole
    parts: List[ADKContentPart] = Field(default_factory=list)
    
    @field_validator('parts')
    @classmethod
    def validate_parts_not_empty(cls, v):
        """Ensure parts list is not empty"""
        if not v:
            raise ValueError("Content parts cannot be empty")
        return v


class ADKEvent(BaseModel):
    """
    ADK Event object structure for Server-Sent Events streaming
    
    This model represents the Event objects returned by ADK's /run_sse endpoint
    """
    author: Optional[str] = None
    invocation_id: Optional[str] = None
    content: Optional[ADKEventContent] = None
    interrupted: bool = False
    turn_complete: bool = False
    long_running_tool_ids: Optional[List[str]] = Field(default_factory=list)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration"""
        extra = "allow"  # Allow additional fields for future ADK extensions


class ADKMessage(BaseModel):
    """Message structure for RunAgentRequest"""
    role: MessageRole
    parts: List[ADKContentPart]
    
    @classmethod
    def from_text(cls, text: str, role: MessageRole = MessageRole.USER) -> "ADKMessage":
        """Create a simple text message"""
        return cls(
            role=role,
            parts=[ADKContentPart(text=text)]
        )


class ADKRunAgentRequest(BaseModel):
    """
    RunAgentRequest format for ADK API communication
    
    This model represents the request format expected by ADK's /run and /run_sse endpoints
    """
    app_name: str = Field(..., description="Name of the ADK agent to run")
    user_id: str = Field(..., description="Unique identifier for the user")
    session_id: str = Field(..., description="Session identifier for conversation continuity")
    new_message: ADKMessage = Field(..., description="New message to send to the agent")
    streaming: bool = Field(default=True, description="Enable streaming responses")
    state_delta: Optional[Dict[str, Any]] = Field(default=None, description="State changes to apply")
    
    class Config:
        """Pydantic configuration"""
        json_schema_extra = {
            "example": {
                "app_name": "content_planner",
                "user_id": "user_123",
                "session_id": "session_456",
                "new_message": {
                    "role": "user",
                    "parts": [{"text": "Hello, can you help me plan content?"}]
                },
                "streaming": True
            }
        }


class ADKAgentInfo(BaseModel):
    """Information about available ADK agents"""
    name: str = Field(..., description="Agent name/identifier")
    description: Optional[str] = Field(None, description="Agent description")
    available: bool = Field(default=True, description="Whether agent is available")
    capabilities: Optional[List[str]] = Field(default_factory=list, description="Agent capabilities")
    tools: Optional[List[str]] = Field(default_factory=list, description="Available tools")


class ADKSessionInfo(BaseModel):
    """ADK session information"""
    id: str = Field(..., description="Session identifier")
    app_name: str = Field(..., description="Associated agent name")
    user_id: str = Field(..., description="User identifier")
    created_at: Optional[datetime] = Field(None, description="Session creation time")
    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")
    event_count: int = Field(default=0, description="Number of events in session")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ADKSessionCreateRequest(BaseModel):
    """Request to create a new ADK session"""
    user_id: str = Field(..., description="User identifier")
    app_name: str = Field(..., description="Agent name")
    initial_state: Optional[Dict[str, Any]] = Field(default=None, description="Initial session state")


class ADKSessionCreateResponse(BaseModel):
    """Response from ADK session creation"""
    id: str = Field(..., description="Created session identifier")
    app_name: str = Field(..., description="Agent name")
    user_id: str = Field(..., description="User identifier")
    created_at: datetime = Field(..., description="Creation timestamp")


class ADKErrorResponse(BaseModel):
    """Error response from ADK API"""
    error: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Error code")
    details: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional error details")
    invocation_id: Optional[str] = Field(None, description="Request invocation ID for tracing")


class ADKHealthCheck(BaseModel):
    """Health check response from ADK server"""
    status: str = Field(..., description="Health status")
    agents_loaded: int = Field(..., description="Number of agents loaded")
    available_agents: List[str] = Field(..., description="List of available agent names")
    server_version: Optional[str] = Field(None, description="ADK server version")
    uptime_seconds: Optional[float] = Field(None, description="Server uptime in seconds")


# Validation schemas for API communication
class ADKRequestValidator:
    """Validation utilities for ADK requests"""
    
    @staticmethod
    def validate_app_name(app_name: str) -> bool:
        """Validate agent app name format"""
        if not app_name or not isinstance(app_name, str):
            return False
        # Agent names should be alphanumeric with underscores/hyphens
        import re
        return bool(re.match(r'^[a-zA-Z0-9_-]+$', app_name))
    
    @staticmethod
    def validate_user_id(user_id: str) -> bool:
        """Validate user ID format"""
        if not user_id or not isinstance(user_id, str):
            return False
        # User IDs should be non-empty strings
        return len(user_id.strip()) > 0
    
    @staticmethod
    def validate_session_id(session_id: str) -> bool:
        """Validate session ID format"""
        if not session_id or not isinstance(session_id, str):
            return False
        # Session IDs should be non-empty strings
        return len(session_id.strip()) > 0


# Response transformation models
class StreamingChunk(BaseModel):
    """Frontend-compatible streaming chunk format"""
    content: str = Field(..., description="Text content of the chunk")
    done: bool = Field(..., description="Whether the response is complete")
    message_id: str = Field(..., description="Message/invocation identifier")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        """Pydantic configuration"""
        json_schema_extra = {
            "example": {
                "content": "Hello! I can help you with content planning.",
                "done": False,
                "message_id": "inv_123",
                "metadata": {
                    "author": "content_planner",
                    "function_calls": [],
                    "interrupted": False
                }
            }
        }


class ChatMessage(BaseModel):
    """Enhanced chat message model with ADK integration"""
    id: str = Field(..., description="Message identifier")
    role: MessageRole = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    session_id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    agent_name: Optional[str] = Field(None, description="ADK agent name")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    adk_invocation_id: Optional[str] = Field(None, description="ADK invocation ID for tracing")
    function_calls: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="Function calls made")
    
    class Config:
        """Pydantic configuration"""
        json_schema_extra = {
            "example": {
                "id": "msg_123",
                "role": "user",
                "content": "Help me plan content for next week",
                "session_id": "session_456",
                "user_id": "user_789",
                "agent_name": "content_planner",
                "timestamp": "2024-01-01T12:00:00Z",
                "metadata": {},
                "adk_invocation_id": "inv_123",
                "function_calls": []
            }
        }