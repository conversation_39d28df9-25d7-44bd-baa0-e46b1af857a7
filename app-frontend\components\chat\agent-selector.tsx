"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  ChevronDown, 
  Bot, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Zap,
  Search,
  BarChart3,
  PenTool,
  MessageSquare
} from "lucide-react";
import { ADKAgentInfo, ChatError } from "@/types/adk";
import { cn } from "@/lib/utils";

export interface AgentSelectorProps {
  availableAgents: ADKAgentInfo[];
  selectedAgent?: string;
  onAgentSelect: (agentName: string) => void;
  isLoading?: boolean;
  isChangingAgent?: boolean;
  error?: ChatError | null;
  onRefresh?: () => void;
  disabled?: boolean;
  showStatus?: boolean;
  showCapabilities?: boolean;
  className?: string;
}

// Agent icon mapping
const getAgentIcon = (agentName: string) => {
  const name = agentName.toLowerCase();
  if (name.includes('content') || name.includes('planner')) return PenTool;
  if (name.includes('research') || name.includes('search')) return Search;
  if (name.includes('analytics') || name.includes('metrics')) return BarChart3;
  if (name.includes('chat') || name.includes('conversation')) return MessageSquare;
  return Bot;
};

// Agent color mapping
const getAgentColor = (agentName: string) => {
  const name = agentName.toLowerCase();
  if (name.includes('content') || name.includes('planner')) return 'text-blue-600 bg-blue-50';
  if (name.includes('research') || name.includes('search')) return 'text-green-600 bg-green-50';
  if (name.includes('analytics') || name.includes('metrics')) return 'text-purple-600 bg-purple-50';
  if (name.includes('chat') || name.includes('conversation')) return 'text-orange-600 bg-orange-50';
  return 'text-gray-600 bg-gray-50';
};

export function AgentSelector({
  availableAgents,
  selectedAgent,
  onAgentSelect,
  isLoading = false,
  isChangingAgent = false,
  error,
  onRefresh,
  disabled = false,
  showStatus = true,
  showCapabilities = true,
  className
}: AgentSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const currentAgent = availableAgents.find(agent => agent.name === selectedAgent);
  const AgentIcon = currentAgent ? getAgentIcon(currentAgent.name) : Bot;
  const agentColorClass = currentAgent ? getAgentColor(currentAgent.name) : 'text-gray-600 bg-gray-50';

  const handleAgentSelect = useCallback((agentName: string) => {
    if (agentName !== selectedAgent && !isChangingAgent) {
      onAgentSelect(agentName);
      setIsOpen(false);
    }
  }, [selectedAgent, isChangingAgent, onAgentSelect]);

  const handleRefresh = useCallback(() => {
    onRefresh?.();
  }, [onRefresh]);

  if (isLoading && availableAgents.length === 0) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex items-center gap-2 px-3 py-2 bg-muted/20 rounded-lg">
          <RefreshCw className="w-4 h-4 animate-spin" />
          <span className="text-sm text-muted-foreground">Loading agents...</span>
        </div>
      </div>
    );
  }

  if (error && availableAgents.length === 0) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex items-center gap-2 px-3 py-2 bg-red-50 text-red-600 rounded-lg">
          <AlertCircle className="w-4 h-4" />
          <span className="text-sm">Failed to load agents</span>
          {onRefresh && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
            >
              <RefreshCw className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={cn("flex items-center gap-2", className)}>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "flex items-center gap-2 h-9 px-3",
                isChangingAgent && "opacity-50 cursor-not-allowed",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              disabled={disabled || isChangingAgent}
            >
              <Avatar className="w-5 h-5">
                <AvatarFallback className={cn("text-xs", agentColorClass)}>
                  <AgentIcon className="w-3 h-3" />
                </AvatarFallback>
              </Avatar>
              
              <div className="flex flex-col items-start min-w-0">
                <span className="text-sm font-medium truncate">
                  {currentAgent?.name || selectedAgent || 'Select Agent'}
                </span>
                {showStatus && currentAgent && (
                  <div className="flex items-center gap-1">
                    {currentAgent.available ? (
                      <CheckCircle className="w-3 h-3 text-green-500" />
                    ) : (
                      <AlertCircle className="w-3 h-3 text-red-500" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {currentAgent.available ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                )}
              </div>

              {isChangingAgent ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent align="start" className="w-80">
            <DropdownMenuLabel className="flex items-center justify-between">
              <span>Select Agent</span>
              {onRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRefresh}
                  className="h-6 w-6 p-0"
                  disabled={isLoading}
                >
                  <RefreshCw className={cn("w-3 h-3", isLoading && "animate-spin")} />
                </Button>
              )}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />

            {availableAgents.length === 0 ? (
              <DropdownMenuItem disabled>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <AlertCircle className="w-4 h-4" />
                  <span>No agents available</span>
                </div>
              </DropdownMenuItem>
            ) : (
              availableAgents.map((agent) => {
                const AgentIcon = getAgentIcon(agent.name);
                const colorClass = getAgentColor(agent.name);
                const isSelected = agent.name === selectedAgent;
                
                return (
                  <DropdownMenuItem
                    key={agent.name}
                    onClick={() => handleAgentSelect(agent.name)}
                    className={cn(
                      "flex flex-col items-start gap-2 p-3 cursor-pointer",
                      isSelected && "bg-accent",
                      !agent.available && "opacity-50"
                    )}
                    disabled={!agent.available}
                  >
                    <div className="flex items-center gap-3 w-full">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className={cn("text-sm", colorClass)}>
                          <AgentIcon className="w-4 h-4" />
                        </AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm truncate">
                            {agent.name}
                          </span>
                          {isSelected && (
                            <Badge variant="secondary" className="h-5 px-2 text-xs">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Current
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-xs text-muted-foreground truncate">
                          {agent.description}
                        </p>
                      </div>

                      <div className="flex items-center gap-1">
                        {agent.available ? (
                          <Tooltip>
                            <TooltipTrigger>
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Agent is available</p>
                            </TooltipContent>
                          </Tooltip>
                        ) : (
                          <Tooltip>
                            <TooltipTrigger>
                              <AlertCircle className="w-4 h-4 text-red-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Agent is currently unavailable</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </div>
                    </div>

                    {showCapabilities && (agent.capabilities?.length > 0 || agent.tools?.length > 0) && (
                      <div className="flex flex-wrap gap-1 w-full">
                        {agent.capabilities?.slice(0, 3).map((capability) => (
                          <Badge
                            key={capability}
                            variant="outline"
                            className="h-5 px-2 text-xs"
                          >
                            <Zap className="w-3 h-3 mr-1" />
                            {capability}
                          </Badge>
                        ))}
                        {agent.tools?.slice(0, 2).map((tool) => (
                          <Badge
                            key={tool}
                            variant="secondary"
                            className="h-5 px-2 text-xs"
                          >
                            {tool}
                          </Badge>
                        ))}
                        {(agent.capabilities?.length > 3 || agent.tools?.length > 2) && (
                          <Badge variant="outline" className="h-5 px-2 text-xs">
                            +{(agent.capabilities?.length || 0) + (agent.tools?.length || 0) - 5} more
                          </Badge>
                        )}
                      </div>
                    )}
                  </DropdownMenuItem>
                );
              })
            )}

            {error && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem disabled>
                  <div className="flex items-center gap-2 text-red-600 text-xs">
                    <AlertCircle className="w-3 h-3" />
                    <span>{error.error}</span>
                  </div>
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {showStatus && currentAgent && (
          <div className="flex items-center gap-1">
            <div className={cn(
              "w-2 h-2 rounded-full",
              currentAgent.available ? "bg-green-500" : "bg-red-500"
            )} />
            <span className="text-xs text-muted-foreground">
              {currentAgent.available ? 'Online' : 'Offline'}
            </span>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}