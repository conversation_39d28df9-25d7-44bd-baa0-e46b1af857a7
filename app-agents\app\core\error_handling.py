"""
Comprehensive error handling for ADK integration.

This module provides a hierarchical exception system and error handling utilities
for ADK communication failures, server unavailability, and other integration issues.
"""

import logging
from typing import Optional, Dict, Any, Union
from enum import Enum
from fastapi import HTTPException, status
from app.models.chat_models import StreamingChunk


class ErrorSeverity(str, Enum):
    """Error severity levels for logging and handling"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ADKIntegrationError(Exception):
    """Base exception for all ADK integration errors"""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = "ADK_ERROR",
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.details = details or {}
        self.original_error = original_error
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/serialization"""
        return {
            "error_type": self.__class__.__name__,
            "error_message": self.message,  # Changed from "message" to avoid LogRecord conflict
            "error_code": self.error_code,
            "severity": self.severity.value,
            "details": self.details,
            "original_error": str(self.original_error) if self.original_error else None
        }


class ADKConnectionError(ADKIntegrationError):
    """Network/connection related errors when communicating with ADK server"""

    def __init__(
        self,
        message: str = "Failed to connect to ADK server",
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(
            message=message,
            error_code="ADK_CONNECTION_ERROR",
            severity=ErrorSeverity.HIGH,
            details=details,
            original_error=original_error
        )


class ADKServerUnavailableError(ADKIntegrationError):
    """ADK server is not available or not responding"""
    
    def __init__(
        self,
        message: str = "ADK server is currently unavailable",
        details: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        super().__init__(
            message=message,
            error_code="ADK_SERVER_UNAVAILABLE",
            severity=ErrorSeverity.HIGH,
            details=details,
            original_error=original_error
        )


class ADKAgentNotFoundError(ADKIntegrationError):
    """Requested agent is not available or not found"""
    
    def __init__(
        self, 
        agent_name: str,
        available_agents: Optional[list] = None,
        original_error: Optional[Exception] = None
    ):
        message = f"Agent '{agent_name}' is not available"
        details = {"agent_name": agent_name}
        if available_agents:
            details["available_agents"] = available_agents
            
        super().__init__(
            message=message,
            error_code="ADK_AGENT_NOT_FOUND",
            severity=ErrorSeverity.MEDIUM,
            details=details,
            original_error=original_error
        )


class ADKSessionError(ADKIntegrationError):
    """Session-related errors (creation, retrieval, management)"""
    
    def __init__(
        self, 
        message: str,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        original_error: Optional[Exception] = None
    ):
        details = {}
        if session_id:
            details["session_id"] = session_id
        if user_id:
            details["user_id"] = user_id
            
        super().__init__(
            message=message,
            error_code="ADK_SESSION_ERROR",
            severity=ErrorSeverity.MEDIUM,
            details=details,
            original_error=original_error
        )


class ADKStreamingError(ADKIntegrationError):
    """Streaming-related errors (SSE, event processing)"""
    
    def __init__(
        self, 
        message: str,
        event_data: Optional[Dict[str, Any]] = None,
        original_error: Optional[Exception] = None
    ):
        details = {}
        if event_data:
            details["event_data"] = event_data
            
        super().__init__(
            message=message,
            error_code="ADK_STREAMING_ERROR",
            severity=ErrorSeverity.MEDIUM,
            details=details,
            original_error=original_error
        )


class ADKTimeoutError(ADKIntegrationError):
    """Request timeout errors"""
    
    def __init__(
        self, 
        message: str = "ADK request timed out",
        timeout_seconds: Optional[float] = None,
        original_error: Optional[Exception] = None
    ):
        details = {}
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
            
        super().__init__(
            message=message,
            error_code="ADK_TIMEOUT",
            severity=ErrorSeverity.HIGH,
            details=details,
            original_error=original_error
        )


class ADKAuthenticationError(ADKIntegrationError):
    """Authentication and authorization errors"""
    
    def __init__(
        self, 
        message: str = "Authentication failed for ADK request",
        user_id: Optional[str] = None,
        original_error: Optional[Exception] = None
    ):
        details = {}
        if user_id:
            details["user_id"] = user_id
            
        super().__init__(
            message=message,
            error_code="ADK_AUTH_ERROR",
            severity=ErrorSeverity.HIGH,
            details=details,
            original_error=original_error
        )


class ADKValidationError(ADKIntegrationError):
    """Request/response validation errors"""
    
    def __init__(
        self, 
        message: str,
        validation_errors: Optional[list] = None,
        original_error: Optional[Exception] = None
    ):
        details = {}
        if validation_errors:
            details["validation_errors"] = validation_errors
            
        super().__init__(
            message=message,
            error_code="ADK_VALIDATION_ERROR",
            severity=ErrorSeverity.MEDIUM,
            details=details,
            original_error=original_error
        )


class ErrorHandler:
    """Centralized error handling for ADK integration"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
    
    def log_error(self, error: ADKIntegrationError) -> None:
        """Log error with appropriate level based on severity"""
        error_dict = error.to_dict()
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical("ADK Integration Error", extra=error_dict)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error("ADK Integration Error", extra=error_dict)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning("ADK Integration Error", extra=error_dict)
        else:
            self.logger.info("ADK Integration Error", extra=error_dict)
    
    def get_user_friendly_message(self, error: ADKIntegrationError) -> str:
        """Get user-friendly error message based on error type"""
        
        if isinstance(error, ADKServerUnavailableError):
            return "I'm temporarily unavailable. Please try again in a moment."
        
        elif isinstance(error, ADKAgentNotFoundError):
            available = error.details.get("available_agents", [])
            if available:
                return f"The requested agent is not available. Available agents: {', '.join(available)}"
            return "The requested agent is not available. Using default assistant."
        
        elif isinstance(error, ADKSessionError):
            return "There was an issue with your conversation session. Starting a new session."
        
        elif isinstance(error, ADKStreamingError):
            return "There was an issue with the response stream. Please try sending your message again."
        
        elif isinstance(error, ADKTimeoutError):
            return "The request is taking longer than expected. Please try again."
        
        elif isinstance(error, ADKAuthenticationError):
            return "Authentication failed. Please refresh the page and try again."
        
        elif isinstance(error, ADKValidationError):
            return "There was an issue with your request format. Please try again."
        
        else:
            return "I encountered an unexpected issue. Please try again."
    
    def create_error_response(
        self, 
        error: Union[ADKIntegrationError, Exception],
        fallback_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create standardized error response"""
        
        if not isinstance(error, ADKIntegrationError):
            # Wrap generic exceptions
            error = ADKIntegrationError(
                message=str(error),
                original_error=error
            )
        
        self.log_error(error)
        
        user_message = fallback_message or self.get_user_friendly_message(error)
        
        return {
            "error": True,
            "message": user_message,
            "error_code": error.error_code,
            "severity": error.severity.value,
            "timestamp": None  # Will be set by middleware
        }
    
    def create_error_stream_chunk(
        self, 
        error: Union[ADKIntegrationError, Exception],
        message_id: str = "error"
    ) -> StreamingChunk:
        """Create error streaming chunk for SSE responses"""
        
        error_response = self.create_error_response(error)
        
        return StreamingChunk(
            content=error_response["message"],
            done=True,
            message_id=message_id,
            metadata={
                "error": True,
                "error_code": error_response["error_code"],
                "severity": error_response["severity"]
            }
        )
    
    def create_http_exception(
        self, 
        error: Union[ADKIntegrationError, Exception],
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    ) -> HTTPException:
        """Create FastAPI HTTPException from ADK error"""
        
        error_response = self.create_error_response(error)
        
        # Map error types to appropriate HTTP status codes
        if isinstance(error, ADKServerUnavailableError):
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        elif isinstance(error, ADKAgentNotFoundError):
            status_code = status.HTTP_404_NOT_FOUND
        elif isinstance(error, ADKAuthenticationError):
            status_code = status.HTTP_401_UNAUTHORIZED
        elif isinstance(error, ADKValidationError):
            status_code = status.HTTP_400_BAD_REQUEST
        elif isinstance(error, ADKTimeoutError):
            status_code = status.HTTP_408_REQUEST_TIMEOUT
        
        return HTTPException(
            status_code=status_code,
            detail=error_response
        )


# Global error handler instance
error_handler = ErrorHandler()


def handle_adk_error(
    error: Union[ADKIntegrationError, Exception],
    fallback_message: Optional[str] = None
) -> Dict[str, Any]:
    """Convenience function for handling ADK errors"""
    return error_handler.create_error_response(error, fallback_message)


def create_error_stream(
    error: Union[ADKIntegrationError, Exception],
    message_id: str = "error"
) -> str:
    """Convenience function for creating error SSE stream"""
    chunk = error_handler.create_error_stream_chunk(error, message_id)
    return f"data: {chunk.model_dump_json()}\n\n"