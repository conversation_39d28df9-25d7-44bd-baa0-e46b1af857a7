#!/bin/bash

# Cloud Run Deployment Script for Social Media Manager
# This script automates the complete deployment process to Google Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=""
REGION="us-central1"
BACKEND_SERVICE="social-media-backend"
FRONTEND_SERVICE="social-media-frontend"
BACKEND_IMAGE="gcr.io/${PROJECT_ID}/${BACKEND_SERVICE}"
FRONTEND_IMAGE="gcr.io/${PROJECT_ID}/${FRONTEND_SERVICE}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install it first."
        exit 1
    fi
    
    # Check if project ID is set
    if [ -z "$PROJECT_ID" ]; then
        PROJECT_ID=$(gcloud config get-value project)
        if [ -z "$PROJECT_ID" ]; then
            print_error "Google Cloud project ID is not set. Please set it with 'gcloud config set project PROJECT_ID'"
            exit 1
        fi
    fi
    
    print_success "Prerequisites check completed. Using project: $PROJECT_ID"
}

# Function to enable required APIs
enable_apis() {
    print_status "Enabling required Google Cloud APIs..."
    
    gcloud services enable run.googleapis.com --project=$PROJECT_ID
    gcloud services enable cloudbuild.googleapis.com --project=$PROJECT_ID
    gcloud services enable containerregistry.googleapis.com --project=$PROJECT_ID
    gcloud services enable secretmanager.googleapis.com --project=$PROJECT_ID
    gcloud services enable firestore.googleapis.com --project=$PROJECT_ID
    gcloud services enable bigquery.googleapis.com --project=$PROJECT_ID
    
    print_success "APIs enabled successfully"
}

# Function to setup IAM and service accounts
setup_iam() {
    print_status "Setting up IAM and service accounts..."
    
    # Create service account for backend if it doesn't exist
    if ! gcloud iam service-accounts describe social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com --project=$PROJECT_ID &> /dev/null; then
        gcloud iam service-accounts create social-media-backend \
            --display-name="Social Media Backend Service" \
            --project=$PROJECT_ID
    fi
    
    # Grant necessary permissions
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
        --role="roles/secretmanager.secretAccessor"
    
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
        --role="roles/datastore.user"
    
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
        --role="roles/bigquery.user"
    
    print_success "IAM setup completed"
}

# Function to create secrets
create_secrets() {
    print_status "Creating secrets in Secret Manager..."
    
    # Create secrets if they don't exist
    create_secret_if_not_exists() {
        local secret_name=$1
        local secret_value=$2
        
        if ! gcloud secrets describe $secret_name --project=$PROJECT_ID &> /dev/null; then
            echo -n "$secret_value" | gcloud secrets create $secret_name \
                --data-file=- \
                --project=$PROJECT_ID
            print_success "Created secret: $secret_name"
        else
            print_warning "Secret $secret_name already exists"
        fi
    }
    
    # Prompt for secret values if not set as environment variables
    if [ -z "$SECRET_KEY" ]; then
        read -p "Enter SECRET_KEY for JWT tokens: " SECRET_KEY
    fi
    
    if [ -z "$GOOGLE_CLIENT_ID" ]; then
        read -p "Enter Google OAuth Client ID: " GOOGLE_CLIENT_ID
    fi
    
    if [ -z "$GOOGLE_CLIENT_SECRET" ]; then
        read -s -p "Enter Google OAuth Client Secret: " GOOGLE_CLIENT_SECRET
        echo
    fi
    
    if [ -z "$YOUTUBE_API_KEY" ]; then
        read -s -p "Enter YouTube API Key: " YOUTUBE_API_KEY
        echo
    fi
    
    if [ -z "$INSTAGRAM_APP_ID" ]; then
        read -p "Enter Instagram App ID: " INSTAGRAM_APP_ID
    fi
    
    if [ -z "$INSTAGRAM_APP_SECRET" ]; then
        read -s -p "Enter Instagram App Secret: " INSTAGRAM_APP_SECRET
        echo
    fi
    
    # Create secrets
    create_secret_if_not_exists "app-secret-key" "$SECRET_KEY"
    create_secret_if_not_exists "google-client-id" "$GOOGLE_CLIENT_ID"
    create_secret_if_not_exists "google-client-secret" "$GOOGLE_CLIENT_SECRET"
    create_secret_if_not_exists "youtube-api-key" "$YOUTUBE_API_KEY"
    create_secret_if_not_exists "instagram-app-id" "$INSTAGRAM_APP_ID"
    create_secret_if_not_exists "instagram-app-secret" "$INSTAGRAM_APP_SECRET"
    
    print_success "Secrets setup completed"
}

# Function to build and push Docker images
build_and_push_images() {
    print_status "Building and pushing Docker images..."
    
    # Configure Docker for gcloud
    gcloud auth configure-docker --quiet
    
    # Build backend image
    print_status "Building backend image..."
    docker build -t $BACKEND_IMAGE ./app-agents
    
    # Build frontend image
    print_status "Building frontend image..."
    docker build -t $FRONTEND_IMAGE ./app-frontend
    
    # Push images
    print_status "Pushing backend image..."
    docker push $BACKEND_IMAGE
    
    print_status "Pushing frontend image..."
    docker push $FRONTEND_IMAGE
    
    print_success "Docker images built and pushed successfully"
}

# Function to deploy backend service
deploy_backend() {
    print_status "Deploying backend service to Cloud Run..."
    
    # Replace PROJECT_ID in the configuration file
    sed "s/PROJECT_ID/$PROJECT_ID/g" deployment/cloud-run-backend.yaml > /tmp/backend-config.yaml
    
    # Deploy using gcloud with the configuration
    gcloud run services replace /tmp/backend-config.yaml \
        --region=$REGION \
        --project=$PROJECT_ID
    
    # Get the backend URL
    BACKEND_URL=$(gcloud run services describe $BACKEND_SERVICE \
        --region=$REGION \
        --project=$PROJECT_ID \
        --format="value(status.url)")
    
    print_success "Backend deployed successfully at: $BACKEND_URL"
    
    # Clean up temporary file
    rm /tmp/backend-config.yaml
}

# Function to deploy frontend service
deploy_frontend() {
    print_status "Deploying frontend service to Cloud Run..."
    
    # Replace PROJECT_ID and BACKEND_URL in the configuration file
    sed -e "s/PROJECT_ID/$PROJECT_ID/g" \
        -e "s|https://your-backend-domain.com|$BACKEND_URL|g" \
        deployment/cloud-run-frontend.yaml > /tmp/frontend-config.yaml
    
    # Deploy using gcloud with the configuration
    gcloud run services replace /tmp/frontend-config.yaml \
        --region=$REGION \
        --project=$PROJECT_ID
    
    # Get the frontend URL
    FRONTEND_URL=$(gcloud run services describe $FRONTEND_SERVICE \
        --region=$REGION \
        --project=$PROJECT_ID \
        --format="value(status.url)")
    
    print_success "Frontend deployed successfully at: $FRONTEND_URL"
    
    # Clean up temporary file
    rm /tmp/frontend-config.yaml
}

# Function to setup custom domains (optional)
setup_custom_domains() {
    read -p "Do you want to setup custom domains? (y/N): " setup_domains
    
    if [[ $setup_domains =~ ^[Yy]$ ]]; then
        read -p "Enter custom domain for backend (e.g., api.yourdomain.com): " BACKEND_DOMAIN
        read -p "Enter custom domain for frontend (e.g., app.yourdomain.com): " FRONTEND_DOMAIN
        
        if [ ! -z "$BACKEND_DOMAIN" ]; then
            print_status "Setting up custom domain for backend: $BACKEND_DOMAIN"
            gcloud run domain-mappings create \
                --service=$BACKEND_SERVICE \
                --domain=$BACKEND_DOMAIN \
                --region=$REGION \
                --project=$PROJECT_ID
        fi
        
        if [ ! -z "$FRONTEND_DOMAIN" ]; then
            print_status "Setting up custom domain for frontend: $FRONTEND_DOMAIN"
            gcloud run domain-mappings create \
                --service=$FRONTEND_SERVICE \
                --domain=$FRONTEND_DOMAIN \
                --region=$REGION \
                --project=$PROJECT_ID
        fi
        
        print_success "Custom domains setup completed"
    fi
}

# Function to setup monitoring and logging
setup_monitoring() {
    print_status "Setting up monitoring and logging..."
    
    # Create log-based metrics
    gcloud logging metrics create social_media_error_rate \
        --description="Error rate for social media manager" \
        --log-filter='resource.type="cloud_run_revision" AND severity="ERROR"' \
        --project=$PROJECT_ID
    
    # Create alerting policy (requires additional setup)
    print_warning "Monitoring setup basic logging. For advanced monitoring, please configure Cloud Monitoring manually."
    
    print_success "Basic monitoring setup completed"
}

# Function to run post-deployment tests
run_health_checks() {
    print_status "Running health checks..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check backend health
    if curl -f "$BACKEND_URL/health" > /dev/null 2>&1; then
        print_success "Backend health check passed"
    else
        print_error "Backend health check failed"
    fi
    
    # Check frontend health
    if curl -f "$FRONTEND_URL/api/health" > /dev/null 2>&1; then
        print_success "Frontend health check passed"
    else
        print_error "Frontend health check failed"
    fi
}

# Function to display deployment summary
show_deployment_summary() {
    print_success "🎉 Deployment completed successfully!"
    echo
    echo "📋 Deployment Summary:"
    echo "  Project ID: $PROJECT_ID"
    echo "  Region: $REGION"
    echo "  Backend URL: $BACKEND_URL"
    echo "  Frontend URL: $FRONTEND_URL"
    echo
    echo "🔗 Useful Links:"
    echo "  Cloud Console: https://console.cloud.google.com/run?project=$PROJECT_ID"
    echo "  Cloud Logging: https://console.cloud.google.com/logs/query?project=$PROJECT_ID"
    echo "  Secret Manager: https://console.cloud.google.com/security/secret-manager?project=$PROJECT_ID"
    echo
    echo "📋 Next Steps:"
    echo "  1. Update OAuth redirect URIs with the deployed URLs"
    echo "  2. Test the application functionality"
    echo "  3. Setup custom domains if needed"
    echo "  4. Configure monitoring and alerting"
    echo "  5. Setup CI/CD pipeline for automated deployments"
}

# Main deployment function
main() {
    echo "🚀 Social Media Manager - Cloud Run Deployment"
    echo "=============================================="
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --project)
                PROJECT_ID="$2"
                shift 2
                ;;
            --region)
                REGION="$2"
                shift 2
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-secrets)
                SKIP_SECRETS=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [--project PROJECT_ID] [--region REGION] [--skip-build] [--skip-secrets]"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Update image names with project ID
    BACKEND_IMAGE="gcr.io/${PROJECT_ID}/${BACKEND_SERVICE}"
    FRONTEND_IMAGE="gcr.io/${PROJECT_ID}/${FRONTEND_SERVICE}"
    
    # Run deployment steps
    check_prerequisites
    enable_apis
    setup_iam
    
    if [ "$SKIP_SECRETS" != true ]; then
        create_secrets
    fi
    
    if [ "$SKIP_BUILD" != true ]; then
        build_and_push_images
    fi
    
    deploy_backend
    deploy_frontend
    setup_custom_domains
    setup_monitoring
    run_health_checks
    show_deployment_summary
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT

# Run main function with all arguments
main "$@"