/**
 * Type Definitions Index
 * 
 * Central export point for all ADK and chat-related type definitions.
 * This provides a clean import interface for components and utilities.
 */

// ADK Core Types
export * from './adk';

// Enhanced Chat Types
export * from './chat';

// Re-export commonly used types for convenience
export type {
  // Core ADK types
  ADKEvent,
  ADKRunAgentRequest,
  ADKAgentInfo,
  ADKSessionInfo,
  StreamingChunk,
  
  // Chat types
  EnhancedChatMessage,
  ChatSession,
  ChatMessageRequest,
  ChatMessageResponse,
  ChatError,
  
  // Component prop types
  ADKChatProps,
  AgentSelectorProps,
  StreamingMessageProps,
  
  // Hook return types
  UseADKChatReturn,
  UseAgentListReturn,
  UseStreamingReturn,
  
  // Configuration types
  ADKIntegrationConfig
} from './adk';

export type {
  // Extended chat types
  MessageListProps,
  MessageInputProps,
  ChatLayoutProps,
  ChatHeaderProps,
  ChatSidebarProps,
  
  // Function call types
  FunctionCallCardProps,
  ToolExecutionListProps,
  ToolExecutionDetailProps,
  
  // Agent management types
  AgentCardProps,
  AgentListProps,
  AgentSwitchDialogProps,
  
  // Session management types
  SessionListProps,
  SessionCardProps,
  NewSessionDialogProps,
  
  // Error and status types
  ChatErrorDisplayProps,
  LoadingStateProps,
  ConnectionStatusProps,
  
  // Utility types
  ChatMetrics,
  ChatAnalytics,
  MessageFilters,
  SearchResult,
  PerformanceMetrics
} from './chat';