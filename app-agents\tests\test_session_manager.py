"""
Tests for ADK Session Management Integration

This module contains comprehensive tests for the SessionManager class,
covering session creation, retrieval, caching, and lifecycle management.

Requirements covered: 3.1, 3.2, 3.3
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
from typing import Dict, Any

from app.services.session_manager import (
    SessionManager,
    SessionManagerError,
    SessionNotFoundError,
    SessionCreationError,
    session_manager_context,
    get_session_manager
)
from app.services.adk_service import ADKService, ADKConnectionError, ADKAgentNotFoundError
from app.models.adk_models import (
    ADKSessionInfo,
    ADKSessionCreateResponse,
    ADKAgentInfo
)


class TestSessionManager:
    """Test cases for SessionManager class"""
    
    @pytest.fixture
    def mock_adk_service(self):
        """Create mock ADK service"""
        service = AsyncMock(spec=ADKService)
        return service
    
    @pytest.fixture
    def session_manager(self, mock_adk_service):
        """Create SessionManager instance with mock ADK service"""
        return SessionManager(
            adk_service=mock_adk_service,
            cache_ttl=timedelta(minutes=5),
            max_cache_size=100
        )
    
    @pytest.fixture
    def sample_session_response(self):
        """Sample session creation response"""
        return ADKSessionCreateResponse(
            id="session_123",
            app_name="content_planner",
            user_id="user_456",
            created_at=datetime.now()
        )
    
    @pytest.fixture
    def sample_session_info(self):
        """Sample session info"""
        return ADKSessionInfo(
            id="session_123",
            app_name="content_planner",
            user_id="user_456",
            created_at=datetime.now(),
            last_activity=datetime.now(),
            event_count=5,
            metadata={"test": "data"}
        )
    
    @pytest.mark.asyncio
    async def test_create_session_success(self, session_manager, mock_adk_service, sample_session_response):
        """Test successful session creation"""
        # Setup mock
        mock_adk_service.create_session.return_value = sample_session_response
        
        # Test session creation
        result = await session_manager.create_session(
            app_name="content_planner",
            user_id="user_456",
            initial_state={"key": "value"}
        )
        
        # Verify result
        assert result.id == "session_123"
        assert result.app_name == "content_planner"
        assert result.user_id == "user_456"
        
        # Verify ADK service was called correctly
        mock_adk_service.create_session.assert_called_once_with(
            app_name="content_planner",
            user_id="user_456",
            initial_state={"key": "value"}
        )
        
        # Verify session is cached
        session_key = session_manager._get_session_key("content_planner", "user_456", "session_123")
        assert session_key in session_manager._session_cache
        
        # Verify user mapping is updated
        user_key = session_manager._get_user_key("user_456", "content_planner")
        assert session_manager._user_sessions[user_key] == "session_123"
    
    @pytest.mark.asyncio
    async def test_create_session_agent_not_found(self, session_manager, mock_adk_service):
        """Test session creation with non-existent agent"""
        # Setup mock to raise agent not found error
        mock_adk_service.create_session.side_effect = ADKAgentNotFoundError("Agent not found")
        
        # Test session creation should raise error
        with pytest.raises(ADKAgentNotFoundError):
            await session_manager.create_session(
                app_name="nonexistent_agent",
                user_id="user_456"
            )
    
    @pytest.mark.asyncio
    async def test_create_session_connection_error(self, session_manager, mock_adk_service):
        """Test session creation with connection error"""
        # Setup mock to raise connection error
        mock_adk_service.create_session.side_effect = ADKConnectionError("Connection failed")
        
        # Test session creation should raise SessionCreationError
        with pytest.raises(SessionCreationError):
            await session_manager.create_session(
                app_name="content_planner",
                user_id="user_456"
            )
    
    @pytest.mark.asyncio
    async def test_create_session_invalid_inputs(self, session_manager):
        """Test session creation with invalid inputs"""
        # Test invalid app name
        with pytest.raises(ValueError, match="Invalid app name"):
            await session_manager.create_session(
                app_name="",
                user_id="user_456"
            )
        
        # Test invalid user ID
        with pytest.raises(ValueError, match="Invalid user ID"):
            await session_manager.create_session(
                app_name="content_planner",
                user_id=""
            )
    
    @pytest.mark.asyncio
    async def test_get_session_success(self, session_manager, mock_adk_service, sample_session_info):
        """Test successful session retrieval"""
        # Setup mock
        mock_adk_service.get_session.return_value = sample_session_info
        
        # Test session retrieval
        result = await session_manager.get_session(
            app_name="content_planner",
            user_id="user_456",
            session_id="session_123"
        )
        
        # Verify result
        assert result is not None
        assert result.id == "session_123"
        assert result.app_name == "content_planner"
        assert result.user_id == "user_456"
        
        # Verify ADK service was called
        mock_adk_service.get_session.assert_called_once_with(
            "content_planner", "user_456", "session_123"
        )
        
        # Verify session is cached
        session_key = session_manager._get_session_key("content_planner", "user_456", "session_123")
        assert session_key in session_manager._session_cache
    
    @pytest.mark.asyncio
    async def test_get_session_not_found(self, session_manager, mock_adk_service):
        """Test session retrieval when session doesn't exist"""
        # Setup mock to return None
        mock_adk_service.get_session.return_value = None
        
        # Test session retrieval
        result = await session_manager.get_session(
            app_name="content_planner",
            user_id="user_456",
            session_id="nonexistent_session"
        )
        
        # Verify result is None
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_session_from_cache(self, session_manager, mock_adk_service, sample_session_info):
        """Test session retrieval from cache"""
        # Pre-populate cache
        session_key = session_manager._get_session_key("content_planner", "user_456", "session_123")
        session_manager._session_cache[session_key] = (sample_session_info, datetime.now())
        
        # Test session retrieval
        result = await session_manager.get_session(
            app_name="content_planner",
            user_id="user_456",
            session_id="session_123"
        )
        
        # Verify result from cache
        assert result is not None
        assert result.id == "session_123"
        
        # Verify ADK service was NOT called (cache hit)
        mock_adk_service.get_session.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_session_expired_cache(self, session_manager, mock_adk_service, sample_session_info):
        """Test session retrieval with expired cache"""
        # Pre-populate cache with expired entry
        session_key = session_manager._get_session_key("content_planner", "user_456", "session_123")
        expired_time = datetime.now() - timedelta(hours=1)
        session_manager._session_cache[session_key] = (sample_session_info, expired_time)
        
        # Setup mock for fresh retrieval
        mock_adk_service.get_session.return_value = sample_session_info
        
        # Test session retrieval
        result = await session_manager.get_session(
            app_name="content_planner",
            user_id="user_456",
            session_id="session_123"
        )
        
        # Verify result
        assert result is not None
        assert result.id == "session_123"
        
        # Verify ADK service was called (cache miss due to expiration)
        mock_adk_service.get_session.assert_called_once()
        
        # Verify expired entry was removed and new one added
        cached_session, cache_time = session_manager._session_cache[session_key]
        assert cache_time > expired_time
    
    @pytest.mark.asyncio
    async def test_get_session_invalid_inputs(self, session_manager):
        """Test session retrieval with invalid inputs"""
        # Test invalid app name
        with pytest.raises(ValueError, match="Invalid app name"):
            await session_manager.get_session(
                app_name="",
                user_id="user_456",
                session_id="session_123"
            )
        
        # Test invalid user ID
        with pytest.raises(ValueError, match="Invalid user ID"):
            await session_manager.get_session(
                app_name="content_planner",
                user_id="",
                session_id="session_123"
            )
        
        # Test invalid session ID
        with pytest.raises(ValueError, match="Invalid session ID"):
            await session_manager.get_session(
                app_name="content_planner",
                user_id="user_456",
                session_id=""
            )
    
    @pytest.mark.asyncio
    async def test_get_or_create_session_existing(self, session_manager, mock_adk_service, sample_session_info):
        """Test get_or_create_session with existing session"""
        # Setup mock to return existing session
        mock_adk_service.get_session.return_value = sample_session_info
        
        # Test get_or_create_session
        result = await session_manager.get_or_create_session(
            app_name="content_planner",
            user_id="user_456",
            session_id="session_123"
        )
        
        # Verify existing session is returned
        assert result.id == "session_123"
        assert result.app_name == "content_planner"
        
        # Verify only get_session was called, not create_session
        mock_adk_service.get_session.assert_called_once()
        mock_adk_service.create_session.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_or_create_session_new(self, session_manager, mock_adk_service, sample_session_response):
        """Test get_or_create_session creating new session"""
        # Setup mocks
        mock_adk_service.get_session.return_value = None  # Session not found
        mock_adk_service.create_session.return_value = sample_session_response
        
        # Test get_or_create_session
        result = await session_manager.get_or_create_session(
            app_name="content_planner",
            user_id="user_456",
            session_id="nonexistent_session"
        )
        
        # Verify new session is created
        assert result.id == "session_123"
        assert result.app_name == "content_planner"
        
        # Verify both get and create were called
        mock_adk_service.get_session.assert_called_once()
        mock_adk_service.create_session.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_or_create_session_no_session_id(self, session_manager, mock_adk_service, sample_session_response):
        """Test get_or_create_session without session_id"""
        # Setup mock
        mock_adk_service.create_session.return_value = sample_session_response
        
        # Test get_or_create_session without session_id
        result = await session_manager.get_or_create_session(
            app_name="content_planner",
            user_id="user_456"
        )
        
        # Verify new session is created
        assert result.id == "session_123"
        
        # Verify only create_session was called
        mock_adk_service.get_session.assert_not_called()
        mock_adk_service.create_session.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_user_sessions(self, session_manager, sample_session_info):
        """Test retrieving all sessions for a user"""
        # Pre-populate cache with multiple sessions
        session1 = sample_session_info
        session2 = ADKSessionInfo(
            id="session_456",
            app_name="research_agent",
            user_id="user_456",
            created_at=datetime.now(),
            last_activity=datetime.now(),
            event_count=3
        )
        session3 = ADKSessionInfo(
            id="session_789",
            app_name="content_planner",
            user_id="other_user",
            created_at=datetime.now(),
            last_activity=datetime.now(),
            event_count=1
        )
        
        # Add to cache
        now = datetime.now()
        session_manager._session_cache.update({
            "content_planner:user_456:session_123": (session1, now),
            "research_agent:user_456:session_456": (session2, now),
            "content_planner:other_user:session_789": (session3, now)
        })
        
        # Test getting all sessions for user_456
        user_sessions = await session_manager.get_user_sessions("user_456")
        
        # Verify results
        assert len(user_sessions) == 2
        session_ids = [s.id for s in user_sessions]
        assert "session_123" in session_ids
        assert "session_456" in session_ids
        assert "session_789" not in session_ids  # Different user
        
        # Test filtering by agent
        content_sessions = await session_manager.get_user_sessions("user_456", "content_planner")
        assert len(content_sessions) == 1
        assert content_sessions[0].id == "session_123"
    
    @pytest.mark.asyncio
    async def test_invalidate_session(self, session_manager, sample_session_info):
        """Test session invalidation"""
        # Pre-populate cache and user mapping
        session_key = "content_planner:user_456:session_123"
        user_key = "user_456:content_planner"
        
        session_manager._session_cache[session_key] = (sample_session_info, datetime.now())
        session_manager._user_sessions[user_key] = "session_123"
        
        # Test invalidation
        await session_manager.invalidate_session(
            app_name="content_planner",
            user_id="user_456",
            session_id="session_123"
        )
        
        # Verify session is removed from cache and user mapping
        assert session_key not in session_manager._session_cache
        assert user_key not in session_manager._user_sessions
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_sessions(self, session_manager, sample_session_info):
        """Test cleanup of expired sessions"""
        # Pre-populate cache with mix of valid and expired sessions
        now = datetime.now()
        expired_time = now - timedelta(hours=1)
        
        session_manager._session_cache.update({
            "valid_session": (sample_session_info, now),
            "expired_session1": (sample_session_info, expired_time),
            "expired_session2": (sample_session_info, expired_time)
        })
        
        # Test cleanup
        cleaned_count = await session_manager.cleanup_expired_sessions()
        
        # Verify expired sessions were removed
        assert cleaned_count == 2
        assert len(session_manager._session_cache) == 1
        assert "valid_session" in session_manager._session_cache
        assert "expired_session1" not in session_manager._session_cache
        assert "expired_session2" not in session_manager._session_cache
    
    def test_get_cache_stats(self, session_manager, sample_session_info):
        """Test cache statistics"""
        # Pre-populate cache
        now = datetime.now()
        expired_time = now - timedelta(hours=1)
        
        session_manager._session_cache.update({
            "valid1": (sample_session_info, now),
            "valid2": (sample_session_info, now),
            "expired1": (sample_session_info, expired_time)
        })
        
        session_manager._user_sessions.update({
            "user1:agent1": "session1",
            "user2:agent2": "session2"
        })
        
        # Test cache stats
        stats = session_manager.get_cache_stats()
        
        # Verify stats
        assert stats["total_cached_sessions"] == 3
        assert stats["valid_sessions"] == 2
        assert stats["expired_sessions"] == 1
        assert stats["user_mappings"] == 2
        assert stats["cache_ttl_seconds"] == 300  # 5 minutes
        assert stats["max_cache_size"] == 100
    
    @pytest.mark.asyncio
    async def test_concurrent_session_creation(self, session_manager, mock_adk_service, sample_session_response):
        """Test concurrent session creation for same user/agent"""
        # Setup mock with delay to simulate race condition
        async def delayed_create_session(*args, **kwargs):
            await asyncio.sleep(0.1)
            return sample_session_response
        
        mock_adk_service.create_session.side_effect = delayed_create_session
        
        # Start multiple concurrent session creation tasks
        tasks = [
            session_manager.create_session("content_planner", "user_456")
            for _ in range(3)
        ]
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)
        
        # Verify all tasks got the same session (due to locking)
        assert all(r.id == "session_123" for r in results)
        
        # Verify ADK service was called only once (due to locking and caching)
        assert mock_adk_service.create_session.call_count <= 3  # May be called multiple times due to race conditions
    
    @pytest.mark.asyncio
    async def test_force_new_session(self, session_manager, mock_adk_service, sample_session_response, sample_session_info):
        """Test forcing creation of new session"""
        # Pre-populate user mapping with existing session
        user_key = "user_456:content_planner"
        session_manager._user_sessions[user_key] = "existing_session"
        
        # Setup mocks
        mock_adk_service.get_session.return_value = sample_session_info
        mock_adk_service.create_session.return_value = sample_session_response
        
        # Test creating session with force_new=True
        result = await session_manager.create_session(
            app_name="content_planner",
            user_id="user_456",
            force_new=True
        )
        
        # Verify new session was created despite existing session
        assert result.id == "session_123"
        
        # Verify create_session was called (not just get_session)
        mock_adk_service.create_session.assert_called_once()


class TestSessionManagerContextManager:
    """Test cases for session manager context manager"""
    
    @pytest.mark.asyncio
    async def test_session_manager_context(self):
        """Test session manager context manager"""
        mock_adk_service = AsyncMock(spec=ADKService)
        
        async with session_manager_context(mock_adk_service) as session_mgr:
            assert isinstance(session_mgr, SessionManager)
            assert session_mgr.adk_service is mock_adk_service
    
    @pytest.mark.asyncio
    async def test_session_manager_context_cleanup(self):
        """Test session manager context manager cleanup"""
        mock_adk_service = AsyncMock(spec=ADKService)
        
        with patch.object(SessionManager, 'cleanup_expired_sessions') as mock_cleanup:
            mock_cleanup.return_value = 0
            
            async with session_manager_context(mock_adk_service) as session_mgr:
                pass  # Context manager should call cleanup on exit
            
            mock_cleanup.assert_called_once()


class TestSessionManagerGlobalInstance:
    """Test cases for global session manager instance"""
    
    @pytest.mark.asyncio
    async def test_get_session_manager(self):
        """Test getting global session manager instance"""
        mock_adk_service = AsyncMock(spec=ADKService)
        
        # Clear global instance
        import app.services.session_manager
        app.services.session_manager._session_manager = None
        
        # Test getting session manager
        session_mgr = await get_session_manager(mock_adk_service)
        
        assert isinstance(session_mgr, SessionManager)
        assert session_mgr.adk_service is mock_adk_service
        
        # Test getting same instance again
        session_mgr2 = await get_session_manager(mock_adk_service)
        assert session_mgr is session_mgr2  # Should be same instance
    
    @pytest.mark.asyncio
    async def test_get_session_manager_auto_adk_service(self):
        """Test getting session manager with automatic ADK service creation"""
        # Clear global instance
        import app.services.session_manager
        app.services.session_manager._session_manager = None
        
        with patch('app.services.adk_service.get_adk_service') as mock_get_adk:
            mock_adk_service = AsyncMock(spec=ADKService)
            mock_get_adk.return_value = mock_adk_service
            
            # Test getting session manager without providing ADK service
            session_mgr = await get_session_manager()
            
            assert isinstance(session_mgr, SessionManager)
            mock_get_adk.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])