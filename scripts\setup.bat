@echo off
:: Social Media Manager - Setup Script (Windows)
:: This script helps you set up the development environment quickly

echo 🚀 Setting up Social Media Manager Development Environment
echo ==========================================================

:: Check prerequisites
echo 📋 Checking prerequisites...

:: Check Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

:: Check Python
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.11+ from https://python.org/
    pause
    exit /b 1
)

:: Check Docker (optional)
where docker >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ Docker found - Docker setup will be available
    set DOCKER_AVAILABLE=true
) else (
    echo ⚠️  Docker not found - manual setup will be used
    set DOCKER_AVAILABLE=false
)

echo ✅ Prerequisites check completed!
echo.

:: Setup Frontend
echo 🎨 Setting up Frontend ^(Next.js^)...
cd app-frontend

if not exist ".env.local" (
    echo 📄 Creating frontend environment file...
    copy .env.example .env.local >nul
    echo ✅ Created .env.local from template
    echo ⚠️  Please edit app-frontend\.env.local with your configuration
)

echo 📦 Installing frontend dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)
echo ✅ Frontend dependencies installed!

cd ..

:: Setup Backend
echo 🔧 Setting up Backend ^(FastAPI^)...
cd app-agents

if not exist ".env" (
    echo 📄 Creating backend environment file...
    copy .env.example .env >nul
    echo ✅ Created .env from template
    echo ⚠️  Please edit app-agents\.env with your API keys
)

:: Create virtual environment
if not exist "venv" (
    echo 🐍 Creating Python virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created!
)

:: Activate virtual environment and install dependencies
echo 📦 Installing backend dependencies...
call venv\Scripts\activate.bat
python -m pip install --upgrade pip
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)
echo ✅ Backend dependencies installed!

cd ..

:: Create necessary directories
echo 📁 Creating additional directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "downloads" mkdir downloads
echo ✅ Project directories created!

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 Next Steps:
echo 1. Configure your environment variables:
echo    - Edit app-frontend\.env.local
echo    - Edit app-agents\.env
echo.
echo 2. Start the development servers:
echo.

if "%DOCKER_AVAILABLE%"=="true" (
    echo    Option A: Using Docker Compose ^(Recommended^)
    echo    docker-compose up --build
    echo.
)

echo    Option B: Manual startup
echo    # Terminal 1 - Backend
echo    cd app-agents
echo    venv\Scripts\activate.bat
echo    uvicorn main:app --reload --host 0.0.0.0 --port 8000
echo.
echo    # Terminal 2 - Frontend
echo    cd app-frontend
echo    npm run dev
echo.
echo 3. Access the application:
echo    - Frontend: http://localhost:3000
echo    - Backend API: http://localhost:8000
echo    - API Docs: http://localhost:8000/docs
echo.
echo 📚 For more information, see README.md
echo.
echo Happy coding! 🚀

pause