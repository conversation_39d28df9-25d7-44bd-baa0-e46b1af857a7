"""
ADK Models Package

This package contains all data models, type definitions, and validation schemas
for ADK integration with the chat system.

Requirements covered: 1.2, 10.1, 10.3
"""

# Core ADK models
from .adk_models import (
    # Enums
    MessageRole,
    
    # Core ADK types
    ADKContentPart,
    ADKEventContent,
    ADKEvent,
    ADKMessage,
    ADKRunAgentRequest,
    ADKAgentInfo,
    ADKSessionInfo,
    ADKSessionCreateRequest,
    ADKSessionCreateResponse,
    ADKErrorResponse,
    ADKHealthCheck,
    
    # Response transformation
    StreamingChunk,
    ChatMessage,
    
    # Validation utilities
    ADKRequestValidator,
)

# Enhanced chat models
from .chat_models import (
    # Enums
    ChatSessionType,
    
    # Core chat types
    ChatSession,
    EnhancedChatMessage,
    ChatMessageRequest,
    ChatMessageResponse,
    StreamingResponse,
    ChatHistoryRequest,
    ChatHistoryResponse,
    AgentSelectionRequest,
    AgentSelectionResponse,
    ChatError,
    ChatStatus,
    
    # Transformation utilities
    ChatValidators,
)

# Validation schemas
from .validation_schemas import (
    # Validation classes
    ValidationError,
    ValidationSeverity,
    ValidationResult,
    ADKRequestValidator,
    ADKResponseValidator,
    ADKSessionValidator,
    
    # Utility functions
    validate_json_serializable,
    validate_content_safety,
    
    # Decorators
    validate_adk_request,
    validate_adk_response,
)

# Version info
__version__ = "1.0.0"
__author__ = "ADK Integration Team"

# Export all public classes and functions
__all__ = [
    # Enums
    "MessageRole",
    "ChatSessionType",
    "ValidationSeverity",
    
    # Core ADK models
    "ADKContentPart",
    "ADKEventContent", 
    "ADKEvent",
    "ADKMessage",
    "ADKRunAgentRequest",
    "ADKAgentInfo",
    "ADKSessionInfo",
    "ADKSessionCreateRequest",
    "ADKSessionCreateResponse",
    "ADKErrorResponse",
    "ADKHealthCheck",
    
    # Chat models
    "ChatSession",
    "EnhancedChatMessage",
    "ChatMessageRequest",
    "ChatMessageResponse",
    "StreamingResponse",
    "ChatHistoryRequest",
    "ChatHistoryResponse",
    "AgentSelectionRequest",
    "AgentSelectionResponse",
    "ChatError",
    "ChatStatus",
    
    # Response transformation
    "StreamingChunk",
    "ChatMessage",
    
    # Transformation utilities
    "ChatValidators",
    
    # Validation
    "ValidationError",
    "ValidationResult",
    "ADKRequestValidator",
    "ADKResponseValidator", 
    "ADKSessionValidator",
    
    # Utility functions
    "validate_json_serializable",
    "validate_content_safety",
    
    # Decorators
    "validate_adk_request",
    "validate_adk_response",
]