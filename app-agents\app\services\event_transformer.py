"""
ADK Event Transformation Layer

This module provides comprehensive transformation of ADK Event objects to frontend-compatible
StreamingChunk format, handling text extraction, function calls, and tool usage display.

Requirements covered: 10.1, 10.2, 4.5
"""

from typing import Dict, Any, List, Optional
import logging
from datetime import datetime

from app.models.adk_models import ADKEvent, ADKEventContent, ADKContentPart, StreamingChunk, MessageRole
from app.models.chat_models import EnhancedChatMessage

logger = logging.getLogger(__name__)


class ADKEventTransformer:
    """
    Comprehensive transformer for ADK Event objects to frontend-compatible formats
    
    This class handles:
    - Text extraction from Event.content.parts arrays
    - Processing function_call and function_response parts for tool usage display
    - Handling interruptions and completion states
    - Preserving metadata for debugging and display
    """
    
    @staticmethod
    def transform_event_to_chunk(adk_event: ADKEvent) -> StreamingChunk:
        """
        Transform ADK Event object to StreamingChunk format
        
        Args:
            adk_event: ADK Event object from SSE stream
            
        Returns:
            StreamingChunk: Frontend-compatible streaming chunk
            
        Requirements: 10.1, 10.2
        """
        try:
            # Extract text content from Event.content.parts
            content = ADKEventTransformer._extract_text_content(adk_event)
            
            # Process function calls and responses
            function_calls = ADKEventTransformer._extract_function_calls(adk_event)
            function_responses = ADKEventTransformer._extract_function_responses(adk_event)
            
            # Handle interruptions
            if adk_event.interrupted:
                content += " [Interrupted]"
            
            # Build comprehensive metadata
            metadata = ADKEventTransformer._build_metadata(
                adk_event, function_calls, function_responses
            )
            
            # Create streaming chunk
            chunk = StreamingChunk(
                content=content,
                done=adk_event.turn_complete,
                message_id=adk_event.invocation_id or f"chunk_{datetime.now().timestamp()}",
                metadata=metadata
            )
            
            logger.debug(f"Transformed ADK Event to StreamingChunk: {chunk.message_id}")
            return chunk
            
        except Exception as e:
            logger.error(f"Error transforming ADK Event to StreamingChunk: {e}")
            # Return error chunk
            return StreamingChunk(
                content=f"[Error processing response: {str(e)}]",
                done=True,
                message_id="error",
                metadata={"error": True, "error_message": str(e)}
            )
    
    @staticmethod
    def transform_event_to_message(
        adk_event: ADKEvent,
        session_id: str,
        user_id: str,
        agent_name: Optional[str] = None
    ) -> EnhancedChatMessage:
        """
        Transform ADK Event to EnhancedChatMessage for storage/history
        
        Args:
            adk_event: ADK Event object
            session_id: Chat session identifier
            user_id: User identifier
            agent_name: Optional agent name override
            
        Returns:
            EnhancedChatMessage: Complete chat message object
        """
        try:
            # Extract content
            content = ADKEventTransformer._extract_text_content(adk_event)
            function_calls = ADKEventTransformer._extract_function_calls(adk_event)
            
            # Determine role
            role = MessageRole.MODEL
            if adk_event.content and adk_event.content.role:
                try:
                    role = MessageRole(adk_event.content.role)
                except ValueError:
                    role = MessageRole.MODEL  # Default fallback
            
            # Build metadata
            metadata = ADKEventTransformer._build_metadata(adk_event, function_calls, [])
            
            return EnhancedChatMessage(
                id=adk_event.invocation_id or f"msg_{datetime.now().timestamp()}",
                session_id=session_id,
                role=role,
                content=content,
                user_id=user_id,
                agent_name=agent_name or adk_event.author,
                adk_invocation_id=adk_event.invocation_id,
                function_calls=function_calls,
                interrupted=adk_event.interrupted,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Error transforming ADK Event to ChatMessage: {e}")
            # Return error message
            return EnhancedChatMessage(
                id="error",
                session_id=session_id,
                role=MessageRole.MODEL,
                content=f"[Error processing message: {str(e)}]",
                user_id=user_id,
                metadata={"error": True, "error_message": str(e)}
            )
    
    @staticmethod
    def _extract_text_content(adk_event: ADKEvent) -> str:
        """
        Extract text content from Event.content.parts arrays
        
        Args:
            adk_event: ADK Event object
            
        Returns:
            str: Concatenated text content
            
        Requirements: 10.1
        """
        content = ""
        
        if not adk_event.content or not adk_event.content.parts:
            return content
        
        for part in adk_event.content.parts:
            if part.text:
                content += part.text
        
        return content
    
    @staticmethod
    def _extract_function_calls(adk_event: ADKEvent) -> List[Dict[str, Any]]:
        """
        Extract function_call parts for tool usage display
        
        Args:
            adk_event: ADK Event object
            
        Returns:
            List[Dict[str, Any]]: List of function call objects
            
        Requirements: 10.2, 4.5
        """
        function_calls = []
        
        if not adk_event.content or not adk_event.content.parts:
            return function_calls
        
        for part in adk_event.content.parts:
            if part.function_call:
                # Enhance function call with metadata
                enhanced_call = {
                    **part.function_call,
                    "timestamp": datetime.now().isoformat(),
                    "invocation_id": adk_event.invocation_id,
                    "author": adk_event.author
                }
                function_calls.append(enhanced_call)
        
        return function_calls
    
    @staticmethod
    def _extract_function_responses(adk_event: ADKEvent) -> List[Dict[str, Any]]:
        """
        Extract function_response parts for tool execution results
        
        Args:
            adk_event: ADK Event object
            
        Returns:
            List[Dict[str, Any]]: List of function response objects
            
        Requirements: 10.2, 4.5
        """
        function_responses = []
        
        if not adk_event.content or not adk_event.content.parts:
            return function_responses
        
        for part in adk_event.content.parts:
            if part.function_response:
                # Enhance function response with metadata
                enhanced_response = {
                    **part.function_response,
                    "timestamp": datetime.now().isoformat(),
                    "invocation_id": adk_event.invocation_id,
                    "author": adk_event.author
                }
                function_responses.append(enhanced_response)
        
        return function_responses
    
    @staticmethod
    def _extract_inline_data(adk_event: ADKEvent) -> List[Dict[str, Any]]:
        """
        Extract inline_data parts for binary content (audio, images, etc.)
        
        Args:
            adk_event: ADK Event object
            
        Returns:
            List[Dict[str, Any]]: List of inline data objects
        """
        inline_data = []
        
        if not adk_event.content or not adk_event.content.parts:
            return inline_data
        
        for part in adk_event.content.parts:
            if part.inline_data:
                # Enhance inline data with metadata
                enhanced_data = {
                    **part.inline_data,
                    "timestamp": datetime.now().isoformat(),
                    "invocation_id": adk_event.invocation_id,
                    "author": adk_event.author
                }
                inline_data.append(enhanced_data)
        
        return inline_data
    
    @staticmethod
    def _build_metadata(
        adk_event: ADKEvent,
        function_calls: List[Dict[str, Any]],
        function_responses: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Build comprehensive metadata for the transformed event
        
        Args:
            adk_event: Original ADK Event object
            function_calls: Extracted function calls
            function_responses: Extracted function responses
            
        Returns:
            Dict[str, Any]: Comprehensive metadata object
        """
        metadata = {
            # Core event information
            "author": adk_event.author or "agent",
            "interrupted": adk_event.interrupted,
            "turn_complete": adk_event.turn_complete,
            "invocation_id": adk_event.invocation_id,
            
            # Tool usage information
            "function_calls": function_calls,
            "function_responses": function_responses,
            "has_function_calls": len(function_calls) > 0,
            "has_function_responses": len(function_responses) > 0,
            
            # Long-running tools
            "long_running_tool_ids": adk_event.long_running_tool_ids or [],
            "has_long_running_tools": bool(adk_event.long_running_tool_ids),
            
            # Processing information
            "processed_at": datetime.now().isoformat(),
            "content_parts_count": len(adk_event.content.parts) if adk_event.content else 0,
            
            # Binary data information
            "inline_data": ADKEventTransformer._extract_inline_data(adk_event),
            "has_inline_data": bool(ADKEventTransformer._extract_inline_data(adk_event)),
        }
        
        # Include original event metadata if present
        if adk_event.metadata:
            metadata["original_metadata"] = adk_event.metadata
        
        # Add content role information
        if adk_event.content:
            metadata["content_role"] = adk_event.content.role
        
        return metadata
    
    @staticmethod
    def get_tool_usage_summary(adk_event: ADKEvent) -> Dict[str, Any]:
        """
        Get a summary of tool usage from an ADK Event
        
        Args:
            adk_event: ADK Event object
            
        Returns:
            Dict[str, Any]: Tool usage summary
            
        Requirements: 4.5
        """
        function_calls = ADKEventTransformer._extract_function_calls(adk_event)
        function_responses = ADKEventTransformer._extract_function_responses(adk_event)
        
        # Extract tool names from function calls
        tools_used = set()
        for call in function_calls:
            if "name" in call:
                tools_used.add(call["name"])
        
        # Count successful vs failed responses
        successful_responses = 0
        failed_responses = 0
        
        for response in function_responses:
            if response.get("error"):
                failed_responses += 1
            else:
                successful_responses += 1
        
        return {
            "tools_used": list(tools_used),
            "total_function_calls": len(function_calls),
            "total_function_responses": len(function_responses),
            "successful_responses": successful_responses,
            "failed_responses": failed_responses,
            "long_running_tools": adk_event.long_running_tool_ids or [],
            "has_active_tools": bool(adk_event.long_running_tool_ids),
            "invocation_id": adk_event.invocation_id
        }
    
    @staticmethod
    def is_tool_execution_event(adk_event: ADKEvent) -> bool:
        """
        Check if an ADK Event contains tool execution (function calls/responses)
        
        Args:
            adk_event: ADK Event object
            
        Returns:
            bool: True if event contains tool execution
        """
        if not adk_event.content or not adk_event.content.parts:
            return False
        
        for part in adk_event.content.parts:
            if part.function_call or part.function_response:
                return True
        
        return False
    
    @staticmethod
    def extract_content_types(adk_event: ADKEvent) -> List[str]:
        """
        Extract the types of content present in an ADK Event
        
        Args:
            adk_event: ADK Event object
            
        Returns:
            List[str]: List of content types present
        """
        content_types = []
        
        if not adk_event.content or not adk_event.content.parts:
            return content_types
        
        for part in adk_event.content.parts:
            if part.text:
                content_types.append("text")
            if part.function_call:
                content_types.append("function_call")
            if part.function_response:
                content_types.append("function_response")
            if part.inline_data:
                content_types.append("inline_data")
        
        return list(set(content_types))  # Remove duplicates


class EventTransformationError(Exception):
    """Exception raised during event transformation"""
    pass


class EventValidationError(EventTransformationError):
    """Exception raised when event validation fails"""
    pass


class ContentExtractionError(EventTransformationError):
    """Exception raised when content extraction fails"""
    pass