# Social Media Manager - Backend

FastAPI backend with Google ADK agents for the Social Media Manager platform.

## 🛠️ Tech Stack

- **Framework**: FastAPI with async/await
- **Language**: Python 3.11+
- **Agent Framework**: Google ADK (Agent Development Kit)
- **Database**: Google Firestore (NoSQL)
- **Analytics**: Google BigQuery
- **Authentication**: OAuth 2.0, JWT tokens
- **Background Tasks**: Celery with Redis

## 🚀 Development

### Prerequisites
- Python 3.11+
- pip or pipenv
- Redis (for local development)

### Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env

# Start development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Available Scripts
- `uvicorn main:app --reload` - Start development server
- `pytest` - Run tests
- `black .` - Format code
- `flake8` - Lint code

## 📁 Project Structure

```
app/
├── agents/           # AI agent implementations
│   ├── coordinator.py      # Main orchestrator agent
│   ├── youtube_analyzer.py # YouTube analysis agent
│   ├── instagram_analyzer.py # Instagram analysis agent
│   ├── research_agent.py   # Trend research agent
│   └── content_planner.py  # Content planning agent
├── core/             # Core application logic
│   ├── config.py           # Configuration settings
│   ├── auth.py            # Authentication utilities
│   └── logging.py         # Logging configuration
├── models/           # Pydantic data models
│   └── schemas.py         # API schemas and types
├── routers/          # FastAPI route handlers
│   ├── auth.py            # Authentication endpoints
│   ├── chat.py            # Chat interface endpoints
│   ├── accounts.py        # Account management
│   └── planner.py         # Content planning
├── services/         # Business logic services
│   ├── chat_service.py    # Chat message management
│   ├── account_service.py # Account operations
│   ├── oauth_service.py   # OAuth flow handling
│   └── planner_service.py # Content planning logic
└── utils/            # Utility functions

tests/                # Test suite
├── test_agents/      # Agent unit tests
├── test_routers/     # API endpoint tests
└── test_services/    # Service layer tests
```

## 🤖 Agent Architecture

### Coordinator Agent
- Orchestrates all sub-agent operations
- Handles chat message routing
- Composes unified responses
- Manages streaming output

### Platform Analyzers
- **YouTubeAnalyzer**: Channel analytics, video performance
- **InstagramAnalyzer**: Post engagement, story metrics
- **ResearchAgent**: Trend analysis, competitor research
- **ContentPlannerAgent**: Strategy generation, scheduling

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/connect/{platform}` - Initiate OAuth flow
- `GET /api/auth/callback/{platform}` - Complete OAuth
- `DELETE /api/auth/connect/{platform}` - Disconnect account

### Chat Interface
- `POST /api/chat` - Send message (streaming response)
- `GET /api/chat/history` - Get chat history
- `DELETE /api/chat/history` - Clear history

### Account Management
- `GET /api/accounts` - List connected accounts
- `GET /api/accounts/{platform}` - Get platform details
- `POST /api/accounts/{platform}/sync` - Trigger data sync

### Content Planning
- `POST /api/planner/generate` - Generate content plan
- `GET /api/planner/plans` - List user plans
- `POST /api/planner/plans/{id}/export` - Export plan

## 🔒 Authentication

### OAuth 2.0 Flows
- **YouTube**: Google OAuth with YouTube Data API scopes
- **Instagram**: Facebook OAuth with Instagram Graph API
- **Read-only**: Public channel access via YouTube Data API

### Token Management
- Secure storage in Google Secret Manager
- Automatic token refresh
- Revocation handling

## 📊 Data Models

### Core Entities
```python
# User account connection
class AccountConnection(BaseModel):
    id: str
    platform: Platform
    handle: str
    status: ConnectionStatus
    metrics: PlatformMetrics

# Chat message
class ChatMessage(BaseModel):
    role: MessageRole
    content: str
    timestamp: datetime
    metadata: Optional[Dict]

# Content plan
class ContentPlan(BaseModel):
    timeframe_days: int
    platforms: List[Platform]
    posts: List[PostBrief]
```

## 🌐 External Integrations

### YouTube Data API v3
- Channel statistics and analytics
- Video performance metrics
- Content analysis and optimization

### Instagram Graph API
- Account insights and metrics
- Media performance data
- Audience demographics

### Google Search API
- Trend research and analysis
- Competitor content discovery
- Hashtag and keyword suggestions

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_agents/test_coordinator.py

# Run integration tests
pytest tests/integration/
```

## 🚀 Deployment

### Docker
```bash
docker build -t social-media-backend .
docker run -p 8000:8000 social-media-backend
```

### Google Cloud Run
```bash
# Build and deploy
gcloud run deploy social-media-backend \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## 🔧 Configuration

### Environment Variables
```bash
# Application
DEBUG=false
SECRET_KEY=your-secret-key

# Google Cloud
GOOGLE_CLOUD_PROJECT=your-project-id
ADK_LOCATION=us-central1

# API Keys
YOUTUBE_API_KEY=your-youtube-key
INSTAGRAM_APP_ID=your-instagram-app-id
GOOGLE_SEARCH_API_KEY=your-search-key

# Database
REDIS_HOST=localhost
REDIS_PORT=6379
```

## 📈 Monitoring

### Logging
- Structured JSON logging
- Google Cloud Logging integration
- Request/response tracing

### Health Checks
- `/health` - Basic health check
- `/` - Service information

### Metrics
- Request latency tracking
- Error rate monitoring
- Agent performance metrics

## 🔧 Development Tools

### Code Quality
- **Black** - Code formatting
- **Flake8** - Linting
- **mypy** - Type checking
- **pytest** - Testing framework

### Dependencies
- **FastAPI** - Web framework
- **Pydantic** - Data validation
- **SQLAlchemy** - Database ORM (if needed)
- **Redis** - Caching and tasks

## 📚 Learn More

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Google ADK Documentation](https://cloud.google.com/agent-development-kit)
- [Pydantic Documentation](https://docs.pydantic.dev/)
- [YouTube Data API](https://developers.google.com/youtube/v3)
- [Instagram Graph API](https://developers.facebook.com/docs/instagram-api)