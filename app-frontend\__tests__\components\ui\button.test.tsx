import React from 'react'
import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Button } from '@/components/ui/button'
import { renderWithProviders } from '../../utils/test-utils'

describe('Button Component', () => {
  describe('Basic Functionality', () => {
    it('renders with children text', () => {
      renderWithProviders(<Button>Click me</Button>)
      
      expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
    })

    it('handles click events', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      renderWithProviders(<Button onClick={handleClick}>Click me</Button>)
      
      const button = screen.getByRole('button', { name: 'Click me' })
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('can be disabled', () => {
      renderWithProviders(<Button disabled>Disabled button</Button>)
      
      const button = screen.getByRole('button', { name: 'Disabled button' })
      expect(button).toBeDisabled()
    })

    it('does not call onClick when disabled', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      renderWithProviders(
        <Button disabled onClick={handleClick}>
          Disabled button
        </Button>
      )
      
      const button = screen.getByRole('button', { name: 'Disabled button' })
      await user.click(button)
      
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Variants', () => {
    it('applies default variant styling', () => {
      renderWithProviders(<Button>Default</Button>)
      
      const button = screen.getByRole('button', { name: 'Default' })
      expect(button).toHaveClass('bg-primary', 'text-primary-foreground')
    })

    it('applies secondary variant styling', () => {
      renderWithProviders(<Button variant="secondary">Secondary</Button>)
      
      const button = screen.getByRole('button', { name: 'Secondary' })
      expect(button).toHaveClass('bg-secondary', 'text-secondary-foreground')
    })

    it('applies outline variant styling', () => {
      renderWithProviders(<Button variant="outline">Outline</Button>)
      
      const button = screen.getByRole('button', { name: 'Outline' })
      expect(button).toHaveClass('border', 'bg-background')
    })

    it('applies ghost variant styling', () => {
      renderWithProviders(<Button variant="ghost">Ghost</Button>)
      
      const button = screen.getByRole('button', { name: 'Ghost' })
      expect(button).toHaveClass('hover:bg-accent')
    })

    it('applies destructive variant styling', () => {
      renderWithProviders(<Button variant="destructive">Destructive</Button>)
      
      const button = screen.getByRole('button', { name: 'Destructive' })
      expect(button).toHaveClass('bg-destructive', 'text-destructive-foreground')
    })
  })

  describe('Sizes', () => {
    it('applies default size styling', () => {
      renderWithProviders(<Button>Default size</Button>)
      
      const button = screen.getByRole('button', { name: 'Default size' })
      expect(button).toHaveClass('h-10', 'px-4', 'py-2')
    })

    it('applies small size styling', () => {
      renderWithProviders(<Button size="sm">Small</Button>)
      
      const button = screen.getByRole('button', { name: 'Small' })
      expect(button).toHaveClass('h-9', 'px-3')
    })

    it('applies large size styling', () => {
      renderWithProviders(<Button size="lg">Large</Button>)
      
      const button = screen.getByRole('button', { name: 'Large' })
      expect(button).toHaveClass('h-11', 'px-8')
    })

    it('applies icon size styling', () => {
      renderWithProviders(<Button size="icon">🔄</Button>)
      
      const button = screen.getByRole('button', { name: '🔄' })
      expect(button).toHaveClass('h-10', 'w-10')
    })
  })

  describe('Custom Props', () => {
    it('forwards custom className', () => {
      renderWithProviders(<Button className="custom-class">Custom</Button>)
      
      const button = screen.getByRole('button', { name: 'Custom' })
      expect(button).toHaveClass('custom-class')
    })

    it('forwards HTML button attributes', () => {
      renderWithProviders(
        <Button type="submit" form="test-form">
          Submit
        </Button>
      )
      
      const button = screen.getByRole('button', { name: 'Submit' })
      expect(button).toHaveAttribute('type', 'submit')
      expect(button).toHaveAttribute('form', 'test-form')
    })

    it('supports asChild prop', () => {
      renderWithProviders(
        <Button asChild>
          <a href="/test">Link Button</a>
        </Button>
      )
      
      const link = screen.getByRole('link', { name: 'Link Button' })
      expect(link).toHaveAttribute('href', '/test')
      expect(link).toHaveClass('inline-flex', 'items-center')
    })
  })

  describe('Accessibility', () => {
    it('has proper button role', () => {
      renderWithProviders(<Button>Accessible</Button>)
      
      expect(screen.getByRole('button', { name: 'Accessible' })).toBeInTheDocument()
    })

    it('supports aria-label', () => {
      renderWithProviders(<Button aria-label="Close dialog">×</Button>)
      
      const button = screen.getByRole('button', { name: 'Close dialog' })
      expect(button).toHaveAttribute('aria-label', 'Close dialog')
    })

    it('supports aria-describedby', () => {
      renderWithProviders(
        <div>
          <Button aria-describedby="help-text">Submit</Button>
          <div id="help-text">This submits the form</div>
        </div>
      )
      
      const button = screen.getByRole('button', { name: 'Submit' })
      expect(button).toHaveAttribute('aria-describedby', 'help-text')
    })

    it('is keyboard accessible', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      renderWithProviders(<Button onClick={handleClick}>Keyboard</Button>)
      
      const button = screen.getByRole('button', { name: 'Keyboard' })
      button.focus()
      await user.keyboard('{Enter}')
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('supports space key activation', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      renderWithProviders(<Button onClick={handleClick}>Space</Button>)
      
      const button = screen.getByRole('button', { name: 'Space' })
      button.focus()
      await user.keyboard(' ')
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })

  describe('Loading State', () => {
    it('shows loading state when specified', () => {
      renderWithProviders(
        <Button disabled>
          <span className="animate-spin">🔄</span>
          Loading...
        </Button>
      )
      
      const button = screen.getByRole('button', { name: /loading/i })
      expect(button).toBeDisabled()
      expect(screen.getByText('🔄')).toHaveClass('animate-spin')
    })
  })

  describe('Icon Integration', () => {
    it('renders with leading icon', () => {
      renderWithProviders(
        <Button>
          <span>📄</span>
          Download
        </Button>
      )
      
      expect(screen.getByRole('button', { name: /download/i })).toBeInTheDocument()
      expect(screen.getByText('📄')).toBeInTheDocument()
    })

    it('renders with trailing icon', () => {
      renderWithProviders(
        <Button>
          Upload
          <span>⬆️</span>
        </Button>
      )
      
      expect(screen.getByRole('button', { name: /upload/i })).toBeInTheDocument()
      expect(screen.getByText('⬆️')).toBeInTheDocument()
    })

    it('renders icon-only button', () => {
      renderWithProviders(
        <Button size="icon" aria-label="Settings">
          ⚙️
        </Button>
      )
      
      const button = screen.getByRole('button', { name: 'Settings' })
      expect(button).toHaveClass('w-10', 'h-10')
    })
  })

  describe('Focus Management', () => {
    it('can receive focus', () => {
      renderWithProviders(<Button>Focusable</Button>)
      
      const button = screen.getByRole('button', { name: 'Focusable' })
      button.focus()
      
      expect(button).toHaveFocus()
    })

    it('shows focus ring when focused', () => {
      renderWithProviders(<Button>Focus ring</Button>)
      
      const button = screen.getByRole('button', { name: 'Focus ring' })
      expect(button).toHaveClass('focus-visible:ring-2')
    })

    it('cannot receive focus when disabled', () => {
      renderWithProviders(<Button disabled>Not focusable</Button>)
      
      const button = screen.getByRole('button', { name: 'Not focusable' })
      button.focus()
      
      expect(button).not.toHaveFocus()
    })
  })

  describe('Event Handling', () => {
    it('handles multiple event types', async () => {
      const handleClick = jest.fn()
      const handleMouseEnter = jest.fn()
      const handleMouseLeave = jest.fn()
      const user = userEvent.setup()
      
      renderWithProviders(
        <Button
          onClick={handleClick}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          Multiple events
        </Button>
      )
      
      const button = screen.getByRole('button', { name: 'Multiple events' })
      
      await user.hover(button)
      expect(handleMouseEnter).toHaveBeenCalledTimes(1)
      
      await user.click(button)
      expect(handleClick).toHaveBeenCalledTimes(1)
      
      await user.unhover(button)
      expect(handleMouseLeave).toHaveBeenCalledTimes(1)
    })

    it('prevents event propagation when specified', async () => {
      const parentClick = jest.fn()
      const buttonClick = jest.fn((e) => e.stopPropagation())
      const user = userEvent.setup()
      
      renderWithProviders(
        <div onClick={parentClick}>
          <Button onClick={buttonClick}>Stop propagation</Button>
        </div>
      )
      
      const button = screen.getByRole('button', { name: 'Stop propagation' })
      await user.click(button)
      
      expect(buttonClick).toHaveBeenCalledTimes(1)
      expect(parentClick).not.toHaveBeenCalled()
    })
  })
})