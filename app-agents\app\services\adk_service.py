"""
ADK Service Foundation

This module provides the core ADK service for communicating with ADK agents through
the ADK FastAPI server. It handles agent discovery, session management, and provides
async HTTP client functionality for ADK server communication.

Requirements covered: 1.1, 1.5, 4.1
"""

import asyncio
import json
import logging
from typing import List, Optional, Dict, Any, AsyncGenerator
from datetime import datetime, timedelta
import httpx
from contextlib import asynccontextmanager

from app.models.adk_models import (
    ADKAgentInfo,
    ADKSessionInfo,
    ADKSessionCreateRequest,
    ADKSessionCreateResponse,
    ADKHealthCheck,
    ADKErrorResponse,
    ADKRequestValidator,
    ADKRunAgentRequest,
    StreamingChunk
)
from app.services.event_transformer import ADKEventTransformer
from app.core.error_handling import (
    ADKIntegrationError,
    ADKConnectionError,
    ADKServerUnavailableError,
    ADKAgentNotFoundError,
    ADKSessionError,
    ADKStreamingError,
    ADKTimeoutError,
    ADKValidation<PERSON>rror,
    <PERSON>rror<PERSON><PERSON><PERSON>
)


logger = logging.getLogger(__name__)
error_handler = ErrorHandler(logger)


class ADKService:
    """
    Core service for ADK server communication and agent management.
    
    Provides async HTTP client functionality, agent discovery, session management,
    streaming communication, and error handling for ADK integration.
    """
    
    def __init__(
        self,
        base_url: str = "http://localhost:8001",
        agents_dir: str = "agents",
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        """
        Initialize ADK service.
        
        Args:
            base_url: Base URL for ADK server
            agents_dir: Directory containing ADK agents
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.agents_dir = agents_dir
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # HTTP client configuration
        self._client: Optional[httpx.AsyncClient] = None
        self._client_config = {
            "timeout": httpx.Timeout(timeout),
            "limits": httpx.Limits(max_connections=20, max_keepalive_connections=5),
            "headers": {
                "User-Agent": "ADK-Frontend-Integration/1.0",
                "Accept": "application/json"
            }
        }
        
        # Cache for agent discovery and health checks
        self._agents_cache: Optional[List[ADKAgentInfo]] = None
        self._agents_cache_expiry: Optional[datetime] = None
        self._cache_ttl = timedelta(minutes=5)
        
        # Connection state
        self._is_connected = False
        self._last_health_check: Optional[datetime] = None
        self._health_check_interval = timedelta(minutes=1)
        
        # Event transformer for streaming
        self._event_transformer = ADKEventTransformer()
    
    @property
    def client(self) -> httpx.AsyncClient:
        """Get or create HTTP client"""
        if self._client is None:
            self._client = httpx.AsyncClient(**self._client_config)
        return self._client
    
    @client.setter
    def client(self, value: httpx.AsyncClient) -> None:
        """Set HTTP client (for testing)"""
        self._client = value
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self) -> None:
        """Initialize the ADK service and verify connection"""
        try:
            logger.info(f"Initializing ADK service with base URL: {self.base_url}")
            
            # Verify ADK server is available
            await self._check_server_health()
            self._is_connected = True
            
            logger.info("ADK service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize ADK service: {e}")
            self._is_connected = False
            raise ADKServerUnavailableError(
                message=f"Failed to connect to ADK server: {e}",
                details={"base_url": self.base_url, "agents_dir": self.agents_dir},
                original_error=e
            )
    
    async def close(self) -> None:
        """Close the ADK service and cleanup resources"""
        if self._client:
            await self._client.aclose()
            self._client = None
        
        self._is_connected = False
        self._agents_cache = None
        self._agents_cache_expiry = None
        
        logger.info("ADK service closed")
    
    async def is_healthy(self) -> bool:
        """
        Check if ADK server is healthy and responsive.
        
        Returns:
            True if server is healthy, False otherwise
        """
        try:
            # Use cached health check if recent
            now = datetime.now()
            if (self._last_health_check and 
                now - self._last_health_check < self._health_check_interval):
                return self._is_connected
            
            await self._check_server_health()
            self._last_health_check = now
            self._is_connected = True
            return True
            
        except Exception as e:
            logger.warning(f"ADK health check failed: {e}")
            self._is_connected = False
            return False
    
    async def list_agents(self, force_refresh: bool = False) -> List[ADKAgentInfo]:
        """
        Discover available ADK agents using /list-apps endpoint.
        
        Args:
            force_refresh: Force refresh of agent cache
            
        Returns:
            List of available ADK agents
            
        Raises:
            ADKServerUnavailableError: If ADK server is not available
            ADKConnectionError: If connection fails
        """
        try:
            # Check cache first
            now = datetime.now()
            if (not force_refresh and 
                self._agents_cache and 
                self._agents_cache_expiry and 
                now < self._agents_cache_expiry):
                logger.debug("Returning cached agent list")
                return self._agents_cache
            
            logger.info("Discovering ADK agents via /list-apps endpoint")
            
            # Make request with retry logic
            response = await self._make_request("GET", "/list-apps")
            
            if response.status_code != 200:
                raise ADKServerUnavailableError(
                    message=f"Failed to list agents: HTTP {response.status_code}",
                    details={"status_code": response.status_code, "endpoint": "/list-apps"},
                    original_error=None
                )
            
            # Parse response
            apps_data = response.json()
            agents = []
            
            # Handle different response formats
            if isinstance(apps_data, list):
                # Simple list of app names
                for app_name in apps_data:
                    agents.append(ADKAgentInfo(
                        name=app_name,
                        description=f"ADK agent: {app_name}",
                        available=True
                    ))
            elif isinstance(apps_data, dict):
                # Detailed app information
                for app_name, app_info in apps_data.items():
                    if isinstance(app_info, dict):
                        agents.append(ADKAgentInfo(
                            name=app_name,
                            description=app_info.get("description", f"ADK agent: {app_name}"),
                            available=app_info.get("available", True),
                            capabilities=app_info.get("capabilities", []),
                            tools=app_info.get("tools", [])
                        ))
                    else:
                        agents.append(ADKAgentInfo(
                            name=app_name,
                            description=f"ADK agent: {app_name}",
                            available=True
                        ))
            
            # Update cache
            self._agents_cache = agents
            self._agents_cache_expiry = now + self._cache_ttl
            
            logger.info(f"Discovered {len(agents)} ADK agents")
            return agents
            
        except httpx.RequestError as e:
            logger.error(f"Network error during agent discovery: {e}")
            raise ADKServerUnavailableError(
                message="Failed to connect to ADK server during agent discovery",
                details={"endpoint": "/list-apps", "error_type": "network"},
                original_error=e
            )
        except Exception as e:
            logger.error(f"Error discovering agents: {e}")
            raise ADKServerUnavailableError(
                message="Agent discovery failed",
                details={"endpoint": "/list-apps"},
                original_error=e
            )
    
    async def get_agent_info(self, agent_name: str) -> Optional[ADKAgentInfo]:
        """
        Get information about a specific agent.
        
        Args:
            agent_name: Name of the agent
            
        Returns:
            Agent information if found, None otherwise
        """
        if not ADKRequestValidator.validate_app_name(agent_name):
            raise ADKValidationError(
                message=f"Invalid agent name: {agent_name}",
                validation_errors=[f"Agent name '{agent_name}' does not meet validation requirements"]
            )
        
        agents = await self.list_agents()
        for agent in agents:
            if agent.name == agent_name:
                return agent
        
        return None
    
    async def create_session(
        self, 
        app_name: str, 
        user_id: str,
        initial_state: Optional[Dict[str, Any]] = None
    ) -> ADKSessionCreateResponse:
        """
        Create a new ADK session.
        
        Args:
            app_name: Name of the ADK agent
            user_id: User identifier
            initial_state: Optional initial session state
            
        Returns:
            Session creation response
            
        Raises:
            ADKAgentNotFoundError: If agent is not found
            ADKConnectionError: If connection fails
        """
        # Validate inputs
        if not ADKRequestValidator.validate_app_name(app_name):
            raise ADKValidationError(
                message=f"Invalid app name: {app_name}",
                validation_errors=[f"App name '{app_name}' does not meet validation requirements"]
            )
        if not ADKRequestValidator.validate_user_id(user_id):
            raise ADKValidationError(
                message=f"Invalid user ID: {user_id}",
                validation_errors=[f"User ID '{user_id}' does not meet validation requirements"]
            )
        
        try:
            logger.info(f"Creating ADK session for user {user_id} with agent {app_name}")
            
            # Verify agent exists
            agent_info = await self.get_agent_info(app_name)
            if not agent_info:
                available_agents = [agent.name for agent in await self.list_agents()]
                raise ADKAgentNotFoundError(
                    agent_name=app_name,
                    available_agents=available_agents
                )
            
            # Create session request
            endpoint = f"/apps/{app_name}/users/{user_id}/sessions"
            request_data = ADKSessionCreateRequest(
                user_id=user_id,
                app_name=app_name,
                initial_state=initial_state
            )
            
            response = await self._make_request(
                "POST", 
                endpoint, 
                json=request_data.model_dump(exclude_none=True)
            )
            
            if response.status_code != 200:
                error_data = response.json() if response.content else {}
                raise ADKSessionError(
                    message=f"Failed to create session: HTTP {response.status_code}",
                    user_id=user_id,
                    original_error=None
                )
            
            session_data = response.json()
            session_response = ADKSessionCreateResponse(
                id=session_data["id"],
                app_name=app_name,
                user_id=user_id,
                created_at=datetime.fromisoformat(
                    session_data.get("created_at", datetime.now().isoformat())
                )
            )
            
            logger.info(f"Created ADK session: {session_response.id}")
            return session_response
            
        except ADKAgentNotFoundError:
            raise
        except ADKValidationError:
            raise
        except httpx.RequestError as e:
            logger.error(f"Network error creating session: {e}")
            raise ADKServerUnavailableError(
                message="Failed to connect to ADK server during session creation",
                details={"user_id": user_id, "app_name": app_name},
                original_error=e
            )
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise ADKSessionError(
                message="Session creation failed",
                user_id=user_id,
                original_error=e
            )
    
    async def get_session(
        self, 
        app_name: str, 
        user_id: str, 
        session_id: str
    ) -> Optional[ADKSessionInfo]:
        """
        Retrieve ADK session information.
        
        Args:
            app_name: Name of the ADK agent
            user_id: User identifier
            session_id: Session identifier
            
        Returns:
            Session information if found, None otherwise
        """
        # Validate inputs
        if not ADKRequestValidator.validate_app_name(app_name):
            raise ValueError(f"Invalid app name: {app_name}")
        if not ADKRequestValidator.validate_user_id(user_id):
            raise ValueError(f"Invalid user ID: {user_id}")
        if not ADKRequestValidator.validate_session_id(session_id):
            raise ValueError(f"Invalid session ID: {session_id}")
        
        try:
            endpoint = f"/apps/{app_name}/users/{user_id}/sessions/{session_id}"
            response = await self._make_request("GET", endpoint)
            
            if response.status_code == 404:
                return None
            elif response.status_code != 200:
                logger.warning(f"Failed to get session: HTTP {response.status_code}")
                return None
            
            session_data = response.json()
            return ADKSessionInfo(
                id=session_data["id"],
                app_name=app_name,
                user_id=user_id,
                created_at=datetime.fromisoformat(
                    session_data.get("created_at", datetime.now().isoformat())
                ),
                last_activity=datetime.fromisoformat(
                    session_data.get("last_activity", datetime.now().isoformat())
                ),
                event_count=session_data.get("event_count", 0),
                metadata=session_data.get("metadata", {})
            )
            
        except httpx.RequestError as e:
            logger.error(f"Network error getting session: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting session: {e}")
            return None
    
    async def stream_message(
        self, 
        request: ADKRunAgentRequest
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Stream message response using ADK /run_sse endpoint.
        
        This method sends a RunAgentRequest to the ADK server and streams back
        Event objects as StreamingChunk objects for frontend consumption.
        
        Args:
            request: ADK run agent request
            
        Yields:
            StreamingChunk objects containing response data
            
        Raises:
            ADKServiceError: If streaming fails
            ADKAgentNotFoundError: If agent is not found
        """
        # Validate request
        if not ADKRequestValidator.validate_app_name(request.app_name):
            raise ValueError(f"Invalid app name: {request.app_name}")
        if not ADKRequestValidator.validate_user_id(request.user_id):
            raise ValueError(f"Invalid user ID: {request.user_id}")
        if not ADKRequestValidator.validate_session_id(request.session_id):
            raise ValueError(f"Invalid session ID: {request.session_id}")
        
        try:
            logger.info(
                f"Starting ADK streaming for user {request.user_id}, "
                f"agent {request.app_name}, session {request.session_id}"
            )
            
            # Prepare request data
            request_data = request.model_dump(exclude_none=True)
            
            # Set up streaming headers
            headers = {
                "Accept": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
            
            # Create streaming client with longer timeout for SSE
            streaming_timeout = httpx.Timeout(
                connect=10.0,
                read=300.0,  # 5 minutes for long-running streams
                write=10.0,
                pool=10.0
            )
            
            async with httpx.AsyncClient(
                timeout=streaming_timeout,
                headers=headers
            ) as streaming_client:
                
                url = f"{self.base_url}/run_sse"
                logger.debug(f"Starting SSE stream to {url}")
                
                async with streaming_client.stream(
                    "POST",
                    url,
                    json=request_data,
                    headers=headers
                ) as response:
                    
                    if response.status_code != 200:
                        error_text = await response.aread()
                        logger.error(f"ADK streaming failed: HTTP {response.status_code} - {error_text}")
                        
                        if response.status_code == 404:
                            raise ADKAgentNotFoundError(f"Agent not found: {request.app_name}")
                        else:
                            raise ADKServiceError(
                                f"ADK streaming failed: HTTP {response.status_code}"
                            )
                    
                    logger.debug("ADK SSE stream established successfully")
                    
                    # Process SSE stream with comprehensive Event object handling
                    line_count = 0
                    event_count = 0
                    
                    async for line in response.aiter_lines():
                        line_count += 1
                        
                        try:
                            # Parse SSE line according to SSE specification
                            if line.startswith("data: "):
                                event_data = line[6:]  # Remove "data: " prefix
                                
                                # Skip empty data lines (SSE heartbeat)
                                if not event_data.strip():
                                    logger.debug("Received SSE heartbeat (empty data line)")
                                    continue
                                
                                # Parse JSON event data
                                try:
                                    adk_event_dict = json.loads(event_data)
                                    event_count += 1
                                    
                                    logger.debug(
                                        f"Parsed ADK Event {event_count} from SSE line {line_count}: "
                                        f"keys={list(adk_event_dict.keys())}"
                                    )
                                    
                                except json.JSONDecodeError as e:
                                    logger.warning(
                                        f"Failed to parse ADK event JSON on line {line_count}: {e}\n"
                                        f"Raw data: {event_data[:200]}..."
                                    )
                                    continue
                                
                                # Create ADKEvent object with validation
                                try:
                                    adk_event = ADKEvent(**adk_event_dict)
                                except Exception as e:
                                    logger.warning(
                                        f"Failed to create ADKEvent object: {e}\n"
                                        f"Event data: {adk_event_dict}"
                                    )
                                    # Create minimal valid event for error handling
                                    adk_event = ADKEvent(
                                        content=None,
                                        interrupted=False,
                                        turn_complete=True,
                                        metadata={"parse_error": str(e)}
                                    )
                                
                                # Transform ADK Event to StreamingChunk
                                chunk = self._event_transformer.transform_event_to_chunk(adk_event)
                                
                                # Enhanced logging for debugging
                                logger.debug(
                                    f"Transformed ADK Event {event_count} to StreamingChunk: "
                                    f"message_id={chunk.message_id}, "
                                    f"content_length={len(chunk.content)}, "
                                    f"done={chunk.done}, "
                                    f"turn_complete={chunk.metadata.get('turn_complete', False)}, "
                                    f"interrupted={chunk.metadata.get('interrupted', False)}, "
                                    f"has_function_calls={chunk.metadata.get('has_function_calls', False)}, "
                                    f"author={chunk.metadata.get('author', 'unknown')}"
                                )
                                
                                # Add processing metadata
                                chunk.metadata.update({
                                    "sse_line_number": line_count,
                                    "event_number": event_count,
                                    "processing_timestamp": datetime.now().isoformat()
                                })
                                
                                yield chunk
                                
                                # Check for turn completion
                                if chunk.metadata.get("turn_complete", False):
                                    logger.info(
                                        f"ADK turn completed: processed {event_count} events "
                                        f"from {line_count} SSE lines"
                                    )
                                    break
                                
                                # Check for interruption
                                if chunk.metadata.get("interrupted", False):
                                    logger.warning(
                                        f"ADK stream interrupted: processed {event_count} events "
                                        f"from {line_count} SSE lines"
                                    )
                                    break
                                
                                # Check done flag as fallback
                                if chunk.done:
                                    logger.info(
                                        f"ADK streaming marked as done: processed {event_count} events "
                                        f"from {line_count} SSE lines"
                                    )
                                    break
                                    
                            elif line.startswith("event: "):
                                # Handle SSE event types (optional ADK extension)
                                event_type = line[7:]  # Remove "event: " prefix
                                logger.debug(f"Received SSE event type: {event_type}")
                                
                                # Handle specific event types if needed
                                if event_type == "error":
                                    logger.warning("Received SSE error event type")
                                elif event_type == "complete":
                                    logger.info("Received SSE complete event type")
                                    break
                                    
                            elif line.startswith("id: "):
                                # Handle SSE event ID (for reconnection)
                                event_id = line[4:]  # Remove "id: " prefix
                                logger.debug(f"Received SSE event ID: {event_id}")
                                
                            elif line.startswith("retry: "):
                                # Handle SSE retry directive
                                retry_ms = line[7:]  # Remove "retry: " prefix
                                logger.debug(f"Received SSE retry directive: {retry_ms}ms")
                                
                            elif line.strip() == "":
                                # Empty line indicates end of SSE event
                                logger.debug("Received SSE event separator (empty line)")
                                continue
                                
                            else:
                                # Unknown SSE line format
                                logger.debug(f"Unknown SSE line format: {line[:50]}...")
                                
                        except Exception as e:
                            logger.error(
                                f"Error processing SSE line {line_count}: {e}\n"
                                f"Line content: {line[:200]}..."
                            )
                            # Continue processing other lines instead of failing
                            continue
                    
                    logger.info("ADK SSE stream completed")
                    
        except httpx.RequestError as e:
            logger.error(f"Network error during ADK streaming: {e}")
            raise ADKServiceError(f"Network error during streaming: {e}")
        except ADKAgentNotFoundError:
            raise
        except ADKServiceError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error during ADK streaming: {e}")
            raise ADKServiceError(f"Streaming failed: {e}")
    
    async def get_session_events(
        self,
        app_name: str,
        user_id: str,
        session_id: str
    ) -> List[Any]:
        """
        Get events for a specific ADK session.
        
        Args:
            app_name: Name of the ADK agent
            user_id: User identifier
            session_id: Session identifier
            
        Returns:
            List of ADK events for the session
            
        Raises:
            ADKConnectionError: If connection to ADK server fails
            ADKAgentNotFoundError: If agent is not found
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            # Get session data which includes events
            session_info = await self.get_session(app_name, user_id, session_id)
            if not session_info:
                return []
            
            # For now, return empty list as ADK doesn't expose events directly
            # In a real implementation, this would call an ADK endpoint to get events
            # or parse them from the session data
            return []
            
        except ADKConnectionError:
            raise
        except ADKAgentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error getting session events: {e}")
            raise ADKConnectionError(f"Failed to get session events: {e}")

    async def health_check(self) -> bool:
        """
        Perform health check on ADK server.
        
        Returns:
            True if server is healthy, False otherwise
        """
        return await self.is_healthy()
    
    async def _check_server_health(self) -> ADKHealthCheck:
        """
        Check ADK server health status.
        
        Returns:
            Health check response
            
        Raises:
            ADKServerUnavailableError: If server is not healthy
        """
        try:
            # Try health endpoint first
            try:
                response = await self._make_request("GET", "/health", timeout=5.0)
                if response.status_code == 200:
                    health_data = response.json()
                    return ADKHealthCheck(**health_data)
            except:
                pass  # Fall back to list-apps endpoint
            
            # Fall back to list-apps endpoint for basic connectivity
            response = await self._make_request("GET", "/list-apps", timeout=5.0)
            if response.status_code == 200:
                apps_data = response.json()
                if isinstance(apps_data, list):
                    agent_count = len(apps_data)
                    agent_names = list(apps_data)
                elif isinstance(apps_data, dict):
                    agent_count = len(apps_data.keys())
                    agent_names = list(apps_data.keys())
                else:
                    agent_count = 0
                    agent_names = []
                
                return ADKHealthCheck(
                    status="healthy",
                    agents_loaded=agent_count,
                    available_agents=agent_names
                )
            
            raise ADKServerUnavailableError(f"Health check failed: HTTP {response.status_code}")
            
        except httpx.RequestError as e:
            raise ADKServerUnavailableError(f"Cannot connect to ADK server: {e}")
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        timeout: Optional[float] = None,
        **kwargs
    ) -> httpx.Response:
        """
        Make HTTP request to ADK server with retry logic.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            timeout: Request timeout override
            **kwargs: Additional request parameters
            
        Returns:
            HTTP response
            
        Raises:
            httpx.RequestError: If all retry attempts fail
        """
        url = f"{self.base_url}{endpoint}"
        request_timeout = timeout or self.timeout
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = await self.client.request(
                    method=method,
                    url=url,
                    timeout=request_timeout,
                    **kwargs
                )
                
                logger.debug(f"Request completed: {method} {url} -> {response.status_code}")
                return response
                
            except httpx.RequestError as e:
                last_exception = e
                logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    break
        
        logger.error(f"All retry attempts failed for {method} {url}")
        raise last_exception


# Global ADK service instance
_adk_service: Optional[ADKService] = None


async def get_adk_service() -> ADKService:
    """
    Get or create global ADK service instance.
    
    Returns:
        ADK service instance
    """
    global _adk_service
    
    if _adk_service is None:
        _adk_service = ADKService()
        await _adk_service.initialize()
    
    return _adk_service


@asynccontextmanager
async def adk_service_context():
    """
    Context manager for ADK service lifecycle.
    
    Usage:
        async with adk_service_context() as adk:
            agents = await adk.list_agents()
    """
    service = ADKService()
    try:
        await service.initialize()
        yield service
    finally:
        await service.close()