# Firestore Environment Configuration

This directory contains environment-specific configurations for Firestore deployment.

## Environment Setup

### Development Environment
- **Project ID**: `social-media-dev-XXXX`
- **Database**: `(default)`
- **Security**: Relaxed rules for testing
- **Emulator**: Local development with Firebase emulators

### Staging Environment  
- **Project ID**: `social-media-staging-XXXX`
- **Database**: `(default)`
- **Security**: Production-like rules
- **Data**: Sample data for testing

### Production Environment
- **Project ID**: `social-media-prod-XXXX`
- **Database**: `(default)`
- **Security**: Strict production rules
- **Backup**: Automated daily backups

## Configuration Files

### firebase.json
Main Firebase configuration file with:
- Firestore rules and indexes paths
- Hosting configuration for frontend
- Emulator settings for development
- Cloud Functions configuration

### firestore.rules
Security rules defining:
- Authentication requirements
- Data access permissions
- Field validation rules
- Cross-collection security

### firestore.indexes.json
Performance indexes for:
- User queries by email, subscription
- Account queries by user and platform
- Content plan queries by user and status
- Chat message queries by user and timestamp
- Analytics queries by account and platform

## Deployment Commands

### Development Setup
```bash
# Configure development project
firebase use dev
firebase deploy --only firestore

# Start local emulators
firebase emulators:start
```

### Staging Deployment
```bash
# Configure staging project
firebase use staging
firebase deploy --only firestore

# Deploy with sample data
python scripts/seed_database.py --env staging
```

### Production Deployment
```bash
# Configure production project
firebase use production

# Deploy rules and indexes only
firebase deploy --only firestore:rules,firestore:indexes

# DO NOT seed production with sample data
```

## Security Considerations

### Development
- ✅ Allow emulator access without authentication
- ✅ Relaxed validation for testing
- ✅ Sample data with mock credentials

### Staging
- ✅ Require authentication for all operations
- ✅ Production-like security rules
- ✅ Test data isolation from production

### Production
- ✅ Strict authentication and authorization
- ✅ Input validation and sanitization
- ✅ Rate limiting and abuse prevention
- ✅ Audit logging for sensitive operations

## Database Maintenance

### Backup Strategy
```bash
# Export Firestore data
gcloud firestore export gs://backup-bucket/firestore-backup

# Import Firestore data
gcloud firestore import gs://backup-bucket/firestore-backup
```

### Index Management
```bash
# List current indexes
firebase firestore:indexes

# Deploy new indexes
firebase deploy --only firestore:indexes
```

### Data Migration
```bash
# Run migration scripts
python scripts/migrate_data.py --env production --version 1.1.0
```

## Monitoring and Analytics

### Performance Metrics
- Query performance and latency
- Read/write operation counts
- Index efficiency and usage
- Storage utilization trends

### Security Monitoring
- Failed authentication attempts
- Unauthorized access attempts
- Unusual data access patterns
- Rule violation incidents

### Cost Optimization
- Document read/write optimization
- Index usage analysis
- Storage cleanup procedures
- Query pattern optimization

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Check authentication status
   - Verify security rules
   - Confirm user permissions

2. **Index Missing Errors**
   - Deploy missing indexes
   - Check index configuration
   - Monitor index build status

3. **Performance Issues**
   - Analyze query patterns
   - Optimize compound indexes
   - Review security rule efficiency

### Debug Commands

```bash
# Check project configuration
firebase projects:list
firebase use

# Validate rules syntax
firebase firestore:rules get

# Test rules with emulator
firebase emulators:start --only firestore
```

## Best Practices

### Development
1. Use Firebase emulators for local development
2. Test security rules thoroughly before deployment
3. Validate data models with sample data
4. Monitor query performance early

### Deployment
1. Deploy to staging before production
2. Backup production data before major changes
3. Use gradual rollouts for rule changes
4. Monitor error rates after deployment

### Maintenance
1. Regular index cleanup and optimization
2. Periodic security rule audits
3. Performance monitoring and alerts
4. Cost analysis and optimization