# Cloud Run Deployment Script for Social Media Manager (PowerShell)
# This script automates the complete deployment process to Google Cloud Run

param(
    [string]$ProjectId = "",
    [string]$Region = "us-central1",
    [switch]$SkipBuild,
    [switch]$SkipSecrets,
    [switch]$Help
)

# Configuration
$BackendService = "social-media-backend"
$FrontendService = "social-media-frontend"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-Status "Checking prerequisites..."
    
    # Check if gcloud is installed
    try {
        $null = Get-Command gcloud -ErrorAction Stop
    }
    catch {
        Write-Error "gcloud CLI is not installed. Please install it first."
        exit 1
    }
    
    # Check if docker is installed
    try {
        $null = Get-Command docker -ErrorAction Stop
    }
    catch {
        Write-Error "Docker is not installed. Please install it first."
        exit 1
    }
    
    # Check if project ID is set
    if ([string]::IsNullOrEmpty($script:ProjectId)) {
        $script:ProjectId = gcloud config get-value project
        if ([string]::IsNullOrEmpty($script:ProjectId)) {
            Write-Error "Google Cloud project ID is not set. Please set it with 'gcloud config set project PROJECT_ID'"
            exit 1
        }
    }
    
    Write-Success "Prerequisites check completed. Using project: $script:ProjectId"
}

# Function to enable required APIs
function Enable-APIs {
    Write-Status "Enabling required Google Cloud APIs..."
    
    $apis = @(
        "run.googleapis.com",
        "cloudbuild.googleapis.com",
        "containerregistry.googleapis.com",
        "secretmanager.googleapis.com",
        "firestore.googleapis.com",
        "bigquery.googleapis.com"
    )
    
    foreach ($api in $apis) {
        gcloud services enable $api --project=$script:ProjectId
    }
    
    Write-Success "APIs enabled successfully"
}

# Function to setup IAM and service accounts
function Set-IAM {
    Write-Status "Setting up IAM and service accounts..."
    
    # Create service account for backend if it doesn't exist
    $serviceAccount = "social-media-backend@$($script:ProjectId).iam.gserviceaccount.com"
    
    try {
        gcloud iam service-accounts describe $serviceAccount --project=$script:ProjectId 2>$null
    }
    catch {
        gcloud iam service-accounts create social-media-backend `
            --display-name="Social Media Backend Service" `
            --project=$script:ProjectId
    }
    
    # Grant necessary permissions
    $roles = @(
        "roles/secretmanager.secretAccessor",
        "roles/datastore.user",
        "roles/bigquery.user"
    )
    
    foreach ($role in $roles) {
        gcloud projects add-iam-policy-binding $script:ProjectId `
            --member="serviceAccount:$serviceAccount" `
            --role=$role
    }
    
    Write-Success "IAM setup completed"
}

# Function to create secrets
function New-Secrets {
    Write-Status "Creating secrets in Secret Manager..."
    
    function New-SecretIfNotExists {
        param(
            [string]$SecretName,
            [string]$SecretValue
        )
        
        try {
            gcloud secrets describe $SecretName --project=$script:ProjectId 2>$null
            Write-Warning "Secret $SecretName already exists"
        }
        catch {
            $SecretValue | gcloud secrets create $SecretName --data-file=- --project=$script:ProjectId
            Write-Success "Created secret: $SecretName"
        }
    }
    
    # Prompt for secret values if not set as environment variables
    if ([string]::IsNullOrEmpty($env:SECRET_KEY)) {
        $env:SECRET_KEY = Read-Host "Enter SECRET_KEY for JWT tokens"
    }
    
    if ([string]::IsNullOrEmpty($env:GOOGLE_CLIENT_ID)) {
        $env:GOOGLE_CLIENT_ID = Read-Host "Enter Google OAuth Client ID"
    }
    
    if ([string]::IsNullOrEmpty($env:GOOGLE_CLIENT_SECRET)) {
        $env:GOOGLE_CLIENT_SECRET = Read-Host "Enter Google OAuth Client Secret" -AsSecureString | ConvertFrom-SecureString -AsPlainText
    }
    
    if ([string]::IsNullOrEmpty($env:YOUTUBE_API_KEY)) {
        $env:YOUTUBE_API_KEY = Read-Host "Enter YouTube API Key" -AsSecureString | ConvertFrom-SecureString -AsPlainText
    }
    
    if ([string]::IsNullOrEmpty($env:INSTAGRAM_APP_ID)) {
        $env:INSTAGRAM_APP_ID = Read-Host "Enter Instagram App ID"
    }
    
    if ([string]::IsNullOrEmpty($env:INSTAGRAM_APP_SECRET)) {
        $env:INSTAGRAM_APP_SECRET = Read-Host "Enter Instagram App Secret" -AsSecureString | ConvertFrom-SecureString -AsPlainText
    }
    
    # Create secrets
    New-SecretIfNotExists "app-secret-key" $env:SECRET_KEY
    New-SecretIfNotExists "google-client-id" $env:GOOGLE_CLIENT_ID
    New-SecretIfNotExists "google-client-secret" $env:GOOGLE_CLIENT_SECRET
    New-SecretIfNotExists "youtube-api-key" $env:YOUTUBE_API_KEY
    New-SecretIfNotExists "instagram-app-id" $env:INSTAGRAM_APP_ID
    New-SecretIfNotExists "instagram-app-secret" $env:INSTAGRAM_APP_SECRET
    
    Write-Success "Secrets setup completed"
}

# Function to build and push Docker images
function Build-Images {
    Write-Status "Building and pushing Docker images..."
    
    # Configure Docker for gcloud
    gcloud auth configure-docker --quiet
    
    $BackendImage = "gcr.io/$($script:ProjectId)/$BackendService"
    $FrontendImage = "gcr.io/$($script:ProjectId)/$FrontendService"
    
    # Build backend image
    Write-Status "Building backend image..."
    docker build -t $BackendImage ./app-agents
    
    # Build frontend image
    Write-Status "Building frontend image..."
    docker build -t $FrontendImage ./app-frontend
    
    # Push images
    Write-Status "Pushing backend image..."
    docker push $BackendImage
    
    Write-Status "Pushing frontend image..."
    docker push $FrontendImage
    
    Write-Success "Docker images built and pushed successfully"
    
    return @{
        BackendImage = $BackendImage
        FrontendImage = $FrontendImage
    }
}

# Function to deploy backend service
function Deploy-Backend {
    Write-Status "Deploying backend service to Cloud Run..."
    
    # Replace PROJECT_ID in the configuration file
    $backendConfig = Get-Content "deployment/cloud-run-backend.yaml" -Raw
    $backendConfig = $backendConfig -replace "PROJECT_ID", $script:ProjectId
    $backendConfig | Out-File -FilePath "$env:TEMP/backend-config.yaml" -Encoding UTF8
    
    # Deploy using gcloud with the configuration
    gcloud run services replace "$env:TEMP/backend-config.yaml" `
        --region=$Region `
        --project=$script:ProjectId
    
    # Get the backend URL
    $BackendUrl = gcloud run services describe $BackendService `
        --region=$Region `
        --project=$script:ProjectId `
        --format="value(status.url)"
    
    Write-Success "Backend deployed successfully at: $BackendUrl"
    
    # Clean up temporary file
    Remove-Item "$env:TEMP/backend-config.yaml" -ErrorAction SilentlyContinue
    
    return $BackendUrl
}

# Function to deploy frontend service
function Deploy-Frontend {
    param([string]$BackendUrl)
    
    Write-Status "Deploying frontend service to Cloud Run..."
    
    # Replace PROJECT_ID and BACKEND_URL in the configuration file
    $frontendConfig = Get-Content "deployment/cloud-run-frontend.yaml" -Raw
    $frontendConfig = $frontendConfig -replace "PROJECT_ID", $script:ProjectId
    $frontendConfig = $frontendConfig -replace "https://your-backend-domain.com", $BackendUrl
    $frontendConfig | Out-File -FilePath "$env:TEMP/frontend-config.yaml" -Encoding UTF8
    
    # Deploy using gcloud with the configuration
    gcloud run services replace "$env:TEMP/frontend-config.yaml" `
        --region=$Region `
        --project=$script:ProjectId
    
    # Get the frontend URL
    $FrontendUrl = gcloud run services describe $FrontendService `
        --region=$Region `
        --project=$script:ProjectId `
        --format="value(status.url)"
    
    Write-Success "Frontend deployed successfully at: $FrontendUrl"
    
    # Clean up temporary file
    Remove-Item "$env:TEMP/frontend-config.yaml" -ErrorAction SilentlyContinue
    
    return $FrontendUrl
}

# Function to setup custom domains (optional)
function Set-CustomDomains {
    $setupDomains = Read-Host "Do you want to setup custom domains? (y/N)"
    
    if ($setupDomains -match "^[Yy]$") {
        $BackendDomain = Read-Host "Enter custom domain for backend (e.g., api.yourdomain.com)"
        $FrontendDomain = Read-Host "Enter custom domain for frontend (e.g., app.yourdomain.com)"
        
        if (![string]::IsNullOrEmpty($BackendDomain)) {
            Write-Status "Setting up custom domain for backend: $BackendDomain"
            gcloud run domain-mappings create `
                --service=$BackendService `
                --domain=$BackendDomain `
                --region=$Region `
                --project=$script:ProjectId
        }
        
        if (![string]::IsNullOrEmpty($FrontendDomain)) {
            Write-Status "Setting up custom domain for frontend: $FrontendDomain"
            gcloud run domain-mappings create `
                --service=$FrontendService `
                --domain=$FrontendDomain `
                --region=$Region `
                --project=$script:ProjectId
        }
        
        Write-Success "Custom domains setup completed"
    }
}

# Function to run health checks
function Test-HealthChecks {
    param(
        [string]$BackendUrl,
        [string]$FrontendUrl
    )
    
    Write-Status "Running health checks..."
    
    # Wait for services to be ready
    Start-Sleep -Seconds 30
    
    # Check backend health
    try {
        $response = Invoke-WebRequest -Uri "$BackendUrl/health" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "Backend health check passed"
        } else {
            Write-Error "Backend health check failed with status: $($response.StatusCode)"
        }
    }
    catch {
        Write-Error "Backend health check failed: $($_.Exception.Message)"
    }
    
    # Check frontend health
    try {
        $response = Invoke-WebRequest -Uri "$FrontendUrl/api/health" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "Frontend health check passed"
        } else {
            Write-Error "Frontend health check failed with status: $($response.StatusCode)"
        }
    }
    catch {
        Write-Error "Frontend health check failed: $($_.Exception.Message)"
    }
}

# Function to display deployment summary
function Show-DeploymentSummary {
    param(
        [string]$BackendUrl,
        [string]$FrontendUrl
    )
    
    Write-Success "🎉 Deployment completed successfully!"
    Write-Host ""
    Write-Host "📋 Deployment Summary:" -ForegroundColor Cyan
    Write-Host "  Project ID: $script:ProjectId"
    Write-Host "  Region: $Region"
    Write-Host "  Backend URL: $BackendUrl"
    Write-Host "  Frontend URL: $FrontendUrl"
    Write-Host ""
    Write-Host "🔗 Useful Links:" -ForegroundColor Cyan
    Write-Host "  Cloud Console: https://console.cloud.google.com/run?project=$script:ProjectId"
    Write-Host "  Cloud Logging: https://console.cloud.google.com/logs/query?project=$script:ProjectId"
    Write-Host "  Secret Manager: https://console.cloud.google.com/security/secret-manager?project=$script:ProjectId"
    Write-Host ""
    Write-Host "📋 Next Steps:" -ForegroundColor Cyan
    Write-Host "  1. Update OAuth redirect URIs with the deployed URLs"
    Write-Host "  2. Test the application functionality"
    Write-Host "  3. Setup custom domains if needed"
    Write-Host "  4. Configure monitoring and alerting"
    Write-Host "  5. Setup CI/CD pipeline for automated deployments"
}

# Main deployment function
function Main {
    if ($Help) {
        Write-Host "Usage: .\deploy.ps1 [-ProjectId PROJECT_ID] [-Region REGION] [-SkipBuild] [-SkipSecrets]"
        return
    }
    
    Write-Host "🚀 Social Media Manager - Cloud Run Deployment" -ForegroundColor Magenta
    Write-Host "===============================================" -ForegroundColor Magenta
    
    # Update script variables
    $script:ProjectId = $ProjectId
    
    # Run deployment steps
    Test-Prerequisites
    Enable-APIs
    Set-IAM
    
    if (-not $SkipSecrets) {
        New-Secrets
    }
    
    if (-not $SkipBuild) {
        $images = Build-Images
    }
    
    $BackendUrl = Deploy-Backend
    $FrontendUrl = Deploy-Frontend -BackendUrl $BackendUrl
    Set-CustomDomains
    Test-HealthChecks -BackendUrl $BackendUrl -FrontendUrl $FrontendUrl
    Show-DeploymentSummary -BackendUrl $BackendUrl -FrontendUrl $FrontendUrl
}

# Handle script interruption
trap {
    Write-Error "Deployment interrupted"
    exit 1
}

# Run main function
Main