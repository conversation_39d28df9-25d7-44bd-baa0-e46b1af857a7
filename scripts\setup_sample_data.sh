#!/bin/bash

# Sample Data Generation Script for Social Media Manager
# This script automates the process of generating and seeding sample data

set -e  # Exit on any error

echo "🚀 Social Media Manager - Sample Data Setup"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is available
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed."
        exit 1
    fi
    print_success "Python 3 found"
}

# Check if required Python packages are installed
check_dependencies() {
    print_status "Checking Python dependencies..."
    
    # Check if google-cloud-firestore is available
    if ! python3 -c "import google.cloud.firestore" 2>/dev/null; then
        print_warning "google-cloud-firestore not found. Installing..."
        pip3 install google-cloud-firestore
    fi
    
    print_success "Dependencies verified"
}

# Generate sample data
generate_data() {
    print_status "Generating sample data..."
    cd "$(dirname "$0")"
    
    if python3 generate_sample_data.py; then
        print_success "Sample data generated successfully"
    else
        print_error "Failed to generate sample data"
        exit 1
    fi
}

# Seed database (optional)
seed_database() {
    read -p "Do you want to seed the database with sample data? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Seeding database..."
        
        # Check for Google Cloud credentials
        if [[ -z "${GOOGLE_APPLICATION_CREDENTIALS}" ]] && ! command -v gcloud &> /dev/null; then
            print_warning "Google Cloud credentials not found."
            print_warning "Please set GOOGLE_APPLICATION_CREDENTIALS or run 'gcloud auth application-default login'"
            return
        fi
        
        # Ask if user wants to clear existing data
        read -p "Clear existing data before seeding? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            python3 seed_database.py --clear --env dev
        else
            python3 seed_database.py --env dev
        fi
        
        if [ $? -eq 0 ]; then
            print_success "Database seeded successfully"
        else
            print_error "Failed to seed database"
        fi
    else
        print_status "Skipping database seeding"
    fi
}

# Display next steps
show_next_steps() {
    echo
    echo "🎉 Sample data setup complete!"
    echo
    echo "📋 Next steps:"
    echo "   1. Start your backend server:"
    echo "      cd ../app-agents && uvicorn app.main:app --reload"
    echo
    echo "   2. Start your frontend application:"
    echo "      cd ../app-frontend && npm run dev"
    echo
    echo "   3. Use sample users for testing:"
    echo "      • <EMAIL> (Tech Entrepreneur)"
    echo "      • <EMAIL> (Marketing Strategist)"
    echo "      • <EMAIL> (Fitness Coach)"
    echo
    echo "📁 Generated files:"
    echo "   • sample_data.json (Contains all sample data)"
    echo
    echo "🔗 Useful links:"
    echo "   • Frontend: http://localhost:3000"
    echo "   • Backend API: http://localhost:8000/docs"
    echo "   • Firestore Console: https://console.firebase.google.com/"
}

# Main execution
main() {
    print_status "Starting sample data setup process..."
    
    check_python
    check_dependencies
    generate_data
    seed_database
    show_next_steps
}

# Handle script interruption
trap 'print_error "Script interrupted"; exit 1' INT

# Run main function
main "$@"