import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock data factories
export const mockMessage = (overrides: any = {}) => ({
  id: 'test-message-1',
  role: 'user' as const,
  content: 'Test message content',
  timestamp: new Date('2024-01-01T12:00:00Z'),
  ...overrides,
})

export const mockPlatformData = (platform: string, overrides: any = {}) => ({
  platform,
  connected: true,
  account_info: {
    username: `test_${platform}`,
    followers: 1000,
    following: 500,
  },
  metrics: {
    engagement_rate: 5.2,
    growth_rate: 2.1,
    total_posts: 150,
  },
  ...overrides,
})

export const mockContentPlan = (overrides: any = {}) => ({
  id: 'test-plan-1',
  title: 'Test Content Plan',
  timeframe_start: '2024-01-01T00:00:00Z',
  timeframe_end: '2024-01-07T23:59:59Z',
  platforms: ['youtube', 'instagram'],
  posts: [
    {
      id: 'post-1',
      title: 'Test Post 1',
      platform: 'youtube',
      scheduled_time: '2024-01-02T10:00:00Z',
      content_type: 'video',
      status: 'scheduled',
    },
  ],
  ...overrides,
})

// Test wrapper with providers
interface WrapperProps {
  children: React.ReactNode
}

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
})

export const TestWrapper: React.FC<WrapperProps> = ({ children }) => {
  const testQueryClient = createTestQueryClient()

  return (
    <QueryClientProvider client={testQueryClient}>
      {children}
    </QueryClientProvider>
  )
}

// Custom render function with providers
export const renderWithProviders = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  return render(ui, { wrapper: TestWrapper, ...options })
}

// Mock API responses
export const mockApiResponse = {
  success: (data: any) => ({
    status: 'success',
    data,
    message: 'Operation completed successfully',
  }),
  error: (message: string = 'An error occurred') => ({
    status: 'error',
    message,
    data: null,
  }),
}

// Mock fetch for API calls
export const mockFetch = (response: any, ok: boolean = true) => {
  global.fetch = jest.fn(() =>
    Promise.resolve({
      ok,
      status: ok ? 200 : 400,
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(JSON.stringify(response)),
    })
  ) as jest.Mock
}

// Helper to wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0))

// Re-export everything from React Testing Library
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'