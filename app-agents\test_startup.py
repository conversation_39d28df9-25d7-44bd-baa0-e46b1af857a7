#!/usr/bin/env python3

print("Testing backend dependencies...")

try:
    from fastapi import FastAPI
    print("✓ FastAPI imported successfully")
except ImportError as e:
    print(f"✗ FastAPI import failed: {e}")

try:
    import uvicorn
    print("✓ Uvicorn imported successfully")
except ImportError as e:
    print(f"✗ Uvicorn import failed: {e}")

try:
    from dotenv import load_dotenv
    print("✓ python-dotenv imported successfully")
except ImportError as e:
    print(f"✗ python-dotenv import failed: {e}")

try:
    from app.routers import health
    print("✓ Health router imported successfully")
except ImportError as e:
    print(f"✗ Health router import failed: {e}")

try:
    from app.core.config import get_settings
    print("✓ Config imported successfully")
except ImportError as e:
    print(f"✗ Config import failed: {e}")

try:
    from app.core.logging import setup_logging
    print("✓ Logging setup imported successfully")
except ImportError as e:
    print(f"✗ Logging setup import failed: {e}")

print("\nAll tests completed!")