#!/usr/bin/env python3
"""
Firestore Setup and Deployment Script

This script automates the deployment of Firestore security rules and indexes
for the Social Media Manager application.
"""

import subprocess
import sys
import json
import os
from pathlib import Path

def run_command(command: str, description: str):
    """Run a shell command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        return False

def check_firebase_cli():
    """Check if Firebase CLI is installed"""
    try:
        result = subprocess.run("firebase --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Firebase CLI found: {result.stdout.strip()}")
            return True
        else:
            print("❌ Firebase CLI not found")
            return False
    except:
        print("❌ Firebase CLI not found")
        return False

def check_project_config():
    """Check if Firebase project is configured"""
    firebase_json = Path("firebase.json")
    if not firebase_json.exists():
        print("❌ firebase.json not found")
        return False
    
    try:
        with open(firebase_json, 'r') as f:
            config = json.load(f)
        
        if 'projects' in config and 'default' in config['projects']:
            project_id = config['projects']['default']
            if project_id == "your-project-id":
                print("⚠️  Warning: Please update firebase.json with your actual project ID")
                return False
            else:
                print(f"✅ Firebase project configured: {project_id}")
                return True
        else:
            print("❌ Firebase project not configured in firebase.json")
            return False
    except Exception as e:
        print(f"❌ Error reading firebase.json: {e}")
        return False

def deploy_firestore_rules():
    """Deploy Firestore security rules"""
    rules_file = Path("firestore.rules")
    if not rules_file.exists():
        print("❌ firestore.rules file not found")
        return False
    
    return run_command(
        "firebase deploy --only firestore:rules",
        "Deploying Firestore security rules"
    )

def deploy_firestore_indexes():
    """Deploy Firestore indexes"""
    indexes_file = Path("firestore.indexes.json")
    if not indexes_file.exists():
        print("❌ firestore.indexes.json file not found")
        return False
    
    return run_command(
        "firebase deploy --only firestore:indexes",
        "Deploying Firestore indexes"
    )

def test_firestore_rules():
    """Test Firestore rules using Firebase emulator"""
    print("🧪 Testing Firestore rules...")
    
    # Start emulator in background
    print("   Starting Firestore emulator...")
    emulator_process = subprocess.Popen(
        "firebase emulators:start --only firestore",
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    # Wait for emulator to start (simple approach)
    import time
    time.sleep(5)
    
    # Run basic tests (if test file exists)
    test_file = Path("firestore.test.js")
    if test_file.exists():
        test_success = run_command(
            "npm test firestore",
            "Running Firestore rules tests"
        )
    else:
        print("   No test file found, skipping automated tests")
        test_success = True
    
    # Stop emulator
    emulator_process.terminate()
    print("   Firestore emulator stopped")
    
    return test_success

def setup_firestore():
    """Main setup function"""
    print("🚀 Firestore Setup and Deployment")
    print("=" * 50)
    
    # Check prerequisites
    if not check_firebase_cli():
        print("\n📥 Please install Firebase CLI:")
        print("   npm install -g firebase-tools")
        print("   Or visit: https://firebase.google.com/docs/cli")
        return False
    
    if not check_project_config():
        print("\n⚙️  Please configure your Firebase project:")
        print("   1. Create a Firebase project at https://console.firebase.google.com/")
        print("   2. Run: firebase login")
        print("   3. Run: firebase use --add")
        print("   4. Update firebase.json with your project ID")
        return False
    
    # Deploy rules and indexes
    print("\n📤 Deploying Firestore configuration...")
    
    rules_success = deploy_firestore_rules()
    indexes_success = deploy_firestore_indexes()
    
    if rules_success and indexes_success:
        print("\n✅ Firestore setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Run database seeder: python scripts/seed_database.py")
        print("   2. Start your backend server")
        print("   3. Test API endpoints with seeded data")
        print("   4. Configure OAuth flows for social media platforms")
        
        print("\n🔗 Useful links:")
        print("   • Firestore Console: https://console.firebase.google.com/project/YOUR_PROJECT/firestore")
        print("   • Security Rules: https://console.firebase.google.com/project/YOUR_PROJECT/firestore/rules")
        print("   • Indexes: https://console.firebase.google.com/project/YOUR_PROJECT/firestore/indexes")
        
        return True
    else:
        print("\n❌ Firestore setup failed")
        print("   Please check the errors above and try again")
        return False

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Setup and deploy Firestore configuration')
    parser.add_argument('--test', action='store_true', help='Test rules using emulator')
    parser.add_argument('--rules-only', action='store_true', help='Deploy only security rules')
    parser.add_argument('--indexes-only', action='store_true', help='Deploy only indexes')
    
    args = parser.parse_args()
    
    if args.test:
        test_firestore_rules()
    elif args.rules_only:
        deploy_firestore_rules()
    elif args.indexes_only:
        deploy_firestore_indexes()
    else:
        setup_firestore()

if __name__ == '__main__':
    main()