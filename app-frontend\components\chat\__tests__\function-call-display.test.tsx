/**
 * Function Call Display Component Tests
 * 
 * Tests for the FunctionCallDisplay component that handles tool usage display
 * from ADK Event objects.
 * 
 * Requirements covered: 4.5, 10.2
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FunctionCallDisplay } from '../function-call-display';
import { FunctionCall, FunctionResponse, ToolExecution } from '@/types/adk';

// Mock the UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className, ...props }: any) => (
    <button onClick={onClick} className={className} {...props}>
      {children}
    </button>
  )
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className, variant }: any) => (
    <span className={`badge ${variant} ${className}`}>{children}</span>
  )
}));

jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => (
    <div className={`card ${className}`}>{children}</div>
  ),
  CardContent: ({ children, className }: any) => (
    <div className={`card-content ${className}`}>{children}</div>
  )
}));

// Mock the ToolExecutionDetail component
jest.mock('../tool-execution-detail', () => ({
  ToolExecutionDetail: ({ isOpen, execution, onClose }: any) => (
    isOpen ? (
      <div data-testid="tool-execution-detail">
        <div>Tool: {execution?.name}</div>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null
  )
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(() => Promise.resolve())
  }
});

describe('FunctionCallDisplay', () => {
  const mockFunctionCalls: FunctionCall[] = [
    {
      id: 'call_1',
      name: 'search_web',
      arguments: {
        query: 'test search',
        limit: 10
      }
    },
    {
      id: 'call_2',
      name: 'analyze_data',
      arguments: {
        data: [1, 2, 3, 4, 5]
      }
    }
  ];

  const mockFunctionResponses: FunctionResponse[] = [
    {
      id: 'call_1',
      name: 'search_web',
      content: 'Found 5 results for test search',
      success: true
    },
    {
      id: 'call_2',
      name: 'analyze_data',
      content: 'Analysis complete',
      success: false,
      error: 'Invalid data format'
    }
  ];

  const mockToolExecutions: ToolExecution[] = [
    {
      id: 'call_1',
      name: 'search_web',
      status: 'completed',
      input: { query: 'test search', limit: 10 },
      output: 'Found 5 results for test search',
      duration_ms: 1500
    },
    {
      id: 'call_2',
      name: 'analyze_data',
      status: 'failed',
      input: { data: [1, 2, 3, 4, 5] },
      error: 'Invalid data format',
      duration_ms: 500
    }
  ];

  const defaultProps = {
    functionCalls: mockFunctionCalls,
    functionResponses: mockFunctionResponses,
    toolExecutions: mockToolExecutions
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders function calls summary when collapsed', () => {
    render(<FunctionCallDisplay {...defaultProps} expanded={false} />);
    
    expect(screen.getByText('2 tools used')).toBeInTheDocument();
    expect(screen.queryByText('search_web')).not.toBeInTheDocument();
  });

  it('shows function call details when expanded', () => {
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    expect(screen.getByText('search_web')).toBeInTheDocument();
    expect(screen.getByText('analyze_data')).toBeInTheDocument();
    expect(screen.getByText('completed')).toBeInTheDocument();
    expect(screen.getByText('failed')).toBeInTheDocument();
  });

  it('toggles expansion when summary button is clicked', async () => {
    const user = userEvent.setup();
    const mockToggle = jest.fn();
    
    render(
      <FunctionCallDisplay 
        {...defaultProps} 
        expanded={false}
        onToggleExpanded={mockToggle}
      />
    );
    
    const toggleButton = screen.getByText('2 tools used');
    await user.click(toggleButton);
    
    expect(mockToggle).toHaveBeenCalledTimes(1);
  });

  it('displays function arguments correctly', () => {
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    expect(screen.getByText(/test search/)).toBeInTheDocument();
    expect(screen.getByText(/\[1,2,3,4,5\]/)).toBeInTheDocument();
  });

  it('shows function response content and status', () => {
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    expect(screen.getByText('Found 5 results for test search')).toBeInTheDocument();
    expect(screen.getByText('Analysis complete')).toBeInTheDocument();
    expect(screen.getByText('Success')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
  });

  it('displays execution duration', () => {
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    expect(screen.getByText('Duration: 1.5s')).toBeInTheDocument();
    expect(screen.getByText('Duration: 500ms')).toBeInTheDocument();
  });

  it('shows error details for failed executions', () => {
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    expect(screen.getByText('Invalid data format')).toBeInTheDocument();
    expect(screen.getByText('Error')).toBeInTheDocument();
  });

  it('copies function call data when copy button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnCopy = jest.fn();
    
    render(
      <FunctionCallDisplay 
        {...defaultProps} 
        expanded={true}
        onCopy={mockOnCopy}
      />
    );
    
    const copyButtons = screen.getAllByTitle('Copy function call');
    await user.click(copyButtons[0]);
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
      expect.stringContaining('search_web')
    );
    expect(mockOnCopy).toHaveBeenCalledWith(
      expect.stringContaining('search_web'),
      'function_call'
    );
  });

  it('opens tool execution detail modal when view details button is clicked', async () => {
    const user = userEvent.setup();
    
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    const detailButtons = screen.getAllByTitle('View details');
    await user.click(detailButtons[0]);
    
    expect(screen.getByTestId('tool-execution-detail')).toBeInTheDocument();
    expect(screen.getByText('Tool: search_web')).toBeInTheDocument();
  });

  it('closes tool execution detail modal when close button is clicked', async () => {
    const user = userEvent.setup();
    
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    // Open modal
    const detailButtons = screen.getAllByTitle('View details');
    await user.click(detailButtons[0]);
    
    expect(screen.getByTestId('tool-execution-detail')).toBeInTheDocument();
    
    // Close modal
    const closeButton = screen.getByText('Close');
    await user.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByTestId('tool-execution-detail')).not.toBeInTheDocument();
    });
  });

  it('handles empty function calls array', () => {
    render(<FunctionCallDisplay functionCalls={[]} />);
    
    expect(screen.queryByText('tools used')).not.toBeInTheDocument();
  });

  it('handles missing function responses', () => {
    render(
      <FunctionCallDisplay 
        functionCalls={mockFunctionCalls}
        expanded={true}
      />
    );
    
    expect(screen.getByText('search_web')).toBeInTheDocument();
    expect(screen.getByText('analyze_data')).toBeInTheDocument();
    // Should show unknown status when no responses available
    expect(screen.getAllByText('unknown')).toHaveLength(2);
  });

  it('handles missing tool executions', () => {
    render(
      <FunctionCallDisplay 
        functionCalls={mockFunctionCalls}
        functionResponses={mockFunctionResponses}
        expanded={true}
      />
    );
    
    expect(screen.getByText('search_web')).toBeInTheDocument();
    expect(screen.getByText('analyze_data')).toBeInTheDocument();
    // Should not show duration when no executions available
    expect(screen.queryByText(/Duration:/)).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <FunctionCallDisplay 
        {...defaultProps} 
        className="custom-class"
      />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles function calls without IDs', () => {
    const callsWithoutIds: FunctionCall[] = [
      {
        name: 'test_function',
        arguments: { test: 'value' }
      }
    ];
    
    render(
      <FunctionCallDisplay 
        functionCalls={callsWithoutIds}
        expanded={true}
      />
    );
    
    expect(screen.getByText('test_function')).toBeInTheDocument();
  });

  it('shows correct status icons', () => {
    render(<FunctionCallDisplay {...defaultProps} expanded={true} />);
    
    // Check that status icons are rendered (we can't easily test the specific icons,
    // but we can verify the status badges are present)
    expect(screen.getByText('completed')).toBeInTheDocument();
    expect(screen.getByText('failed')).toBeInTheDocument();
  });

  it('handles long function names and content gracefully', () => {
    const longFunctionCalls: FunctionCall[] = [
      {
        id: 'long_call',
        name: 'very_long_function_name_that_might_overflow_the_container',
        arguments: {
          very_long_argument_name: 'very long argument value that might cause layout issues'
        }
      }
    ];
    
    render(
      <FunctionCallDisplay 
        functionCalls={longFunctionCalls}
        expanded={true}
      />
    );
    
    expect(screen.getByText('very_long_function_name_that_might_overflow_the_container')).toBeInTheDocument();
  });
});