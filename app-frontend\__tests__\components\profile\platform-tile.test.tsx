import React from 'react'
import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { PlatformTile } from '@/components/profile/platform-tile'
import { renderWithProviders, mockPlatformData } from '../../utils/test-utils'

describe('PlatformTile', () => {
  const defaultProps = {
    timeframe: '30d',
  }

  describe('YouTube Platform', () => {
    const youtubeAccount = mockPlatformData('youtube', {
      id: 'yt-1',
      handle: '@testchannel',
      avatar: 'https://example.com/avatar.jpg',
      metrics: {
        followers: 15420,
        engagement: 4.2,
        growth: { followers: 7.8 },
      },
    })

    it('renders YouTube account information correctly', () => {
      renderWithProviders(
        <PlatformTile account={youtubeAccount} {...defaultProps} />
      )
      
      expect(screen.getByText('@testchannel')).toBeInTheDocument()
      expect(screen.getByText('youtube')).toBeInTheDocument()
      expect(screen.getByText('Subscribers')).toBeInTheDocument()
      expect(screen.getByText('15.4K')).toBeInTheDocument()
    })

    it('displays correct YouTube-specific metrics', () => {
      renderWithProviders(
        <PlatformTile account={youtubeAccount} {...defaultProps} />
      )
      
      expect(screen.getByText('Views')).toBeInTheDocument()
      expect(screen.getByText('125K')).toBeInTheDocument() // Mocked views
      expect(screen.getByText('Avg Views')).toBeInTheDocument()
    })

    it('shows positive growth indicator for YouTube', () => {
      renderWithProviders(
        <PlatformTile account={youtubeAccount} {...defaultProps} />
      )
      
      expect(screen.getByText('+7.8%')).toBeInTheDocument()
      // Should have green color class for positive growth
      const growthElement = screen.getByText('+7.8%')
      expect(growthElement).toHaveClass('text-green-600')
    })
  })

  describe('Instagram Platform', () => {
    const instagramAccount = mockPlatformData('instagram', {
      id: 'ig-1',
      handle: '@testaccount',
      metrics: {
        followers: 8750,
        engagement: 3.1,
        growth: { followers: 5.2 },
      },
    })

    it('renders Instagram account information correctly', () => {
      renderWithProviders(
        <PlatformTile account={instagramAccount} {...defaultProps} />
      )
      
      expect(screen.getByText('@testaccount')).toBeInTheDocument()
      expect(screen.getByText('instagram')).toBeInTheDocument()
      expect(screen.getByText('Followers')).toBeInTheDocument()
      expect(screen.getByText('8.8K')).toBeInTheDocument()
    })

    it('displays correct Instagram-specific metrics', () => {
      renderWithProviders(
        <PlatformTile account={instagramAccount} {...defaultProps} />
      )
      
      expect(screen.getByText('Reach')).toBeInTheDocument()
      expect(screen.getByText('67K')).toBeInTheDocument() // Mocked reach
      expect(screen.getByText('Avg Likes')).toBeInTheDocument()
    })

    it('applies correct Instagram styling', () => {
      renderWithProviders(
        <PlatformTile account={instagramAccount} {...defaultProps} />
      )
      
      const card = screen.getByRole('generic').closest('div')
      expect(card).toHaveClass('text-pink-500', 'bg-pink-50', 'border-pink-200')
    })
  })

  describe('Twitter Platform', () => {
    const twitterAccount = mockPlatformData('twitter', {
      id: 'tw-1',
      handle: '@testtwitter',
      metrics: {
        followers: 3400,
        engagement: 2.7,
        growth: { followers: 2.1 },
      },
    })

    it('renders Twitter account information correctly', () => {
      renderWithProviders(
        <PlatformTile account={twitterAccount} {...defaultProps} />
      )
      
      expect(screen.getByText('@testtwitter')).toBeInTheDocument()
      expect(screen.getByText('twitter')).toBeInTheDocument()
      expect(screen.getByText('Followers')).toBeInTheDocument()
      expect(screen.getByText('3.4K')).toBeInTheDocument()
    })

    it('displays correct Twitter-specific metrics', () => {
      renderWithProviders(
        <PlatformTile account={twitterAccount} {...defaultProps} />
      )
      
      expect(screen.getByText('Impressions')).toBeInTheDocument()
      expect(screen.getByText('45K')).toBeInTheDocument() // Mocked impressions
      expect(screen.getByText('Avg Retweets')).toBeInTheDocument()
    })
  })

  describe('Number Formatting', () => {
    const testCases = [
      { input: 999, expected: '999' },
      { input: 1000, expected: '1.0K' },
      { input: 1500, expected: '1.5K' },
      { input: 1000000, expected: '1.0M' },
      { input: 2500000, expected: '2.5M' },
    ]

    testCases.forEach(({ input, expected }) => {
      it(`formats ${input} as ${expected}`, () => {
        const account = mockPlatformData('youtube', {
          metrics: { followers: input },
        })

        renderWithProviders(
          <PlatformTile account={account} {...defaultProps} />
        )
        
        expect(screen.getByText(expected)).toBeInTheDocument()
      })
    })
  })

  describe('Growth Indicators', () => {
    it('shows positive growth with green color', () => {
      const account = mockPlatformData('youtube', {
        metrics: { growth: { followers: 7.8 } },
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const growthText = screen.getByText('+7.8%')
      expect(growthText).toBeInTheDocument()
      expect(growthText).toHaveClass('text-green-600')
    })

    it('shows negative growth with red color', () => {
      const account = mockPlatformData('youtube', {
        metrics: { growth: { followers: -2.3 } },
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const growthText = screen.getByText('-2.3%')
      expect(growthText).toBeInTheDocument()
      expect(growthText).toHaveClass('text-red-500')
    })

    it('shows zero growth with gray color', () => {
      const account = mockPlatformData('youtube', {
        metrics: { growth: { followers: 0 } },
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const growthText = screen.getByText('0%')
      expect(growthText).toBeInTheDocument()
      expect(growthText).toHaveClass('text-gray-500')
    })

    it('displays trending up icon for positive growth', () => {
      const account = mockPlatformData('youtube', {
        metrics: { growth: { followers: 5.0 } },
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      // Trending up icon should be present
      const trendingUpIcons = screen.getAllByTestId('trending-up-icon')
      expect(trendingUpIcons.length).toBeGreaterThan(0)
    })

    it('displays trending down icon for negative growth', () => {
      const account = mockPlatformData('youtube', {
        metrics: { growth: { followers: -1.5 } },
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      // Trending down icon should be present (note: this depends on icon implementation)
      const growthSection = screen.getByText('-1.5%').closest('div')
      expect(growthSection).toBeInTheDocument()
    })
  })

  describe('Engagement Rate Display', () => {
    it('displays engagement rate correctly', () => {
      const account = mockPlatformData('youtube', {
        metrics: { engagement: 4.2 },
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      expect(screen.getByText('Engagement Rate')).toBeInTheDocument()
      expect(screen.getByText('4.2%')).toBeInTheDocument()
    })

    it('renders engagement rate progress bar', () => {
      const account = mockPlatformData('youtube', {
        metrics: { engagement: 4.2 },
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      // Progress bar should have width based on engagement rate
      const progressBar = screen.getByRole('generic').querySelector('div[style*="width"]')
      expect(progressBar).toBeInTheDocument()
    })
  })

  describe('Timeframe Display', () => {
    it('displays posts count with timeframe', () => {
      const account = mockPlatformData('youtube')

      renderWithProviders(
        <PlatformTile account={account} timeframe="7d" />
      )
      
      expect(screen.getByText('Posts (7d)')).toBeInTheDocument()
    })

    it('displays posts count with different timeframe', () => {
      const account = mockPlatformData('youtube')

      renderWithProviders(
        <PlatformTile account={account} timeframe="90d" />
      )
      
      expect(screen.getByText('Posts (90d)')).toBeInTheDocument()
    })
  })

  describe('User Interactions', () => {
    it('renders more options button', () => {
      const account = mockPlatformData('youtube')

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const moreButton = screen.getByRole('button')
      expect(moreButton).toBeInTheDocument()
    })

    it('handles more options button click', async () => {
      const user = userEvent.setup()
      const account = mockPlatformData('youtube')

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const moreButton = screen.getByRole('button')
      await user.click(moreButton)
      
      // Button should be clickable (actual dropdown functionality would be tested separately)
      expect(moreButton).toBeInTheDocument()
    })
  })

  describe('Avatar Handling', () => {
    it('displays avatar image when provided', () => {
      const account = mockPlatformData('youtube', {
        avatar: 'https://example.com/avatar.jpg',
        handle: '@testchannel',
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const avatar = screen.getByRole('img', { name: '@testchannel' })
      expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg')
    })

    it('displays fallback icon when no avatar', () => {
      const account = mockPlatformData('youtube', {
        avatar: undefined,
        handle: '@testchannel',
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      // Avatar fallback should contain platform icon
      const avatarFallback = screen.getByRole('img', { name: '@testchannel' }).closest('span')
      expect(avatarFallback).toBeInTheDocument()
    })
  })

  describe('Platform-Specific Styling', () => {
    it('applies YouTube theme colors', () => {
      const account = mockPlatformData('youtube')

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const card = screen.getByRole('generic').closest('div')
      expect(card).toHaveClass('text-red-500', 'bg-red-50', 'border-red-200')
    })

    it('applies hover effects', () => {
      const account = mockPlatformData('youtube')

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      const card = screen.getByRole('generic').closest('div')
      expect(card).toHaveClass('transition-all', 'hover:shadow-md')
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels and semantic structure', () => {
      const account = mockPlatformData('youtube', {
        handle: '@testchannel',
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      expect(screen.getByRole('img', { name: '@testchannel' })).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('maintains proper heading hierarchy', () => {
      const account = mockPlatformData('youtube', {
        handle: '@testchannel',
      })

      renderWithProviders(
        <PlatformTile account={account} {...defaultProps} />
      )
      
      expect(screen.getByRole('heading', { name: '@testchannel' })).toBeInTheDocument()
    })
  })
})