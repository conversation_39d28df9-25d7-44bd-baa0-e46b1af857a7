#!/usr/bin/env python3
"""
Database Seeder for Social Media Manager Backend

This script loads sample data into Firestore for development and demo purposes.
Run this script to populate your development database with realistic test data.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add the app directory to the path
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app-agents'))

from google.cloud import firestore
from google.cloud.firestore_v1.base_query import FieldFilter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseSeeder:
    """Seeds the Firestore database with sample data"""
    
    def __init__(self, project_id: str = None):
        """Initialize the database seeder"""
        self.db = firestore.Client(project=project_id)
        self.batch_size = 500  # Firestore batch limit
        
    async def load_sample_data(self, data_file: str = None) -> Dict[str, Any]:
        """Load sample data from JSON file"""
        if not data_file:
            # Default to sample_data.json in scripts directory
            script_dir = Path(__file__).parent
            data_file = script_dir / 'sample_data.json'
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"✓ Loaded sample data from {data_file}")
            return data
        except FileNotFoundError:
            logger.error(f"Sample data file not found: {data_file}")
            logger.info("Please run generate_sample_data.py first to create sample data")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in sample data file: {e}")
            return {}
    
    def clear_collections(self, collections: List[str]):
        """Clear specified collections (use with caution!)"""
        logger.warning("🗑️  Clearing existing collections...")
        
        for collection_name in collections:
            collection_ref = self.db.collection(collection_name)
            
            # Delete documents in batches
            docs = collection_ref.limit(self.batch_size).stream()
            deleted = 0
            
            batch = self.db.batch()
            for doc in docs:
                batch.delete(doc.reference)
                deleted += 1
                
                if deleted % self.batch_size == 0:
                    batch.commit()
                    batch = self.db.batch()
            
            # Commit remaining deletes
            if deleted % self.batch_size != 0:
                batch.commit()
            
            logger.info(f"   • Deleted {deleted} documents from {collection_name}")
    
    def seed_users(self, users: List[Dict]) -> int:
        """Seed users collection"""
        logger.info("👥 Seeding users...")
        
        batch = self.db.batch()
        count = 0
        
        for user in users:
            user_ref = self.db.collection('users').document(user['id'])
            
            # Prepare user document
            user_doc = {
                'id': user['id'],
                'name': user['name'],
                'email': user['email'],
                'bio': user.get('bio', ''),
                'avatar': user.get('avatar', ''),
                'industry': user.get('industry', ''),
                'created_at': firestore.SERVER_TIMESTAMP,
                'last_active': datetime.fromisoformat(user['last_active'].replace('Z', '+00:00')),
                'subscription_plan': user.get('subscription_plan', 'free'),
                'preferences': user.get('preferences', {}),
                'status': 'active'
            }
            
            batch.set(user_ref, user_doc)
            count += 1
            
            # Commit batch if it reaches the limit
            if count % self.batch_size == 0:
                batch.commit()
                batch = self.db.batch()
        
        # Commit remaining documents
        if count % self.batch_size != 0:
            batch.commit()
        
        logger.info(f"   ✓ Seeded {count} users")
        return count
    
    def seed_connected_accounts(self, accounts: List[Dict]) -> int:
        """Seed connected_accounts collection"""
        logger.info("🔗 Seeding connected accounts...")
        
        batch = self.db.batch()
        count = 0
        
        for account in accounts:
            account_ref = self.db.collection('connected_accounts').document(account['id'])
            
            # Prepare account document
            account_doc = {
                'id': account['id'],
                'user_id': account['user_id'],
                'platform': account['platform'],
                'handle': account['handle'],
                'display_name': account['display_name'],
                'avatar': account.get('avatar', ''),
                'connected_at': datetime.fromisoformat(account['connected_at'].replace('Z', '+00:00')),
                'last_sync': datetime.fromisoformat(account['last_sync'].replace('Z', '+00:00')),
                'status': account.get('status', 'active'),
                'permissions': account.get('permissions', []),
                'metrics': account.get('metrics', {}),
                'account_info': account.get('account_info', {}),
                'oauth_token': None,  # Don't store real tokens in sample data
                'oauth_refresh_token': None
            }
            
            batch.set(account_ref, account_doc)
            count += 1
            
            if count % self.batch_size == 0:
                batch.commit()
                batch = self.db.batch()
        
        if count % self.batch_size != 0:
            batch.commit()
        
        logger.info(f"   ✓ Seeded {count} connected accounts")
        return count
    
    def seed_content_plans(self, plans: List[Dict]) -> int:
        """Seed content_plans collection"""
        logger.info("📋 Seeding content plans...")
        
        batch = self.db.batch()
        count = 0
        
        for plan in plans:
            plan_ref = self.db.collection('content_plans').document(plan['id'])
            
            # Prepare plan document
            plan_doc = {
                'id': plan['id'],
                'user_id': plan['user_id'],
                'title': plan['title'],
                'description': plan.get('description', ''),
                'timeframe_start': datetime.fromisoformat(plan['timeframe_start'].replace('Z', '+00:00')),
                'timeframe_end': datetime.fromisoformat(plan['timeframe_end'].replace('Z', '+00:00')),
                'status': plan.get('status', 'draft'),
                'platforms': plan.get('platforms', []),
                'goals': plan.get('goals', []),
                'target_metrics': plan.get('target_metrics', {}),
                'created_at': datetime.fromisoformat(plan['created_at'].replace('Z', '+00:00')),
                'updated_at': datetime.fromisoformat(plan['updated_at'].replace('Z', '+00:00')),
                'post_count': len(plan.get('posts', [])),
                'ai_generated': plan.get('ai_generated', False)
            }
            
            batch.set(plan_ref, plan_doc)
            count += 1
            
            # Seed posts as subcollection
            if 'posts' in plan and plan['posts']:
                self.seed_planned_posts(plan['id'], plan['posts'])
            
            if count % self.batch_size == 0:
                batch.commit()
                batch = self.db.batch()
        
        if count % self.batch_size != 0:
            batch.commit()
        
        logger.info(f"   ✓ Seeded {count} content plans")
        return count
    
    def seed_planned_posts(self, plan_id: str, posts: List[Dict]) -> int:
        """Seed planned posts as subcollection of content plans"""
        batch = self.db.batch()
        count = 0
        
        for post in posts:
            post_ref = self.db.collection('content_plans').document(plan_id).collection('posts').document(post['id'])
            
            # Prepare post document
            post_doc = {
                'id': post['id'],
                'title': post['title'],
                'description': post.get('description', ''),
                'content_type': post.get('content_type', 'post'),
                'platform': post['platform'],
                'account_id': post.get('account_id', ''),
                'scheduled_time': datetime.fromisoformat(post['scheduled_time'].replace('Z', '+00:00')),
                'status': post.get('status', 'draft'),
                'tags': post.get('tags', []),
                'estimated_reach': post.get('estimated_reach', 0),
                'content_pillars': post.get('content_pillars', []),
                'ai_generated': post.get('ai_generated', False),
                'performance_prediction': post.get('performance_prediction', {}),
                'created_at': firestore.SERVER_TIMESTAMP
            }
            
            batch.set(post_ref, post_doc)
            count += 1
            
            if count % self.batch_size == 0:
                batch.commit()
                batch = self.db.batch()
        
        if count % self.batch_size != 0:
            batch.commit()
        
        return count
    
    def seed_chat_messages(self, messages: List[Dict]) -> int:
        """Seed chat_messages collection"""
        logger.info("💬 Seeding chat messages...")
        
        # Group messages by user for better organization
        user_messages = {}
        for message in messages:
            user_id = message['user_id']
            if user_id not in user_messages:
                user_messages[user_id] = []
            user_messages[user_id].append(message)
        
        total_count = 0
        
        for user_id, user_msgs in user_messages.items():
            batch = self.db.batch()
            count = 0
            
            for message in user_msgs:
                msg_ref = self.db.collection('chat_messages').document(message['id'])
                
                # Prepare message document
                msg_doc = {
                    'id': message['id'],
                    'user_id': message['user_id'],
                    'role': message['role'],
                    'content': message['content'],
                    'timestamp': datetime.fromisoformat(message['timestamp'].replace('Z', '+00:00')),
                    'session_id': message.get('session_id', ''),
                    'metadata': message.get('metadata', {}),
                    'created_at': firestore.SERVER_TIMESTAMP
                }
                
                batch.set(msg_ref, msg_doc)
                count += 1
                
                if count % self.batch_size == 0:
                    batch.commit()
                    batch = self.db.batch()
            
            if count % self.batch_size != 0:
                batch.commit()
            
            total_count += count
        
        logger.info(f"   ✓ Seeded {total_count} chat messages")
        return total_count
    
    def seed_analytics(self, analytics_data: Dict[str, Any]) -> int:
        """Seed analytics_data collection"""
        logger.info("📊 Seeding analytics data...")
        
        batch = self.db.batch()
        count = 0
        
        for account_id, analytics in analytics_data.items():
            analytics_ref = self.db.collection('analytics_data').document(account_id)
            
            # Convert date strings to datetime objects
            daily_metrics = []
            for daily_data in analytics.get('daily_metrics', []):
                daily_metric = daily_data.copy()
                daily_metric['date'] = datetime.fromisoformat(daily_data['date'])
                daily_metrics.append(daily_metric)
            
            # Prepare analytics document
            analytics_doc = {
                'account_id': analytics['account_id'],
                'platform': analytics['platform'],
                'daily_metrics': daily_metrics,
                'summary': analytics.get('summary', {}),
                'last_updated': firestore.SERVER_TIMESTAMP,
                'data_range_days': len(daily_metrics)
            }
            
            batch.set(analytics_ref, analytics_doc)
            count += 1
            
            if count % self.batch_size == 0:
                batch.commit()
                batch = self.db.batch()
        
        if count % self.batch_size != 0:
            batch.commit()
        
        logger.info(f"   ✓ Seeded analytics for {count} accounts")
        return count
    
    def create_indexes(self):
        """Create necessary Firestore indexes"""
        logger.info("🔍 Creating database indexes...")
        
        # Note: Firestore indexes are typically created via Firebase Console
        # or firestore.indexes.json file. This is a placeholder for manual creation.
        
        index_recommendations = [
            "users: email (ascending)",
            "connected_accounts: user_id (ascending), platform (ascending)",
            "content_plans: user_id (ascending), status (ascending)",
            "chat_messages: user_id (ascending), timestamp (descending)",
            "analytics_data: account_id (ascending)"
        ]
        
        logger.info("   Recommended indexes to create manually:")
        for idx in index_recommendations:
            logger.info(f"     • {idx}")
    
    async def seed_all(self, data_file: str = None, clear_existing: bool = False) -> Dict[str, int]:
        """Seed all collections with sample data"""
        logger.info("🌱 Starting database seeding process...")
        logger.info("=" * 60)
        
        # Load sample data
        sample_data = await self.load_sample_data(data_file)
        if not sample_data:
            logger.error("❌ Failed to load sample data. Aborting.")
            return {}
        
        # Clear existing data if requested
        if clear_existing:
            collections_to_clear = [
                'users', 'connected_accounts', 'content_plans', 
                'chat_messages', 'analytics_data'
            ]
            self.clear_collections(collections_to_clear)
        
        # Seed all collections
        results = {}
        
        try:
            results['users'] = self.seed_users(sample_data.get('users', []))
            results['connected_accounts'] = self.seed_connected_accounts(sample_data.get('connected_accounts', []))
            results['content_plans'] = self.seed_content_plans(sample_data.get('content_plans', []))
            results['chat_messages'] = self.seed_chat_messages(sample_data.get('chat_messages', []))
            results['analytics_data'] = self.seed_analytics(sample_data.get('analytics_data', {}))
            
            # Create indexes
            self.create_indexes()
            
            logger.info("=" * 60)
            logger.info("✅ Database seeding completed successfully!")
            logger.info("\n📊 Seeding Summary:")
            for collection, count in results.items():
                logger.info(f"   • {collection.replace('_', ' ').title()}: {count} records")
            
            total_records = sum(results.values())
            logger.info(f"\n🎯 Total records seeded: {total_records}")
            
            logger.info("\n🚀 Next steps:")
            logger.info("   1. Start your FastAPI backend server")
            logger.info("   2. Test API endpoints with the seeded data")
            logger.info("   3. Run frontend application to see populated content")
            logger.info("   4. Use sample users for authentication testing")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error during seeding: {e}")
            logger.error("   Check your Firestore permissions and network connection")
            return {}

async def main():
    """Main function to run the database seeder"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Seed Firestore database with sample data')
    parser.add_argument('--project-id', help='Google Cloud Project ID')
    parser.add_argument('--data-file', help='Path to sample data JSON file')
    parser.add_argument('--clear', action='store_true', help='Clear existing data before seeding')
    parser.add_argument('--env', choices=['dev', 'staging', 'prod'], default='dev', 
                       help='Environment to seed (default: dev)')
    
    args = parser.parse_args()
    
    # Safety check for production
    if args.env == 'prod' and not args.clear:
        logger.warning("⚠️  Running in production mode. Use --clear flag to confirm.")
        return
    
    # Initialize seeder
    seeder = DatabaseSeeder(project_id=args.project_id)
    
    # Run seeding
    results = await seeder.seed_all(
        data_file=args.data_file,
        clear_existing=args.clear
    )
    
    if results:
        logger.info("🎉 Database ready for development and testing!")
    else:
        logger.error("❌ Seeding failed. Please check logs and try again.")

if __name__ == '__main__':
    asyncio.run(main())