{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscription_plan", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "industry", "order": "ASCENDING"}, {"fieldPath": "last_active", "order": "DESCENDING"}]}, {"collectionGroup": "connected_accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "platform", "order": "ASCENDING"}]}, {"collectionGroup": "connected_accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "last_sync", "order": "DESCENDING"}]}, {"collectionGroup": "connected_accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "connected_accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "platform", "order": "ASCENDING"}, {"fieldPath": "connected_at", "order": "DESCENDING"}]}, {"collectionGroup": "content_plans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "content_plans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "content_plans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "timeframe_start", "order": "ASCENDING"}]}, {"collectionGroup": "content_plans", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "timeframe_end", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "platform", "order": "ASCENDING"}, {"fieldPath": "scheduled_time", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "scheduled_time", "order": "ASCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "content_type", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "chat_messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "chat_messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "session_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "chat_messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analytics_data", "queryScope": "COLLECTION", "fields": [{"fieldPath": "account_id", "order": "ASCENDING"}]}, {"collectionGroup": "analytics_data", "queryScope": "COLLECTION", "fields": [{"fieldPath": "platform", "order": "ASCENDING"}, {"fieldPath": "last_updated", "order": "DESCENDING"}]}], "fieldOverrides": []}