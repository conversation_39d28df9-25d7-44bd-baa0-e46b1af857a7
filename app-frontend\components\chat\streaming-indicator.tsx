"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Bot, Square, Wifi, WifiOff, RotateCcw } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface StreamingIndicatorProps {
  isStreaming: boolean;
  isConnected?: boolean;
  isReconnecting?: boolean;
  agentName?: string;
  canInterrupt?: boolean;
  onInterrupt?: () => void;
  onReconnect?: () => void;
  className?: string;
}

export function StreamingIndicator({
  isStreaming,
  isConnected = true,
  isReconnecting = false,
  agentName = 'Assistant',
  canInterrupt = true,
  onInterrupt,
  onReconnect,
  className
}: StreamingIndicatorProps) {
  const [dots, setDots] = useState('');

  // Animate dots for streaming indicator
  useEffect(() => {
    if (!isStreaming && !isReconnecting) {
      setDots('');
      return;
    }

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isStreaming, isReconnecting]);

  if (!isStreaming && !isReconnecting && isConnected) {
    return null;
  }

  return (
    <Card className={cn("bg-muted/50 border-dashed", className)}>
      <CardContent className="p-3">
        <div className="flex items-center gap-3">
          <Avatar className="w-8 h-8">
            <AvatarFallback className={cn(
              "transition-colors",
              isReconnecting ? "bg-orange-100 text-orange-600" :
              !isConnected ? "bg-red-100 text-red-600" :
              "bg-blue-100 text-blue-600"
            )}>
              <Bot className="w-4 h-4" />
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              {/* Connection Status Icon */}
              {isReconnecting ? (
                <RotateCcw className="w-4 h-4 text-orange-500 animate-spin" />
              ) : !isConnected ? (
                <WifiOff className="w-4 h-4 text-red-500" />
              ) : (
                <Wifi className="w-4 h-4 text-green-500" />
              )}

              {/* Status Text */}
              <div className="text-sm">
                {isReconnecting ? (
                  <span className="text-orange-600">
                    Reconnecting{dots}
                  </span>
                ) : !isConnected ? (
                  <span className="text-red-600">
                    Connection lost
                  </span>
                ) : isStreaming ? (
                  <span className="text-blue-600">
                    {agentName} is thinking{dots}
                  </span>
                ) : null}
              </div>
            </div>

            {/* Additional Status Info */}
            {isStreaming && (
              <div className="text-xs text-muted-foreground mt-1">
                Streaming response in real-time
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {isStreaming && canInterrupt && onInterrupt && (
              <Button
                variant="outline"
                size="sm"
                onClick={onInterrupt}
                className="h-8 px-3 text-xs"
              >
                <Square className="w-3 h-3 mr-1" />
                Stop
              </Button>
            )}

            {!isConnected && !isReconnecting && onReconnect && (
              <Button
                variant="outline"
                size="sm"
                onClick={onReconnect}
                className="h-8 px-3 text-xs"
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                Retry
              </Button>
            )}
          </div>
        </div>

        {/* Streaming Progress Bar */}
        {isStreaming && (
          <div className="mt-2">
            <div className="w-full bg-muted rounded-full h-1">
              <div className="bg-blue-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export interface TypingIndicatorProps {
  show: boolean;
  agentName?: string;
  message?: string;
  className?: string;
}

export function TypingIndicator({
  show,
  agentName = 'Assistant',
  message = 'is typing',
  className
}: TypingIndicatorProps) {
  if (!show) return null;

  return (
    <div className={cn("flex gap-3 justify-start", className)}>
      <Avatar className="w-8 h-8 mt-1">
        <AvatarFallback className="bg-blue-100 text-blue-600">
          <Bot className="w-4 h-4" />
        </AvatarFallback>
      </Avatar>
      
      <Card className="bg-card border-dashed">
        <CardContent className="p-3">
          <div className="flex items-center gap-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
              <div 
                className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" 
                style={{ animationDelay: '0.1s' }} 
              />
              <div 
                className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" 
                style={{ animationDelay: '0.2s' }} 
              />
            </div>
            <span className="text-xs text-muted-foreground">
              {agentName} {message}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export interface ConnectionStatusProps {
  isConnected: boolean;
  isReconnecting?: boolean;
  lastError?: { error: string; error_code: string } | null;
  onReconnect?: () => void;
  className?: string;
}

export function ConnectionStatus({
  isConnected,
  isReconnecting = false,
  lastError,
  onReconnect,
  className
}: ConnectionStatusProps) {
  if (isConnected && !isReconnecting && !lastError) {
    return null;
  }

  return (
    <div className={cn("p-3 border-b bg-muted/30", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isReconnecting ? (
            <>
              <RotateCcw className="w-4 h-4 text-orange-500 animate-spin" />
              <span className="text-sm text-orange-600">Reconnecting...</span>
            </>
          ) : !isConnected ? (
            <>
              <WifiOff className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-600">Connection lost</span>
            </>
          ) : lastError ? (
            <>
              <WifiOff className="w-4 h-4 text-yellow-500" />
              <span className="text-sm text-yellow-600">
                Connection issue: {lastError.error}
              </span>
            </>
          ) : null}
        </div>

        {!isConnected && !isReconnecting && onReconnect && (
          <Button
            variant="outline"
            size="sm"
            onClick={onReconnect}
            className="h-7 px-2 text-xs"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Reconnect
          </Button>
        )}
      </div>

      {lastError && lastError.error_code && (
        <div className="text-xs text-muted-foreground mt-1">
          Error code: {lastError.error_code}
        </div>
      )}
    </div>
  );
}