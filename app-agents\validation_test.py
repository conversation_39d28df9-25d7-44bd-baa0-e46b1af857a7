#!/usr/bin/env python3
"""Test validation functionality"""

import sys
sys.path.append('app')

from app.models import (
    ADKRunAgentRequest,
    ADKMessage,
    MessageRole,
    ADKRequestValidator
)

print("Testing validation...")

# Test valid request
try:
    valid_request = ADKRunAgentRequest(
        app_name="content_planner",
        user_id="user_123",
        session_id="session_456",
        new_message=ADKMessage.from_text("Hello", MessageRole.USER)
    )
    
    result = ADKRequestValidator.validate_run_agent_request(valid_request)
    if result.is_valid:
        print("✅ Valid request passes validation")
    else:
        print(f"❌ Valid request failed validation: {result.errors}")
        
except Exception as e:
    print(f"❌ Validation test failed: {e}")

print("Validation test completed!")