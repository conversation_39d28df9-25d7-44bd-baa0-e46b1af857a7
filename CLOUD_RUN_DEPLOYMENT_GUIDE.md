# Cloud Run Deployment Guide

This guide provides comprehensive instructions for deploying the Social Media Manager application to Google Cloud Run.

## Overview

The deployment consists of two Cloud Run services:
- **Backend API** - FastAPI application with AI agents
- **Frontend** - Next.js application with React UI

Both services are containerized and deployed to Google Cloud Run for automatic scaling and management.

## Architecture

```
Internet -> Cloud Load Balancer -> Cloud Run Services
                                 ├── Frontend (Next.js)
                                 └── Backend (FastAPI)
                                     ├── Secret Manager (OAuth tokens)
                                     ├── Firestore (User data)
                                     └── BigQuery (Analytics)
```

## Prerequisites

### 1. Google Cloud Platform Setup

1. **Create or Select Project**:
   ```bash
   # Create new project
   gcloud projects create social-media-manager-prod --name="Social Media Manager"
   
   # Set active project
   gcloud config set project social-media-manager-prod
   ```

2. **Enable Billing**: Ensure billing is enabled for your project

3. **Install gcloud CLI**: [Download and install](https://cloud.google.com/sdk/docs/install)

### 2. Local Development Tools

1. **Docker**: [Download and install](https://docs.docker.com/get-docker/)
2. **Git**: For version control
3. **Text Editor**: VS Code recommended

### 3. API Keys and OAuth Setup

1. **YouTube Data API v3**:
   - Enable in Google Cloud Console
   - Create OAuth 2.0 credentials
   - Create API key for public data

2. **Instagram Graph API**:
   - Create Facebook App
   - Add Instagram Graph API product
   - Configure OAuth redirect URIs

3. **Google Search API** (optional):
   - Create Custom Search Engine
   - Generate Search API key

## Quick Deployment

### Option 1: Automated Script (Recommended)

```bash
# Linux/macOS
cd deployment
chmod +x deploy.sh
./deploy.sh --project your-project-id

# Windows PowerShell
cd deployment
.\deploy.ps1 -ProjectId your-project-id
```

### Option 2: Manual Deployment

Follow the step-by-step instructions below.

## Step-by-Step Deployment

### Step 1: Environment Setup

1. **Authenticate with gcloud**:
   ```bash
   gcloud auth login
   gcloud auth configure-docker
   ```

2. **Set project and region**:
   ```bash
   export PROJECT_ID="your-project-id"
   export REGION="us-central1"
   gcloud config set project $PROJECT_ID
   ```

### Step 2: Enable Required APIs

```bash
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable bigquery.googleapis.com
```

### Step 3: Create Service Account

```bash
# Create service account
gcloud iam service-accounts create social-media-backend \
    --display-name="Social Media Backend Service"

# Grant permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/datastore.user"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:social-media-backend@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/bigquery.user"
```

### Step 4: Store Secrets

```bash
# Create secrets in Secret Manager
echo -n "your-jwt-secret-key" | gcloud secrets create app-secret-key --data-file=-
echo -n "your-google-client-id" | gcloud secrets create google-client-id --data-file=-
echo -n "your-google-client-secret" | gcloud secrets create google-client-secret --data-file=-
echo -n "your-youtube-api-key" | gcloud secrets create youtube-api-key --data-file=-
echo -n "your-instagram-app-id" | gcloud secrets create instagram-app-id --data-file=-
echo -n "your-instagram-app-secret" | gcloud secrets create instagram-app-secret --data-file=-
```

### Step 5: Build and Push Docker Images

```bash
# Build backend image
docker build -t gcr.io/$PROJECT_ID/social-media-backend ./app-agents

# Build frontend image
docker build -t gcr.io/$PROJECT_ID/social-media-frontend ./app-frontend

# Push images
docker push gcr.io/$PROJECT_ID/social-media-backend
docker push gcr.io/$PROJECT_ID/social-media-frontend
```

### Step 6: Deploy Backend Service

```bash
# Update configuration with your project ID
sed "s/PROJECT_ID/$PROJECT_ID/g" deployment/cloud-run-backend.yaml > /tmp/backend-config.yaml

# Deploy backend
gcloud run services replace /tmp/backend-config.yaml \
    --region=$REGION
```

### Step 7: Deploy Frontend Service

```bash
# Get backend URL
BACKEND_URL=$(gcloud run services describe social-media-backend \
    --region=$REGION \
    --format="value(status.url)")

# Update frontend configuration
sed -e "s/PROJECT_ID/$PROJECT_ID/g" \
    -e "s|https://your-backend-domain.com|$BACKEND_URL|g" \
    deployment/cloud-run-frontend.yaml > /tmp/frontend-config.yaml

# Deploy frontend
gcloud run services replace /tmp/frontend-config.yaml \
    --region=$REGION
```

### Step 8: Get Service URLs

```bash
# Get service URLs
BACKEND_URL=$(gcloud run services describe social-media-backend \
    --region=$REGION \
    --format="value(status.url)")

FRONTEND_URL=$(gcloud run services describe social-media-frontend \
    --region=$REGION \
    --format="value(status.url)")

echo "Backend URL: $BACKEND_URL"
echo "Frontend URL: $FRONTEND_URL"
```

## Post-Deployment Configuration

### 1. Update OAuth Redirect URIs

Update your OAuth applications with the deployed URLs:

**Google OAuth (YouTube)**:
- Authorized redirect URIs: `https://your-backend-url.run.app/api/auth/callback/youtube`

**Facebook OAuth (Instagram)**:
- Valid OAuth Redirect URIs: `https://your-backend-url.run.app/api/auth/callback/instagram`

### 2. Test the Deployment

```bash
# Test backend health
curl "$BACKEND_URL/health"

# Test frontend
curl "$FRONTEND_URL/api/health"
```

### 3. Setup Firestore

```bash
# Deploy Firestore rules and indexes
firebase deploy --only firestore --project $PROJECT_ID
```

## Custom Domains (Optional)

### 1. Setup Domain Mapping

```bash
# Map custom domain to backend
gcloud run domain-mappings create \
    --service=social-media-backend \
    --domain=api.yourdomain.com \
    --region=$REGION

# Map custom domain to frontend
gcloud run domain-mappings create \
    --service=social-media-frontend \
    --domain=app.yourdomain.com \
    --region=$REGION
```

### 2. Configure DNS

Add CNAME records to your DNS:
```
api.yourdomain.com CNAME ghs.googlehosted.com
app.yourdomain.com CNAME ghs.googlehosted.com
```

## Monitoring and Logging

### 1. View Logs

```bash
# Backend logs
gcloud run services logs tail social-media-backend --region=$REGION

# Frontend logs
gcloud run services logs tail social-media-frontend --region=$REGION
```

### 2. Monitor Performance

Access Cloud Console for monitoring:
- **Cloud Run**: https://console.cloud.google.com/run
- **Logs**: https://console.cloud.google.com/logs
- **Monitoring**: https://console.cloud.google.com/monitoring

### 3. Set Up Alerts

```bash
# Create log-based metric for errors
gcloud logging metrics create error_rate \
    --description="Error rate for social media app" \
    --log-filter='resource.type="cloud_run_revision" AND severity="ERROR"'
```

## Environment Management

### Staging Environment

1. Create staging project:
   ```bash
   gcloud projects create social-media-manager-staging
   ```

2. Deploy with staging configuration:
   ```bash
   ./deploy.sh --project social-media-manager-staging
   ```

### Production Environment

1. Use production project
2. Set appropriate resource limits
3. Configure backup and monitoring
4. Implement blue-green deployments

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Deploy to Cloud Run

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - id: 'auth'
      uses: 'google-github-actions/auth@v1'
      with:
        credentials_json: '${{ secrets.GCP_SA_KEY }}'
    
    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v1'
    
    - name: Deploy to Cloud Run
      run: |
        cd deployment
        ./deploy.sh --project ${{ secrets.GCP_PROJECT_ID }} --skip-secrets
```

## Security Best Practices

### 1. Service Account Permissions
- Use least privilege principle
- Separate service accounts for different environments
- Regular permission audits

### 2. Secret Management
- Store all sensitive data in Secret Manager
- Use IAM for secret access control
- Regular secret rotation

### 3. Network Security
- Use private services where possible
- Configure VPC if needed
- Enable audit logging

## Troubleshooting

### Common Issues

1. **Image Build Failures**:
   ```bash
   # Check build logs
   gcloud builds list --limit=10
   ```

2. **Service Deploy Failures**:
   ```bash
   # Check service events
   gcloud run revisions list --service=social-media-backend --region=$REGION
   ```

3. **Permission Issues**:
   ```bash
   # Check IAM policies
   gcloud projects get-iam-policy $PROJECT_ID
   ```

4. **Secret Access Issues**:
   ```bash
   # Test secret access
   gcloud secrets versions access latest --secret="app-secret-key"
   ```

### Debug Commands

```bash
# Service status
gcloud run services describe social-media-backend --region=$REGION

# Service logs
gcloud run services logs read social-media-backend --region=$REGION --limit=50

# Service metrics
gcloud run services list --region=$REGION
```

## Cost Optimization

### 1. Resource Optimization
- Set appropriate CPU and memory limits
- Configure concurrency levels
- Use minimum instances wisely

### 2. Request Optimization
- Enable compression
- Optimize container startup time
- Use efficient Docker layers

### 3. Monitoring Costs
```bash
# Set up billing alerts
gcloud alpha billing budgets create \
    --billing-account=$BILLING_ACCOUNT \
    --display-name="Social Media Manager Budget" \
    --budget-amount=100USD
```

## Backup and Recovery

### 1. Database Backup
```bash
# Export Firestore data
gcloud firestore export gs://your-backup-bucket/firestore-backup
```

### 2. Configuration Backup
- Store deployment configurations in version control
- Document all manual configuration steps
- Regular testing of restore procedures

## Support and Maintenance

### Regular Tasks
1. **Security Updates**: Keep base images updated
2. **Dependency Updates**: Update application dependencies
3. **Performance Monitoring**: Regular performance reviews
4. **Cost Reviews**: Monthly cost analysis
5. **Backup Testing**: Quarterly backup restore tests

### Emergency Procedures
1. **Rollback**: Use previous revision if issues occur
2. **Scale Down**: Reduce traffic during incidents
3. **Health Checks**: Monitor service health continuously

For additional support, refer to:
- [Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Google Cloud Support](https://cloud.google.com/support)
- Project-specific documentation and runbooks