"use client";

import { useState, useCallback, useEffect } from 'react';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ChatError, ADKAgentInfo } from '@/types/adk';

export interface UseAgentSelectionOptions {
  userId: string;
  currentAgent?: string;
  currentSessionId?: string;
  preserveHistory?: boolean;
  onAgentChange?: (agentName: string, sessionId: string) => void;
  onError?: (error: ChatError) => void;
}

export interface UseAgentSelectionReturn {
  selectedAgent: string;
  isChangingAgent: boolean;
  error: ChatError | null;
  selectAgent: (agentName: string) => Promise<void>;
  clearError: () => void;
}

export function useAgentSelection(options: UseAgentSelectionOptions): UseAgentSelectionReturn {
  const {
    userId,
    currentAgent = 'content_planner',
    currentSessionId,
    preserveHistory = false,
    onAgentChange,
    onError
  } = options;

  const queryClient = useQueryClient();
  const [selectedAgent, setSelectedAgent] = useState<string>(currentAgent);
  const [lastError, setLastError] = useState<ChatError | null>(null);

  // Update selected agent when currentAgent prop changes
  useEffect(() => {
    if (currentAgent && currentAgent !== selectedAgent) {
      setSelectedAgent(currentAgent);
    }
  }, [currentAgent, selectedAgent]);

  // Clear error function
  const clearError = useCallback(() => {
    setLastError(null);
  }, []);

  // Agent selection mutation
  const selectAgentMutation = useMutation({
    mutationFn: async (agentName: string): Promise<{ sessionId: string; agentName: string }> => {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
      const authToken = localStorage.getItem('access_token') || 'dev-token';

      // If selecting the same agent, just return current session
      if (agentName === selectedAgent && currentSessionId) {
        return { sessionId: currentSessionId, agentName };
      }

      try {
        // Create new session for the selected agent
        const response = await fetch(`${baseUrl}/chat/session`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
          },
          body: JSON.stringify({
            user_id: userId,
            agent_name: agentName,
            preserve_history: preserveHistory,
            previous_session_id: currentSessionId
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        return {
          sessionId: result.session_id,
          agentName: agentName
        };
      } catch (error) {
        throw new Error(
          error instanceof Error 
            ? error.message 
            : `Failed to switch to agent: ${agentName}`
        );
      }
    },
    onSuccess: (result) => {
      setSelectedAgent(result.agentName);
      setLastError(null);

      // Clear chat history cache if not preserving history
      if (!preserveHistory) {
        queryClient.removeQueries({ queryKey: ["chat", "history"] });
      }

      // Update session persistence
      if (typeof window !== 'undefined') {
        localStorage.setItem('chat_session_id', result.sessionId);
        localStorage.setItem('selected_agent', result.agentName);
      }

      // Notify parent component
      onAgentChange?.(result.agentName, result.sessionId);
    },
    onError: (error) => {
      console.error("Failed to select agent:", error);
      
      const chatError: ChatError = {
        error: error instanceof Error ? error.message : 'Failed to select agent',
        error_code: 'AGENT_SELECTION_ERROR',
        session_id: currentSessionId
      };
      
      setLastError(chatError);
      onError?.(chatError);
    },
  });

  // Select agent function
  const selectAgent = useCallback(async (agentName: string) => {
    if (agentName === selectedAgent) {
      return; // No change needed
    }

    clearError();
    await selectAgentMutation.mutateAsync(agentName);
  }, [selectedAgent, selectAgentMutation, clearError]);

  return {
    selectedAgent,
    isChangingAgent: selectAgentMutation.isPending,
    error: lastError,
    selectAgent,
    clearError
  };
}