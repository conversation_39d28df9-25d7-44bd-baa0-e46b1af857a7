#!/usr/bin/env python3
"""
ADK Agent Structure Validation Test
Tests the new ADK multi-agent structure with proper imports and hierarchy.
"""

import sys
import os
from pathlib import Path

# Add the app-agents directory to the Python path
current_dir = Path(__file__).parent
app_agents_dir = current_dir / "app-agents"
agents_dir = current_dir / "app-agents" / "agents"

sys.path.insert(0, str(app_agents_dir))
sys.path.insert(0, str(agents_dir))

def test_adk_structure():
    """Test the ADK agent structure and imports."""
    print("=" * 60)
    print("ADK MULTI-AGENT STRUCTURE VALIDATION")  
    print("=" * 60)
    
    try:
        print("\n1. Testing individual agent imports...")
        
        # Test YouTube analyzer
        try:
            from agents.youtube_analyzer import root_agent as youtube_agent
            print(f"✅ YouTube Analyzer: {youtube_agent.name}")
            print(f"   Model: {youtube_agent.model}")
            print(f"   Tools: {len(youtube_agent.tools)}")
        except Exception as e:
            print(f"❌ YouTube Analyzer import failed: {e}")
        
        # Test Instagram analyzer  
        try:
            from agents.instagram_analyzer import root_agent as instagram_agent
            print(f"✅ Instagram Analyzer: {instagram_agent.name}")
            print(f"   Model: {instagram_agent.model}")
            print(f"   Tools: {len(instagram_agent.tools)}")
        except Exception as e:
            print(f"❌ Instagram Analyzer import failed: {e}")
            
        # Test Research agent
        try:
            from agents.research_agent import root_agent as research_agent
            print(f"✅ Research Agent: {research_agent.name}")
            print(f"   Model: {research_agent.model}")
            print(f"   Tools: {len(research_agent.tools)}")
        except Exception as e:
            print(f"❌ Research Agent import failed: {e}")
            
        # Test Content planner
        try:
            from agents.content_planner import root_agent as content_agent
            print(f"✅ Content Planner: {content_agent.name}")
            print(f"   Model: {content_agent.model}")
            print(f"   Tools: {len(content_agent.tools)}")
        except Exception as e:
            print(f"❌ Content Planner import failed: {e}")
            
        print("\n2. Testing coordinator agent with sub-agents...")
        
        # Test coordinator (this will import all sub-agents)
        try:
            from agents.social_media_coordinator import root_agent as coordinator
            print(f"✅ Coordinator Agent: {coordinator.name}")
            print(f"   Model: {coordinator.model}")
            print(f"   Sub-agents: {len(coordinator.sub_agents)}")
            print(f"   Tools: {len(coordinator.tools)}")
            
            # Verify sub-agents are properly linked
            print("\n   Sub-agent details:")
            for i, sub_agent in enumerate(coordinator.sub_agents):
                print(f"     {i+1}. {sub_agent.name} ({sub_agent.model})")
                
            # Verify AgentTools are created
            agent_tools = [tool for tool in coordinator.tools if hasattr(tool, 'agent')]
            print(f"\n   AgentTools: {len(agent_tools)} configured")
            for tool in agent_tools:
                if hasattr(tool, 'agent'):
                    print(f"     • {tool.name} → {tool.agent.name}")
                    
        except Exception as e:
            print(f"❌ Coordinator Agent import failed: {e}")
            import traceback
            traceback.print_exc()
            
        print("\n3. Testing ADK structure compliance...")
        
        # Check folder structure
        structure_checks = [
            ("agents/social_media_coordinator/__init__.py", "Coordinator init"),
            ("agents/social_media_coordinator/agent.py", "Coordinator agent"),
            ("agents/youtube_analyzer/__init__.py", "YouTube init"),
            ("agents/youtube_analyzer/agent.py", "YouTube agent"),
            ("agents/instagram_analyzer/__init__.py", "Instagram init"),
            ("agents/instagram_analyzer/agent.py", "Instagram agent"),
            ("agents/research_agent/__init__.py", "Research init"),
            ("agents/research_agent/agent.py", "Research agent"),
            ("agents/content_planner/__init__.py", "Content planner init"),
            ("agents/content_planner/agent.py", "Content planner agent")
        ]
        
        for file_path, description in structure_checks:
            full_path = agents_dir / file_path
            if full_path.exists():
                print(f"✅ {description}: {file_path}")
            else:
                print(f"❌ {description}: MISSING {file_path}")
                
        print("\n4. Testing 'adk web' compatibility...")
        print("📝 Structure follows ADK patterns:")
        print("   • Each agent in separate folder")
        print("   • Each folder has __init__.py with root_agent")
        print("   • Each folder has agent.py with LlmAgent")
        print("   • Proper sub_agents hierarchy implemented")
        print("   • AgentTool integration configured")
        
        print("\n" + "=" * 60)
        print("✅ ADK MULTI-AGENT STRUCTURE VALIDATION COMPLETED")
        print("=" * 60)
        
        print("\n📋 NEXT STEPS:")
        print("1. Navigate to app-agents directory")
        print("2. Run: adk web agents/")
        print("3. Verify agents are discovered and loadable")
        print("4. Test agent interactions and delegation")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Overall validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_adk_structure()
    sys.exit(0 if success else 1)