"use client";

import { useState, useEffect, useCallback } from 'react';
import { useQuery } from "@tanstack/react-query";
import { ADKAgentInfo, ChatError, UseAgentListReturn } from '@/types/adk';

export interface UseAgentListOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onError?: (error: ChatError) => void;
}

export function useAgentList(options: UseAgentListOptions = {}): UseAgentListReturn {
  const {
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    onError
  } = options;

  const [lastError, setLastError] = useState<ChatError | null>(null);

  // Fetch available agents from ADK server
  const fetchAgents = useCallback(async (): Promise<ADKAgentInfo[]> => {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
    const authToken = localStorage.getItem('access_token') || 'dev-token';

    try {
      const response = await fetch(`${baseUrl}/chat/agents`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform backend response to ADKAgentInfo format
      if (Array.isArray(data)) {
        return data.map((agent: any): ADKAgentInfo => ({
          name: agent.name || agent.app_name || agent,
          description: agent.description || `${agent.name || agent} agent`,
          available: agent.available !== false, // Default to true if not specified
          capabilities: agent.capabilities || [],
          tools: agent.tools || []
        }));
      } else if (data.agents && Array.isArray(data.agents)) {
        return data.agents.map((agent: any): ADKAgentInfo => ({
          name: agent.name || agent.app_name || agent,
          description: agent.description || `${agent.name || agent} agent`,
          available: agent.available !== false,
          capabilities: agent.capabilities || [],
          tools: agent.tools || []
        }));
      } else {
        // Fallback: assume it's a simple array of strings
        const agentNames = data.available_agents || data || [];
        return agentNames.map((name: string): ADKAgentInfo => ({
          name,
          description: `${name} agent`,
          available: true,
          capabilities: [],
          tools: []
        }));
      }
    } catch (error) {
      const chatError: ChatError = {
        error: error instanceof Error ? error.message : 'Failed to fetch agents',
        error_code: 'AGENT_LIST_ERROR'
      };
      
      setLastError(chatError);
      onError?.(chatError);
      
      // Return default agents as fallback
      return [
        {
          name: 'content_planner',
          description: 'Content planning and strategy agent',
          available: true,
          capabilities: ['content_planning', 'social_media_strategy'],
          tools: ['google_search', 'social_media_apis']
        },
        {
          name: 'research_agent',
          description: 'Research and analysis agent',
          available: true,
          capabilities: ['research', 'data_analysis'],
          tools: ['google_search', 'web_scraping']
        }
      ];
    }
  }, [onError]);

  // Use React Query for caching and automatic refetching
  const query = useQuery({
    queryKey: ['agents', 'list'],
    queryFn: fetchAgents,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchInterval: autoRefresh ? refreshInterval : false,
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Clear error when query succeeds
  useEffect(() => {
    if (query.isSuccess && lastError) {
      setLastError(null);
    }
  }, [query.isSuccess, lastError]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    setLastError(null);
    await query.refetch();
  }, [query]);

  return {
    agents: query.data || [],
    isLoading: query.isLoading,
    error: lastError || (query.error ? {
      error: query.error instanceof Error ? query.error.message : 'Unknown error',
      error_code: 'QUERY_ERROR'
    } : null),
    refresh
  };
}