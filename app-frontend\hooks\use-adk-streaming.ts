"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { StreamingChunk, ChatError, EnhancedChatMessage } from "@/types/adk";

export interface UseADKStreamingOptions {
  userId: string;
  sessionId?: string;
  agentName?: string;
  onChunk?: (chunk: StreamingChunk) => void;
  onComplete?: (messageId: string) => void;
  onError?: (error: ChatError) => void;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
}

export interface UseADKStreamingReturn {
  isConnected: boolean;
  isStreaming: boolean;
  isReconnecting: boolean;
  currentChunk: StreamingChunk | null;
  error: ChatError | null;
  connectionAttempts: number;
  startStreaming: (messageId: string, message: string) => Promise<void>;
  stopStreaming: () => void;
  interruptStreaming: () => void;
  reconnect: () => Promise<void>;
  clearError: () => void;
}

export function useADKStreaming(
  options: UseADKStreamingOptions
): UseADKStreamingReturn {
  const {
    userId,
    sessionId,
    agentName = "content_planner",
    onChunk,
    onComplete,
    onError,
    autoReconnect = true,
    maxReconnectAttempts = 3,
    reconnectDelay = 2000,
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [currentChunk, setCurrentChunk] = useState<StreamingChunk | null>(null);
  const [error, setError] = useState<ChatError | null>(null);
  const [connectionAttempts, setConnectionAttempts] = useState(0);

  const eventSourceRef = useRef<EventSource | null>(null);
  const currentMessageIdRef = useRef<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManualDisconnectRef = useRef(false);

  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    setIsConnected(false);
    setIsStreaming(false);
    setIsReconnecting(false);
    setCurrentChunk(null);
  }, []);

  const handleError = useCallback(
    (errorData: ChatError) => {
      console.error("ADK Streaming Error:", errorData);
      setError(errorData);
      setIsStreaming(false);
      onError?.(errorData);

      if (
        autoReconnect &&
        connectionAttempts < maxReconnectAttempts &&
        !isManualDisconnectRef.current
      ) {
        setIsReconnecting(true);
        reconnectTimeoutRef.current = setTimeout(() => {
          reconnect();
        }, reconnectDelay);
      }
    },
    [
      autoReconnect,
      connectionAttempts,
      maxReconnectAttempts,
      reconnectDelay,
      onError,
    ]
  );

  const startStreaming = useCallback(
    async (messageId: string, message: string) => {
      if (!userId || !sessionId) {
        const error: ChatError = {
          error: "Missing required parameters",
          error_code: "MISSING_PARAMS",
          details: { userId, sessionId },
        };
        handleError(error);
        return;
      }

      try {
        isManualDisconnectRef.current = false;
        currentMessageIdRef.current = messageId;
        setError(null);
        setIsStreaming(true);

        // Create EventSource URL for ADK streaming
        const baseUrl =
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api";
        const streamUrl = new URL(`${baseUrl}/chat/send-message-stream`);

        // Add query parameters
        streamUrl.searchParams.set("user_id", userId);
        streamUrl.searchParams.set("session_id", sessionId);
        streamUrl.searchParams.set("agent_name", agentName);
        streamUrl.searchParams.set("message_id", messageId);

        // Create EventSource connection
        const eventSource = new EventSource(streamUrl.toString(), {
          withCredentials: false,
        });

        eventSourceRef.current = eventSource;

        // Send the message via POST to initiate streaming
        const authToken = localStorage.getItem("access_token") || "dev-token";
        const response = await fetch(`${baseUrl}/chat/send-message-stream`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${authToken}`,
          },
          body: JSON.stringify({
            message,
            session_id: sessionId,
            user_id: userId,
            agent_name: agentName,
            message_id: messageId,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Set up EventSource event handlers
        eventSource.onopen = () => {
          console.log("ADK EventSource connected");
          setIsConnected(true);
          setIsReconnecting(false);
          setConnectionAttempts(0);
        };

        eventSource.onmessage = (event) => {
          try {
            const chunk: StreamingChunk = JSON.parse(event.data);
            setCurrentChunk(chunk);
            onChunk?.(chunk);

            if (chunk.done) {
              setIsStreaming(false);
              onComplete?.(messageId);
              cleanup();
            }
          } catch (parseError) {
            console.warn("Failed to parse streaming chunk:", event.data);
          }
        };

        eventSource.onerror = (event) => {
          console.error("EventSource error:", event);

          const errorData: ChatError = {
            error: "Streaming connection failed",
            error_code: "STREAM_ERROR",
            message_id: messageId,
            session_id: sessionId,
            details: { readyState: eventSource.readyState },
          };

          // Check if this is a connection error vs. a stream completion
          if (eventSource.readyState === EventSource.CLOSED) {
            if (!isManualDisconnectRef.current) {
              handleError(errorData);
            }
          } else {
            handleError(errorData);
          }
        };

        // Handle custom events
        eventSource.addEventListener("error", (event: any) => {
          try {
            const errorData = JSON.parse(event.data);
            handleError(errorData);
          } catch {
            handleError({
              error: "Unknown streaming error",
              error_code: "UNKNOWN_ERROR",
              message_id: messageId,
            });
          }
        });

        eventSource.addEventListener("interrupt", () => {
          setIsStreaming(false);
          onComplete?.(messageId);
          cleanup();
        });
      } catch (error) {
        console.error("Failed to start streaming:", error);
        const errorData: ChatError = {
          error:
            error instanceof Error
              ? error.message
              : "Failed to start streaming",
          error_code: "STREAM_START_ERROR",
          message_id: messageId,
          session_id: sessionId,
        };
        handleError(errorData);
      }
    },
    [userId, sessionId, agentName, onChunk, onComplete, handleError, cleanup]
  );

  const stopStreaming = useCallback(() => {
    isManualDisconnectRef.current = true;
    cleanup();
  }, [cleanup]);

  const interruptStreaming = useCallback(async () => {
    if (!currentMessageIdRef.current || !sessionId) return;

    try {
      const baseUrl =
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api";
      const authToken = localStorage.getItem("access_token") || "dev-token";

      await fetch(`${baseUrl}/chat/interrupt`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          session_id: sessionId,
          message_id: currentMessageIdRef.current,
        }),
      });

      stopStreaming();
    } catch (error) {
      console.error("Failed to interrupt streaming:", error);
    }
  }, [sessionId, stopStreaming]);

  const reconnect = useCallback(async () => {
    if (connectionAttempts >= maxReconnectAttempts) {
      setIsReconnecting(false);
      return;
    }

    setConnectionAttempts((prev) => prev + 1);
    setIsReconnecting(true);
    setError(null);

    // If we have a current message, try to restart streaming
    if (currentMessageIdRef.current) {
      // Note: In a real implementation, we'd need to store the original message
      // For now, we'll just attempt to reconnect the EventSource
      try {
        cleanup();
        // The actual reconnection would happen when startStreaming is called again
        setIsReconnecting(false);
      } catch (error) {
        console.error("Reconnection failed:", error);
        setIsReconnecting(false);
      }
    }
  }, [connectionAttempts, maxReconnectAttempts, cleanup]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isManualDisconnectRef.current = true;
      cleanup();
    };
  }, [cleanup]);

  return {
    isConnected,
    isStreaming,
    isReconnecting,
    currentChunk,
    error,
    connectionAttempts,
    startStreaming,
    stopStreaming,
    interruptStreaming,
    reconnect,
    clearError,
  };
}
