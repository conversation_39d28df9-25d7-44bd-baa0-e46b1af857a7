"""
Feature Flags for ADK Integration

Simple feature flag implementation for ADK integration.
Requirements covered: 6.1, 6.2, 6.3
"""

import os
import logging
from typing import Dict, Any, Optional, List
from functools import lru_cache

logger = logging.getLogger(__name__)


class FeatureFlagManager:
    """Simple feature flag manager for ADK integration"""
    
    def __init__(self):
        """Initialize with default flags"""
        self._flags = {
            "adk_enabled": True,
            "adk_streaming_enabled": True,
            "debug_mode_enabled": False,
            "mock_mode_enabled": False,
            "rollout_percentage": 100,
            "disabled_agents": [],
            "beta_agents": [],
            "beta_user_ids": []
        }
        self._load_from_environment()
    
    def _load_from_environment(self):
        """Load flags from environment variables"""
        env_mappings = {
            "ADK_ENABLED": ("adk_enabled", bool),
            "ADK_STREAMING_ENABLED": ("adk_streaming_enabled", bool),
            "ADK_DEBUG_MODE": ("debug_mode_enabled", bool),
            "ADK_ROLLOUT_PERCENTAGE": ("rollout_percentage", int),
        }
        
        for env_var, (flag_name, flag_type) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    if flag_type == bool:
                        value = env_value.lower() in ("true", "1", "yes", "on")
                    elif flag_type == int:
                        value = int(env_value)
                    else:
                        value = env_value
                    
                    self._flags[flag_name] = value
                    logger.info(f"Loaded feature flag from environment: {flag_name} = {value}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid environment value for {env_var}: {env_value} - {e}")
    
    def is_enabled(self, flag_name: str, user_id: Optional[str] = None) -> bool:
        """Check if a feature flag is enabled"""
        if flag_name not in self._flags:
            logger.warning(f"Unknown feature flag: {flag_name}")
            return False
        
        flag_value = self._flags[flag_name]
        if isinstance(flag_value, bool):
            return flag_value and self._check_rollout(user_id)
        return bool(flag_value)
    
    def is_agent_enabled(self, agent_name: str, user_id: Optional[str] = None) -> bool:
        """Check if a specific agent is enabled"""
        if agent_name in self._flags.get("disabled_agents", []):
            return False
        
        if agent_name in self._flags.get("beta_agents", []):
            return user_id in self._flags.get("beta_user_ids", [])
        
        return self.is_enabled("adk_enabled", user_id)
    
    def is_beta_user(self, user_id: Optional[str]) -> bool:
        """Check if user is a beta user"""
        if not user_id:
            return False
        return user_id in self._flags.get("beta_user_ids", [])
    
    def _check_rollout(self, user_id: Optional[str]) -> bool:
        """Check if user is included in rollout percentage"""
        rollout = self._flags.get("rollout_percentage", 100)
        if rollout >= 100:
            return True
        if rollout <= 0:
            return False
        
        if user_id:
            import hashlib
            hash_value = int(hashlib.md5(user_id.encode()).hexdigest(), 16)
            return (hash_value % 100) < rollout
        
        return True
    
    def get_flag_value(self, flag_name: str, default: Any = None) -> Any:
        """Get raw flag value"""
        return self._flags.get(flag_name, default)
    
    def set_flag(self, flag_name: str, value: Any) -> bool:
        """Set feature flag value at runtime"""
        self._flags[flag_name] = value
        logger.info(f"Updated feature flag: {flag_name} = {value}")
        return True
    
    def get_all_flags(self) -> Dict[str, Any]:
        """Get all feature flags as dictionary"""
        return self._flags.copy()


# Global instance
_feature_flag_manager: Optional[FeatureFlagManager] = None


@lru_cache(maxsize=1)
def get_feature_flag_manager() -> FeatureFlagManager:
    """Get or create global feature flag manager instance"""
    global _feature_flag_manager
    
    if _feature_flag_manager is None:
        _feature_flag_manager = FeatureFlagManager()
        logger.info("Initialized global feature flag manager")
    
    return _feature_flag_manager


# Convenience functions
def is_adk_enabled(user_id: Optional[str] = None) -> bool:
    """Check if ADK integration is enabled"""
    return get_feature_flag_manager().is_enabled("adk_enabled", user_id)


def is_streaming_enabled(user_id: Optional[str] = None) -> bool:
    """Check if ADK streaming is enabled"""
    manager = get_feature_flag_manager()
    return (manager.is_enabled("adk_enabled", user_id) and 
            manager.is_enabled("adk_streaming_enabled", user_id))


def is_agent_enabled(agent_name: str, user_id: Optional[str] = None) -> bool:
    """Check if specific agent is enabled"""
    return get_feature_flag_manager().is_agent_enabled(agent_name, user_id)


def is_debug_mode_enabled() -> bool:
    """Check if debug mode is enabled"""
    return get_feature_flag_manager().is_enabled("debug_mode_enabled")


def is_mock_mode_enabled() -> bool:
    """Check if mock mode is enabled"""
    return get_feature_flag_manager().is_enabled("mock_mode_enabled")


def get_streaming_timeout() -> int:
    """Get streaming timeout value"""
    return get_feature_flag_manager().get_flag_value("adk_streaming_timeout", 300)


def get_rollout_percentage() -> int:
    """Get rollout percentage"""
    return get_feature_flag_manager().get_flag_value("rollout_percentage", 100)


# Initialize on import
try:
    get_feature_flag_manager()
    logger.info("Feature flags initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize feature flags: {e}")


def get_feature_flags(user_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Backwards-compatible helper that returns the active feature flags for a user.

    Many modules expect a `get_feature_flags` function. This returns a snapshot
    of all flags with user-specific evaluations applied where relevant.
    """
    manager = get_feature_flag_manager()
    flags = manager.get_all_flags()
    # Enrich with evaluated boolean values for common queries
    flags_snapshot: Dict[str, Any] = {
        "adk_enabled": manager.is_enabled("adk_enabled", user_id),
        "streaming_enabled": manager.is_enabled("adk_streaming_enabled", user_id),
        "debug_mode": manager.is_enabled("debug_mode_enabled"),
        "rollout_percentage": manager.get_flag_value("rollout_percentage", 100),
        "disabled_agents": manager.get_flag_value("disabled_agents", []),
        "beta_agents": manager.get_flag_value("beta_agents", []),
        "is_beta_user": manager.is_beta_user(user_id)
    }
    # Merge raw flags under 'raw' key for full visibility
    flags_snapshot["raw"] = flags
    return flags_snapshot
