#!/usr/bin/env python3
"""
Quick ADK Integration Status Check
Run this to verify all ADK components are working correctly.
"""
import asyncio
import os
import sys

def check_adk_installation():
    """Check if ADK is properly installed"""
    try:
        import google.adk
        print("✅ Google ADK package installed successfully")
        return True
    except ImportError as e:
        print(f"❌ ADK installation issue: {e}")
        return False

def check_environment_config():
    """Check environment configuration"""
    print("\n🔧 Environment Configuration:")
    
    # Check API key
    api_key = os.getenv('GOOGLE_API_KEY')
    if api_key:
        print(f"✅ GOOGLE_API_KEY: {'*' * min(len(api_key), 20)}...")
    else:
        print("⚠️  GOOGLE_API_KEY: Not set (required for ADK)")
    
    # Check Vertex AI setting
    use_vertex = os.getenv('GOOGLE_GENAI_USE_VERTEXAI', 'FALSE')
    print(f"📋 GOOGLE_GENAI_USE_VERTEXAI: {use_vertex}")
    
    return bool(api_key)

async def check_adk_services():
    """Check ADK service initialization"""
    print("\n🛠️  ADK Services Status:")
    
    try:
        from app.services.adk_config_service import get_adk_config_service
        config_service = get_adk_config_service()
        
        # Validate configuration
        status = config_service.validate_configuration()
        print(f"✅ ADK Config Service: Ready")
        print(f"   - ADK Available: {status['adk_available']}")
        print(f"   - Config Loaded: {status['config_loaded']}")
        print(f"   - Authentication: {status['authentication']}")
        
        if status['issues']:
            print("⚠️  Issues found:")
            for issue in status['issues'][:3]:  # Show first 3 issues
                print(f"   - {issue}")
        
        return status['adk_ready']
        
    except Exception as e:
        print(f"❌ ADK Config Service error: {e}")
        return False

async def test_adk_agent():
    """Test ADK agent functionality"""
    print("\n🤖 ADK Agent Test:")
    
    try:
        from app.agents.adk_research_agent import ADKResearchAgent
        
        # Create agent instance
        agent = ADKResearchAgent()
        print("✅ ADK Research Agent created successfully")
        
        # Test basic functionality
        result = await agent.analyze(
            user_id="test_user",
            query="social media trends test",
            platforms=["youtube"]
        )
        
        analysis_method = result.get('analysis_method', 'unknown')
        print(f"✅ Agent analysis completed using: {analysis_method}")
        
        if 'adk' in analysis_method:
            print("🚀 ADK intelligence is active!")
        else:
            print("📊 Using fallback mode (ADK may not be fully configured)")
            
        return True
        
    except Exception as e:
        print(f"❌ ADK Agent test failed: {e}")
        return False

async def main():
    """Run comprehensive ADK status check"""
    print("🔍 ADK Integration Status Check")
    print("=" * 50)
    
    # Check installation
    adk_installed = check_adk_installation()
    
    # Check environment
    env_configured = check_environment_config()
    
    # Check services
    services_ready = await check_adk_services() if adk_installed else False
    
    # Test agent
    agent_working = await test_adk_agent() if adk_installed else False
    
    # Summary
    print("\n📊 Status Summary:")
    print("=" * 50)
    print(f"ADK Package: {'✅' if adk_installed else '❌'}")
    print(f"Environment: {'✅' if env_configured else '⚠️'}")
    print(f"Services: {'✅' if services_ready else '❌'}")
    print(f"Agent Test: {'✅' if agent_working else '❌'}")
    
    if all([adk_installed, env_configured, services_ready, agent_working]):
        print("\n🎉 ADK Integration is fully operational!")
        print("   Ready to use ADK-powered social media agents.")
    elif adk_installed and not env_configured:
        print("\n⚠️  ADK is installed but needs API key configuration.")
        print("   Add your GOOGLE_API_KEY to the .env file.")
    else:
        print("\n⚠️  ADK integration needs attention.")
        print("   Check the issues above and refer to ADK_QUICKSTART.md")

if __name__ == "__main__":
    # Change to the correct directory
    import os
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Add the app directory to Python path
    sys.path.insert(0, os.path.join(script_dir, '..'))
    
    asyncio.run(main())